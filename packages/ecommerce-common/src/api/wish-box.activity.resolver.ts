import {Args, Mutation, Parent, Query, ResolveField, Resolver} from '@nestjs/graphql';
import {Allow, Ctx, ID, ListQueryOptions, Permission, RelationPaths, Relations, RequestContext} from '@vendure/core';
import {WishBoxService} from '../service';
import {WishBoxActivity, WishBoxActivityBuy, WishBoxActivityPrize, WishBoxActivityRecord} from '../entities';
import _ from 'lodash';
import {WeChatPaymentType} from '../generated-shop-types';

@Resolver('WishBoxActivityResolver')
export class WishBoxActivityResolver {
  constructor(private wishBoxService: WishBoxService) {}

  @Query()
  @Allow(Permission.Public)
  async wishBoxActivity(
    @Ctx() ctx: RequestContext,
    @Args('wishBoxActivityId') wishBoxActivityId: ID,
    @Args('options') options: ListQueryOptions<WishBoxActivity>,
    @Relations({entity: WishBoxActivity}) relations: RelationPaths<WishBoxActivity>,
  ) {
    return this.wishBoxService.findOne(ctx, wishBoxActivityId, options, relations);
  }

  @Query()
  @Allow(Permission.Owner)
  async ********************************(
    @Ctx() ctx: RequestContext,
    @Args('wishBoxActivityId') wishBoxActivityId: ID,
  ): Promise<boolean> {
    return this.wishBoxService.********************************(ctx, wishBoxActivityId);
  }

  @Mutation()
  @Allow(Permission.Owner)
  async wishBoxActivityFreeOpen(@Ctx() ctx: RequestContext, @Args('wishBoxActivityId') wishBoxActivityId: ID) {
    return this.wishBoxService.wishBoxActivityFreeOpen(ctx, wishBoxActivityId);
  }

  @Mutation()
  @Allow(Permission.Owner)
  async wishBoxActivityWishProductPick(
    @Ctx() ctx: RequestContext,
    @Args('wishBoxActivityId') wishBoxActivityId: ID,
    @Args('wishBoxActivityPrizeIdList') wishBoxActivityPrizeIdList: ID[],
  ) {
    return this.wishBoxService.wishBoxActivityWishProductPick(ctx, wishBoxActivityId, wishBoxActivityPrizeIdList);
  }

  @Query()
  @Allow(Permission.Owner)
  async wishBoxActivityWishProductFind(
    @Ctx() ctx: RequestContext,
    @Args('wishBoxActivityId') wishBoxActivityId: ID,
    @Args('options') options: ListQueryOptions<WishBoxActivityPrize>,
    @Relations({entity: WishBoxActivityPrize}) relations: RelationPaths<WishBoxActivityPrize>,
  ) {
    return this.wishBoxService.wishBoxActivityWishProductFind(ctx, wishBoxActivityId, options, relations);
  }

  @Query()
  @Allow(Permission.Owner)
  async wishBoxActivityRecords(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ListQueryOptions<WishBoxActivityRecord>,
    @Relations({entity: WishBoxActivityRecord}) relations: RelationPaths<WishBoxActivityRecord>,
  ) {
    return this.wishBoxService.wishBoxActivityRecords(ctx, options, relations, true);
  }

  @Mutation()
  @Allow(Permission.Owner)
  async wishBoxActivityBooking(@Ctx() ctx: RequestContext, @Args('wishBoxActivityId') wishBoxActivityId: ID) {
    return this.wishBoxService.wishBoxActivityBooking(ctx, wishBoxActivityId);
  }

  @Query()
  @Allow(Permission.Owner)
  async wishBoxActivityBookingCheck(
    @Ctx() ctx: RequestContext,
    @Args('wishBoxActivityId') wishBoxActivityId: ID,
  ): Promise<boolean> {
    return this.wishBoxService.wishBoxActivityBooking(ctx, wishBoxActivityId, true);
  }

  @Query()
  @Allow(Permission.Owner)
  async wishBoxActivityBuyCheck(
    @Ctx() ctx: RequestContext,
    @Args('wishBoxActivityId') wishBoxActivityId: ID,
  ): Promise<number> {
    return this.wishBoxService.wishBoxActivityBuyCheck(ctx, wishBoxActivityId);
  }

  @Mutation()
  @Allow(Permission.Owner)
  async wishBoxActivityBuy(
    @Ctx() ctx: RequestContext,
    @Args('wishBoxActivityId') wishBoxActivityId: ID,
    @Args('activityOpenStrategyId') activityOpenStrategyId: ID,
    @Args('paymentType') paymentType: WeChatPaymentType = WeChatPaymentType.WechatProgram,
  ) {
    return this.wishBoxService.wishBoxActivityBuy(ctx, wishBoxActivityId, activityOpenStrategyId, paymentType);
  }

  @Query()
  @Allow(Permission.Owner)
  async getWishBoxActivityBuyById(
    @Ctx() ctx: RequestContext,
    @Args('wishBoxActivityBuyId') wishBoxActivityBuyId: ID,
    @Args('options') options: ListQueryOptions<WishBoxActivityBuy>,
    @Relations({entity: WishBoxActivityBuy}) relations: RelationPaths<WishBoxActivityBuy>,
  ) {
    return this.wishBoxService.getWishBoxActivityBuy(ctx, options, relations, wishBoxActivityBuyId);
  }

  @Query()
  @Allow(Permission.Owner)
  async getWishBoxActivityBuyByCode(
    @Ctx() ctx: RequestContext,
    @Args('code') code: string,
    @Args('options') options: ListQueryOptions<WishBoxActivityBuy>,
    @Relations({entity: WishBoxActivityBuy}) relations: RelationPaths<WishBoxActivityBuy>,
  ) {
    return this.wishBoxService.getWishBoxActivityBuy(ctx, options, relations, undefined, code);
  }

  @Query()
  @Allow(Permission.Owner)
  async getWishBoxActivityBuys(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ListQueryOptions<WishBoxActivityBuy>,
    @Relations({entity: WishBoxActivityBuy}) relations: RelationPaths<WishBoxActivityBuy>,
  ) {
    return this.wishBoxService.getWishBoxActivityBuys(ctx, options, relations, true);
  }

  @Mutation()
  @Allow(Permission.Owner)
  async wishBoxActivityRecordPickUp(
    @Ctx() ctx: RequestContext,
    @Args('wishBoxActivityRecordIds') wishBoxActivityRecordIds: ID[],
  ) {
    return this.wishBoxService.wishBoxActivityRecordPickUp(ctx, wishBoxActivityRecordIds);
  }
}

@Resolver('WishBoxActivity')
export class WishBoxActivityEntityResolver {
  @ResolveField('wishBoxActivityOpenStrategies')
  async wishBoxActivityOpenStrategies(@Ctx() ctx: RequestContext, @Parent() activity: WishBoxActivity) {
    const items = activity.wishBoxActivityOpenStrategies || [];
    return items.map(item => _.omit(item, ['wishProbability', 'baseProbability']));
  }

  @ResolveField('wishBoxActivityPrizes')
  async wishBoxActivityPrizes(@Ctx() ctx: RequestContext, @Parent() activity: WishBoxActivity) {
    const items = activity.wishBoxActivityPrizes || [];
    return items.map(item => _.omit(item, ['probability']));
  }
}
