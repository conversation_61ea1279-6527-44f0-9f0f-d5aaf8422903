import {Args, Mutation, Query, Resolver} from '@nestjs/graphql';
import {Allow, Ctx, ID, ListQueryOptions, RelationPaths, Relations, RequestContext, Transaction} from '@vendure/core';
import {WishBoxActivityOperate} from '../permission-definition';
import {WishBoxService} from '../service';
import {WishBoxActivityInput} from '../generated-admin-types';
import {WishBoxActivity, WishBoxActivityBuy} from '../entities';

@Resolver('WishBoxActivityAdminResolver')
export class WishBoxActivityAdminResolver {
  constructor(private wishBoxService: WishBoxService) {}

  @Transaction()
  @Mutation()
  @Allow(WishBoxActivityOperate.Create, WishBoxActivityOperate.Update)
  async upsertWishBoxActivity(@Ctx() ctx: RequestContext, @Args('input') input: WishBoxActivityInput) {
    return this.wishBoxService.upsertWishBoxActivity(ctx, input);
  }

  @Query()
  @Allow(WishBoxActivityOperate.Read)
  async wishBoxActivities(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ListQueryOptions<WishBoxActivity>,
    @Relations({entity: WishBoxActivity}) relations: RelationPaths<WishBoxActivity>,
  ) {
    return this.wishBoxService.findAll(ctx, options, relations);
  }

  @Query()
  @Allow(WishBoxActivityOperate.Read)
  async wishBoxActivity(
    @Ctx() ctx: RequestContext,
    @Args('wishBoxActivityId') wishBoxActivityId: ID,
    @Args('options') options: ListQueryOptions<WishBoxActivity>,
    @Relations({entity: WishBoxActivity}) relations: RelationPaths<WishBoxActivity>,
  ) {
    return this.wishBoxService.findOne(ctx, wishBoxActivityId, options, relations);
  }

  @Query()
  @Allow(WishBoxActivityOperate.Read)
  async wishBoxActivityBuys(
    @Ctx() ctx: RequestContext,
    @Args('recordCode') recordCode: string,
    @Args('options') options: ListQueryOptions<WishBoxActivityBuy>,
    @Relations({entity: WishBoxActivityBuy}) relations: RelationPaths<WishBoxActivityBuy>,
  ) {
    return this.wishBoxService.getWishBoxActivityBuys(ctx, options, relations, undefined, recordCode);
  }

  @Query()
  @Allow(WishBoxActivityOperate.Read)
  async wishBoxActivityBuy(
    @Ctx() ctx: RequestContext,
    @Args('wishBoxActivityBuyId') wishBoxActivityBuyId: ID,
    @Args('options') options: ListQueryOptions<WishBoxActivityBuy>,
    @Relations({entity: WishBoxActivityBuy}) relations: RelationPaths<WishBoxActivityBuy>,
  ) {
    return this.wishBoxService.wishBoxActivityBuyById(ctx, options, relations, wishBoxActivityBuyId);
  }

  @Transaction()
  @Mutation()
  @Allow(WishBoxActivityOperate.Update)
  async refundWishBoxActivityBuy(
    @Ctx() ctx: RequestContext,
    @Args('wishBoxActivityBuyId') wishBoxActivityBuyId: ID,
    @Args('refundReason') refundReason = '',
  ) {
    return this.wishBoxService.refundWishBoxActivityBuy(ctx, wishBoxActivityBuyId, refundReason);
  }
}
