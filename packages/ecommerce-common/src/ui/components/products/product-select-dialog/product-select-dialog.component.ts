import {Component, OnInit, ChangeDetectionStrategy, Input} from '@angular/core';
import {FormGroup, FormBuilder} from '@angular/forms';

import {Dialog, DataService} from '@vendure/admin-ui/core';

import {BehaviorSubject, Observable} from 'rxjs';

import {Product, ProductList, SortOrder} from '../../../generated-admin-types';

import {PRODUCTS} from '../../product-list/graphql';
import {getProductStock} from '../../../utils/stock';

type ProductWithChecked = Product & {checked?: boolean; disableCheck?: boolean; disableCheckMessage?: string};

@Component({
  selector: 'app-product-select-dialog',
  templateUrl: './product-select-dialog.component.html',
  styleUrls: ['./product-select-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProductSelectDialogComponent implements Dialog, OnInit {
  @Input() selectedIds: string[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  @Input() queryFilter: any = {};
  @Input() disableVirtualTypes: string[] = [];
  @Input() singleSelect = false;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  @Input() disableCheckFn: ((p: Product) => {disabled: boolean; options: any}) | undefined = undefined;

  ids: string[];
  id: string;
  form: FormGroup;

  itemsBhSubj = new BehaviorSubject<Array<ProductWithChecked>>([]);
  totalBhSubj = new BehaviorSubject<number>(0);
  curPageBhSubj = new BehaviorSubject<number>(1);
  itemPerPageBhSubj = new BehaviorSubject<number>(10);

  items$: Observable<Array<ProductWithChecked>>;
  totalItems$: Observable<number>;
  itemsPerPage$: Observable<number>;
  currentPage$: Observable<number>;

  getStock = getProductStock;

  constructor(private dataService: DataService, private fb: FormBuilder) {
    this.form = this.fb.group({
      name: null,
      collectionId: null,
    });
  }
  resolveWith: (result?: string | string[] | Product[]) => void;

  ngOnInit() {
    this.ids = this.selectedIds?.length ? [...this.selectedIds] : [];
    this.id = this.ids[0];
    this.items$ = this.itemsBhSubj.pipe(res => res);
    this.totalItems$ = this.totalBhSubj.pipe(res => res);
    this.currentPage$ = this.curPageBhSubj.pipe(res => res);
    this.itemsPerPage$ = this.itemPerPageBhSubj.pipe(res => res);

    this.pageChange(1);
  }

  queryProducts() {
    const {name, collectionId} = this.form.controls;
    this.dataService
      .query<{newProducts: ProductList}>(
        PRODUCTS,
        {
          collectionId: collectionId.value ? collectionId.value : undefined,
          state: 'selling',
          options: {
            skip: (this.curPageBhSubj.value - 1) * this.itemPerPageBhSubj.value,
            take: this.itemPerPageBhSubj.value,
            sort: {
              createdAt: SortOrder.Desc,
            },
            filter: {
              name: name.value
                ? {
                    contains: name.value,
                  }
                : undefined,
              freeGift: {
                eq: false,
              },
              ...this.queryFilter,
            },
          },
        },
        'no-cache',
      )
      .mapStream(res => res.newProducts)
      .subscribe(res => {
        this.itemsBhSubj.next(
          res.items.map((i: Product) => {
            const virtualTypeCheck = this.getIsDisableCheckByVirtualType(i);
            const fnCheck = this.disableCheckFn?.(i);
            return {
              ...i,
              checked: this.ids.includes(i.id),
              disableCheck: virtualTypeCheck || fnCheck?.disabled,
              disableCheckMessage: this.mergeDisableCheckMsg(i, virtualTypeCheck, fnCheck),
            } as ProductWithChecked;
          }),
        );
        this.totalBhSubj.next(res.totalItems);
      });
  }

  getIsDisableCheckByVirtualType(p: Product) {
    if (this.disableVirtualTypes?.length) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const cf = p.customFields as any;
      const type = cf.virtualTargetType;
      if (type) {
        return this.disableVirtualTypes.includes(type);
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  mergeDisableCheckMsg(product: Product, vtCheck: Boolean, fnCheck?: {disabled: boolean; options: any}) {
    if (!vtCheck && !fnCheck?.disabled) {
      return '';
    }
    let vtMsg = '';
    let fnMsg = '';
    if (vtCheck) {
      if (product.customFields?.virtualTargetType === 'coupon') {
        vtMsg = '付费优惠券不可选';
      } else if (product.customFields?.virtualTargetType === 'memberCard') {
        vtMsg = '会员权益卡不可选';
      }
    }
    if (fnCheck?.disabled) {
      fnMsg = fnCheck.options?.message;
    }
    const res = [vtMsg, fnMsg].filter(Boolean).join('；');
    return res;
  }

  handleCheckProduct(prod: ProductWithChecked, checked: boolean) {
    const findIdxRes = this.ids.findIndex(i => i === prod.id);
    if (prod.checked) {
      if (findIdxRes !== -1) {
        this.ids.splice(findIdxRes, 1);
      }
    } else {
      if (findIdxRes === -1) {
        this.ids.push(prod.id);
      }
    }
  }

  pageChange(page: number) {
    this.curPageBhSubj.next(page);
    this.queryProducts();
  }

  perPageChange(perPage: number) {
    this.itemPerPageBhSubj.next(perPage);
    this.pageChange(1);
  }

  reset() {
    this.form.reset();
    // this.pageChange(1);
  }

  save() {
    if (this.singleSelect) {
      this.resolveWith([this.id]);
    } else {
      this.resolveWith(this.ids);
    }
  }
}
