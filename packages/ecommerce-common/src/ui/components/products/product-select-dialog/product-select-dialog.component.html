<ng-template vdrDialogTitle> 选择可用商品 </ng-template>

<div>
  <div class="form" [formGroup]="form">
    <div class="flex justify-between">
      <div class="flex left-content">
        <label-value label="商品名称搜索">
          <input type="text" formControlName="name" (keydown.enter)="pageChange(1)" />
        </label-value>
        <label-value label="商品分组搜索">
          <product-collection-select
            append="body"
            formControlName="collectionId"
            style="min-width: 175px; height: 36px; z-index: 999"
            (ngModelChange)="pageChange(1)"
          ></product-collection-select>
        </label-value>
      </div>
      <!-- <label-value label="商品分组">
        <date-range formControlName="collectionId" (change)="pageChange(1)"></date-range>
      </label-value> -->
      <div class="right-content">
        <button class="btn btn-primary" (click)="pageChange(1)">查询</button>
        <button class="btn btn-primary ml2" (click)="reset()">重置</button>
      </div>
    </div>
  </div>

  <div class="table-container">
    <vdr-data-table
      [items]="items$ | async"
      [itemsPerPage]="itemsPerPage$ | async"
      [totalItems]="totalItems$ | async"
      [currentPage]="currentPage$ | async"
      class="mt0"
      (pageChange)="pageChange($event)"
      (itemsPerPageChange)="perPageChange($event)"
    >
      <vdr-dt-column></vdr-dt-column>
      <vdr-dt-column style="width: 240px">商品信息</vdr-dt-column>
      <vdr-dt-column>商品分组</vdr-dt-column>
      <vdr-dt-column>库存</vdr-dt-column>
      <vdr-dt-column>创建时间</vdr-dt-column>
      <vdr-dt-column>不可选原因</vdr-dt-column>
      <ng-template let-item="item">
        <!-- [checked]="selectedProductIds.includes(item.id)" -->
        <td>
          <input *ngIf="singleSelect; else Checks" type="radio" name="id" id="id" [value]="item.id" [(ngModel)]="id" />
          <ng-template #Checks>
            <input
              type="checkbox"
              [(ngModel)]="item.checked"
              [disabled]="item.disableCheck"
              (click)="handleCheckProduct(item, $event.target.checked)"
            />
          </ng-template>
        </td>
        <td class="left">
          <div class="flex">
            <img class="product-preview" [src]="item.featuredAsset?.preview" />
            <div class="flex-col justify-between ml2" style="width: 6.5rem">
              <div
                *ngIf="item.name?.length > 10; else noTip"
                class="tooltip tooltip-xl tooltip-bottom-right"
                role="tooltip"
                aria-haspopup="true"
              >
                <div class="ell-text" style="width: 6.5rem">{{ item.name }}</div>
                <span class="tooltip-content">{{ item.name }}</span>
              </div>
              <ng-template #noTip>
                <div>{{ item.name }}</div>
              </ng-template>
              <div>{{ item.customFields?.price | tokenCNY }}</div>
            </div>
          </div>
        </td>
        <td class="left align-middle">
          <div *ngFor="let coll of item.collections">
            <div
              *ngIf="coll.name?.length > 5"
              class="tooltip tooltip-xl tooltip-bottom-right"
              role="tooltip"
              aria-haspopup="true"
            >
              <div class="ell-text" style="width: 3.5rem">{{ coll?.name }}</div>
              <span class="tooltip-content">{{ coll?.name }}</span>
            </div>
            <div *ngIf="coll.name?.length <= 5">{{ coll?.name }}</div>
          </div>
        </td>
        <td class="left align-middle" (click)="handleEditSkus(item)">
          {{ getStock(item) }}
        </td>
        <td class="left align-middle">
          {{ item.createdAt | fmDate }}
        </td>
        <td class="left align-middle">
          <div *ngIf="item.disableCheck">{{ item.disableCheckMessage }}</div>
        </td>
      </ng-template>
    </vdr-data-table>
  </div>
</div>

<ng-template vdrDialogButtons>
  <button class="btn" (click)="resolveWith()">取消</button>
  <button class="btn btn-primary" (click)="save()">确认</button>
</ng-template>
