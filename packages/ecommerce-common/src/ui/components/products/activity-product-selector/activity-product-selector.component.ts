import {
  Component,
  Input,
  Output,
  EventEmitter,
  forwardRef,
  ChangeDetectorRef,
  ContentChildren,
  TemplateRef,
  QueryList,
  AfterContentInit,
} from '@angular/core';
import {ControlValueAccessor, NG_VALUE_ACCESSOR} from '@angular/forms';
import {DataService, ModalService, DataTableColumnComponent} from '@vendure/admin-ui/core';
import {Product, ProductList, SortOrder} from '../../../generated-admin-types';

import {Observable} from 'rxjs';
// import {GET_ACTIVITY_USABLE_PRODUCTS} from '../graphql';
import {cloneDeep} from 'lodash';
import {ValueType} from '../../select-multiple/select-multiple.component';
import {getProductStock} from '../../../utils/stock';
import {ADDED_ACTIVITY_PRODUCTS} from '../graphql';

@Component({
  selector: 'app-activity-product-selector',
  templateUrl: './activity-product-selector.component.html',
  styleUrls: ['./activity-product-selector.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ActivityProductSelectorComponent),
      multi: true,
    },
  ],
})
export class ActivityProductSelectorComponent implements ControlValueAccessor, AfterContentInit {
  @Input() componentId: string;
  @Input() triggerLabel = '选择活动商品';
  @Input() notActivity = false;
  @Input() promotionType: string;
  @Input() startAt: DateValue;
  @Input() endAt: DateValue;
  @Input() applicableProducts: string[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  @Input() queryFilter: any = {};
  @Input() disableVirtualTypes: string[] = ['coupon', 'memberCard'];
  @Input() singleSelect = false;
  @Input() readOnly = false;
  @Input() isSortable = false;
  @Input() echoDisplayAll = false;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  @Input() disableCheckFn: ((p: Product) => {disabled: boolean; options: any}) | undefined = undefined;
  @Output() getProductsOrigin = new EventEmitter<Product[]>();
  @ContentChildren(DataTableColumnComponent) columns: QueryList<DataTableColumnComponent>;
  @ContentChildren(TemplateRef) contentTemplates: QueryList<TemplateRef<Product>>;
  prodTemplates: TemplateRef<Product>;

  items$: Observable<Product[]>;
  items: Product[];
  totalItems$: Observable<number>;
  totalItems = 0;
  curPage$: Observable<number>;
  curPage = 1;
  currentStart = 0;
  currentEnd = 0;
  itemsPerPage = 5;
  sortTargetArr: {id: ValueType; sort: number}[] = [];
  getStock = getProductStock;

  private _value: ValueType[] = [];
  get value(): ValueType[] {
    return this._value;
  }

  set value(v: ValueType[]) {
    this._value = v;
  }

  constructor(private dataService: DataService, private ref: ChangeDetectorRef, private modalService: ModalService) {}

  writeValue(ids: ValueType[]): void {
    if (ids?.length) {
      // console.log(this.startAt, this.endAt);
      if (this.isSortable) {
        this.sortTargetArr = this.generateSortIdsArr(ids);
      }
      this.value = ids;
      this.queryProducts(ids);
    } else {
      this.value = [];
      this.items = [];
    }
  }
  registerOnChange(fn: () => void = () => {}): void {
    this.onChangeCallback = fn;
  }
  registerOnTouched(fn: () => void = () => {}): void {
    this.onTouchedCallback = fn;
  }
  setDisabledState?(isDisabled: boolean): void {
    // TODO
    // throw new Error('Method not implemented.');
  }

  private onChangeCallback: (_: ValueType[]) => void = () => {};
  private onTouchedCallback: () => void = () => {};

  contextRemove = (id: string) => {
    return this.deleteProduct(id);
  };

  contextSortUp = (id: string) => {
    return this.sortUp(id);
  };

  contextSortDown = (id: string) => {
    return this.sortDown(id);
  };

  ngOnInit() {
    // console.log(this.value, this.startAt, this.endAt);
  }

  ngAfterContentInit(): void {
    this.prodTemplates = this.contentTemplates.last;
    // console.log(this.columns, this.contentTemplates);
  }

  // get curItems() {
  //   return this.items.slice((this.curPage - 1) * this.itemsPerPage, this.curPage * this.itemsPerPage);
  // }

  generateSortIdsArr(ids: ValueType[]) {
    return ids.map((v, i) => ({id: v, sort: i}));
  }

  queryProducts(ids: ValueType[]) {
    let skip, take;
    if (this.echoDisplayAll) {
      skip = 0;
      take = ids.length;
    } else {
      skip = (this.curPage - 1) * this.itemsPerPage;
      take = this.itemsPerPage;
    }
    return this.dataService
      .query<{products: ProductList}>(
        ADDED_ACTIVITY_PRODUCTS,
        {
          options: {
            skip,
            take,
            sort: {
              updatedAt: SortOrder.Desc,
            },
            filter: {
              id: {in: ids},
              freeGift: {
                eq: false,
              },
              // hidden: {
              //   eq: false,
              // },
            },
          },
        },
        'no-cache',
      )
      .mapSingle(res => res.products)
      .subscribe(result => {
        const res = cloneDeep(result);
        if (this.isSortable) {
          if (res.items?.length) {
            const newIds = res.items.map(i => i.id as ValueType);
            const oldSortFilter = this.sortTargetArr.filter(s => newIds.includes(s.id));
            oldSortFilter.forEach((os, i) => {
              const findIdx = res.items.findIndex(f => f.id === os.id);
              if (findIdx && findIdx !== -1) {
                const temp = res.items[findIdx];
                res.items.splice(findIdx, 1);
                res.items.splice(i, 0, temp);
              }
            });
            this.sortTargetArr = this.generateSortIdsArr(res.items.map(i => i.id));
          } else {
            this.sortTargetArr = [];
          }
        }
        this.items = res.items;
        this.totalItems = res.totalItems;
        this.currentStart = this.itemsPerPage * (this.curPage - 1) + 1;
        const totalPage = Math.ceil(this.totalItems / this.itemsPerPage);
        this.currentEnd =
          this.currentStart -
          1 +
          (totalPage === this.curPage ? this.totalItems - (this.curPage - 1) * this.itemsPerPage : this.itemsPerPage);
        this.getProductsOrigin.emit(this.items);
        this.ref.detectChanges();
      });
  }

  setPageNumber(page: number) {
    // console.log(page);
    this.curPage = page;
    if (this.echoDisplayAll) {
      this.currentStart = this.itemsPerPage * (this.curPage - 1) + 1;
      const totalPage = Math.ceil(this.totalItems / this.itemsPerPage);
      this.currentEnd =
        this.currentStart -
        1 +
        (totalPage === this.curPage ? this.totalItems - (this.curPage - 1) * this.itemsPerPage : this.itemsPerPage);
    } else {
      this.queryProducts(this.value);
    }
  }

  sortUp(id: ValueType) {
    if (this.isSortable && this.items?.length) {
      const findIdx = this.items.findIndex(f => f.id === id);
      if (findIdx === 0 || findIdx === -1) {
        return;
      }
      const target = this.items[findIdx];
      this.items.splice(findIdx, 1);
      this.items.splice(findIdx - 1, 0, target);
      this.getProductsOrigin.emit(this.items);
    }
  }

  sortDown(id: ValueType) {
    if (this.isSortable && this.items?.length) {
      const findIdx = this.items.findIndex(f => f.id === id);

      if (findIdx === this.items.length - 1 || findIdx === -1) {
        return;
      }
      const target = this.items[findIdx];
      this.items.splice(findIdx, 1);
      this.items.splice(findIdx + 1, 0, target);
      this.getProductsOrigin.emit(this.items);
    }
  }

  deleteProduct(id: ValueType) {
    this.modalService
      .dialog({
        title: '警告',
        body: `确认删除该商品？`,
        buttons: [
          {type: 'secondary', label: '取消', returnValue: false},
          {type: 'danger', label: '删除', returnValue: true},
        ],
      })
      .subscribe(res => {
        if (res) {
          this.items = this.items.filter(i => i.id !== id);
          if (this.echoDisplayAll) {
            const ids = this.items.map(i => i.id);
            this.value = ids;
            this.onChangeCallback(ids);
            const totalPage = Math.ceil(this.items.length / this.itemsPerPage);
            this.curPage = this.curPage > totalPage ? totalPage : this.curPage;
            this.currentStart = this.itemsPerPage * (this.curPage - 1);
            this.currentEnd = this.currentStart + this.items.length;
            this.totalItems = this.items.length;
            this.getProductsOrigin.emit(this.items);
            this.ref.detectChanges();
          } else {
            const ids = this.value.filter(i => i !== id);
            this.value = ids;
            this.onChangeCallback(ids);
            if (!this.items.length) {
              --this.curPage;
            }
            this.queryProducts(this.value);
          }
        }
      });
  }

  handleSelectProducts(ids: string[]) {
    this.curPage = 1;
    this.queryProducts(ids);

    this.value = ids;
    this.onChangeCallback(ids);
  }

  trackByFn(index: number, item: Product) {
    if (item.id != null) {
      return item.id;
    } else {
      return index;
    }
  }
}
