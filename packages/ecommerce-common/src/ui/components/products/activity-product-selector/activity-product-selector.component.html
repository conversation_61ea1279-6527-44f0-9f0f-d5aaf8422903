<div>
  <activity-product-select-trigger
    *ngIf="!notActivity"
    [locals]="{
      promotionType: promotionType,
      startAt: startAt,
      endAt: endAt,
      selected: value,
      queryFilter,
      disableVirtualTypes,
      singleSelect
    }"
    (resolveWith)="handleSelectProducts($event)"
  >
    <button *ngIf="!readOnly" class="btn-link label-trigger">{{ triggerLabel }}</button>
  </activity-product-select-trigger>
  <product-select-trigger
    *ngIf="notActivity"
    [locals]="{
      selectedIds: value,
      queryFilter: queryFilter,
      disableVirtualTypes: disableVirtualTypes,
      singleSelect,
      disableCheckFn
    }"
    (resolveWith)="handleSelectProducts($event)"
  >
    <button *ngIf="!readOnly" class="btn-link label-trigger">{{ triggerLabel }}</button>
  </product-select-trigger>
</div>

<ng-container *ngIf="items?.length">
  <table class="table">
    <thead>
      <tr>
        <ng-container *ngIf="columns.length; else noColumns">
          <th *ngFor="let header of columns?.toArray()" class="left align-middle" [class.expand]="header.expand">
            <ng-container *ngTemplateOutlet="header.template"></ng-container>
          </th>
        </ng-container>
        <ng-template #noColumns>
          <th class="left align-middle">商品名称</th>
          <th class="left align-middle">零售价（元）</th>
          <th class="left align-middle">是否下架</th>
          <th class="left align-middle">库存</th>
          <th class="align-middle">操作</th>
        </ng-template>
      </tr>
    </thead>
    <tbody>
      <tr
        *ngFor="
          let item of items
            | paginate
              : {
                  id: componentId,
                  itemsPerPage: itemsPerPage,
                  currentPage: curPage,
                  totalItems: totalItems
                };
          index as i;
          trackBy: trackByFn
        "
      >
        <ng-container *ngIf="prodTemplates; else noTemplate">
          <ng-container
            *ngTemplateOutlet="
              prodTemplates;
              context: {
                item: item,
                index: i,
                remove: contextRemove,
                items: items,
                curPage: curPage,
                perPage: itemsPerPage,
                sortUp: contextSortUp,
                sortDown: contextSortDown,
              }
            "
          ></ng-container>
        </ng-container>
        <ng-template #noTemplate>
          <td class="left align-middle">
            <div>{{ item.name }}</div>
          </td>
          <td class="left align-middle">
            {{ item?.customFields?.price | tokenCNY }}
          </td>
          <td class="left align-middle">
            {{ item.enabled ? '' : '已下架' }}
          </td>
          <td class="left align-middle">
            {{ getStock(item) }}
          </td>
          <td class="align-middle no-wrap operation-td" [class.disabled]="!item.enabled">
            <button *ngIf="!readOnly" type="button" class="btn-link is-danger" (click)="deleteProduct(item.id)">
              <clr-icon shape="trash" class="is-danger"></clr-icon>
              删除
            </button>
          </td>
        </ng-template>
      </tr>
    </tbody>
  </table>
  <div class="table-footer">
    <app-items-per-page-controls [itemsPerPage]="itemsPerPage" [perConfig]="[5]"></app-items-per-page-controls>

    <div *ngIf="totalItems" class="p5">
      {{ 'common.total-items' | translate : {currentStart, currentEnd, totalItems} }}
    </div>

    <vdr-pagination-controls
      *ngIf="totalItems"
      [id]="componentId"
      [currentPage]="curPage"
      [itemsPerPage]="5"
      [totalItems]="totalItems"
      (pageChange)="setPageNumber($event)"
    ></vdr-pagination-controls>
  </div>
</ng-container>
<!-- <ng-template #emptyPlaceholder>
  <vdr-empty-placeholder [emptyStateLabel]="emptyStateLabel"></vdr-empty-placeholder>
</ng-template> -->

<!-- <vdr-data-table
  *ngIf="(items$ | async).length"
  [items]="items$ | async"
  [itemsPerPage]="itemsPerPage$ | async"
  [totalItems]="totalItems$ | async"
  [currentPage]="curPage$ | async"
  (pageChange)="setPageNumber($event)"
>
  <vdr-dt-column>商品名称 </vdr-dt-column>
  <vdr-dt-column>零售价（元）</vdr-dt-column>
  <vdr-dt-column>库存</vdr-dt-column>
  <vdr-dt-column>操作 </vdr-dt-column>
  <ng-template let-result="item">
    <td class="left align-middle" [class.disabled]="!result.enabled">
      <div>{{ groupByProduct ? result.name : result.name }}</div>
      <div *ngIf="!groupByProduct" class="sku">{{ result.sku }}</div>
    </td>
    <td class="left align-middle">
      {{ result?.customFields?.price | tokenCNY }}
    </td>
    <td class="left align-middle">
      {{ stock(result) }}
    </td>
    <td class="right align-middle no-wrap operation-td" [class.disabled]="!result.enabled">
      <button type="button" class="delete-button" (click)="deleteProduct(result.id)" vdrDropdownItem>
        <clr-icon shape="trash" class="is-danger"></clr-icon>
        {{ 'common.delete' | translate }}
      </button>
    </td>
  </ng-template>
</vdr-data-table> -->
