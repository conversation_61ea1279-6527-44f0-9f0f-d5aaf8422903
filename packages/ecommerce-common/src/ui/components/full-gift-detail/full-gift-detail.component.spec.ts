import {ComponentFixture, TestBed} from '@angular/core/testing';
import {FormBuilder, ReactiveFormsModule, Validators} from '@angular/forms';
import {Router, ActivatedRoute} from '@angular/router';
import {of} from 'rxjs';
import {DataService, NotificationService, ServerConfigService} from '@vendure/admin-ui/core';
import {FullGiftDetailComponent} from './full-gift-detail.component';
import {
  FullDiscountPresent,
  FullDiscountPresentType,
  ActivityStatus,
  RuleType,
  DiscountType,
  ApplicableType,
} from '../../generated-shop-types';

describe('FullGiftDetailComponent', () => {
  let component: FullGiftDetailComponent;
  let fixture: ComponentFixture<FullGiftDetailComponent>;
  let mockDataService: jasmine.SpyObj<DataService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockRoute: jasmine.SpyObj<ActivatedRoute>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockServerConfigService: jasmine.SpyObj<ServerConfigService>;
  let formBuilder: FormBuilder;

  const mockFullDiscountPresent: FullDiscountPresent = {
    id: '1',
    name: '满100减10活动',
    displayName: '满100减10',
    type: FullDiscountPresentType.AmountFullReduction,
    remarks: '测试活动',
    status: ActivityStatus.Normal,
    startTime: new Date().toISOString(),
    endTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    introduce: '满100减10优惠活动',
    ruleType: RuleType.Ladder,
    ruleValues: [
      {
        minimum: 10000,
        discountValue: {
          discountType: DiscountType.FixedAmount,
          discount: 1000,
        },
        maximumOffer: 0,
      },
    ],
    applicableProduct: {
      applicableType: ApplicableType.All,
      productIds: [],
    },
    stackingDiscountSwitch: false,
    stackingPromotionTypes: [],
    whetherRestrictUsers: false,
    memberPlanIds: [],
    showLabelInCommodity: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  beforeEach(async () => {
    const dataServiceSpy = jasmine.createSpyObj('DataService', ['query', 'mutate']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const routeSpy = jasmine.createSpyObj('ActivatedRoute', [], {
      snapshot: {
        params: {id: '1'},
        queryParams: {},
      },
      params: of({id: '1'}),
      queryParams: of({}),
    });
    const notificationServiceSpy = jasmine.createSpyObj('NotificationService', ['success', 'error']);
    const serverConfigServiceSpy = jasmine.createSpyObj('ServerConfigService', ['getConfig']);

    await TestBed.configureTestingModule({
      declarations: [FullGiftDetailComponent],
      imports: [ReactiveFormsModule],
      providers: [
        FormBuilder,
        {provide: DataService, useValue: dataServiceSpy},
        {provide: Router, useValue: routerSpy},
        {provide: ActivatedRoute, useValue: routeSpy},
        {provide: NotificationService, useValue: notificationServiceSpy},
        {provide: ServerConfigService, useValue: serverConfigServiceSpy},
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(FullGiftDetailComponent);
    component = fixture.componentInstance;
    formBuilder = TestBed.inject(FormBuilder);
    mockDataService = TestBed.inject(DataService) as jasmine.SpyObj<DataService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockRoute = TestBed.inject(ActivatedRoute) as jasmine.SpyObj<ActivatedRoute>;
    mockNotificationService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;
    mockServerConfigService = TestBed.inject(ServerConfigService) as jasmine.SpyObj<ServerConfigService>;

    // 模拟数据服务返回
    mockDataService.query.and.returnValue(of({fullDiscountPresent: mockFullDiscountPresent}));
    mockDataService.mutate.and.returnValue(of({upsertFullDiscountPresent: mockFullDiscountPresent}));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Form Initialization', () => {
    it('should initialize form with correct structure', () => {
      component.ngOnInit();

      expect(component.detailForm).toBeDefined();
      expect(component.detailForm.get('name')).toBeDefined();
      expect(component.detailForm.get('displayName')).toBeDefined();
      expect(component.detailForm.get('remarks')).toBeDefined();
      expect(component.detailForm.get('type')).toBeDefined();
      expect(component.detailForm.get('ruleType')).toBeDefined();
      expect(component.detailForm.get('dateRange')).toBeDefined();
      expect(component.detailForm.get('applicableProduct')).toBeDefined();
      expect(component.detailForm.get('stackingDiscountSwitch')).toBeDefined();
      expect(component.detailForm.get('whetherRestrictUsers')).toBeDefined();
      expect(component.detailForm.get('introduce')).toBeDefined();
      expect(component.detailForm.get('showLabelInCommodity')).toBeDefined();
    });

    it('should set form validators correctly', () => {
      component.ngOnInit();

      const nameControl = component.detailForm.get('name');
      const displayNameControl = component.detailForm.get('displayName');

      expect(nameControl?.hasError('required')).toBe(true);
      expect(displayNameControl?.hasError('required')).toBe(true);

      nameControl?.setValue('测试活动');
      displayNameControl?.setValue('测试');

      expect(nameControl?.hasError('required')).toBe(false);
      expect(displayNameControl?.hasError('required')).toBe(false);
    });
  });

  describe('Activity Type Switching', () => {
    beforeEach(() => {
      component.ngOnInit();
    });

    it('should handle switching to amount full reduction type', () => {
      component.switchFullGiftType();

      expect(component.detailForm.get('type')?.value).toBe(FullDiscountPresentType.AmountFullReduction);
      expect(component.isQuantityRule).toBe(false);
    });

    it('should handle switching to quantity full reduction type', () => {
      component.detailForm.get('type')?.setValue(FullDiscountPresentType.QuantityFullReduction);
      component.switchFullGiftType();

      expect(component.isQuantityRule).toBe(true);
    });

    it('should handle switching to amount full present type', () => {
      component.switchFullGiftType('amountFullPresent');

      expect(component.detailForm.get('type')?.value).toBe(FullDiscountPresentType.AmountFullPresent);
      expect(component.isAmountFullPresent).toBe(true);
    });

    it('should disable cycle rule for amount full present', () => {
      component.switchFullGiftType('amountFullPresent');

      expect(component.isCycleRule).toBe(false);
      expect(component.detailForm.get('ruleType')?.value).toBe(RuleType.Ladder);
    });
  });

  describe('Rule Management', () => {
    beforeEach(() => {
      component.ngOnInit();
      component.couponList = [
        {
          minimum: 10000,
          discountValue: {
            discountType: DiscountType.FixedAmount,
            discount: 1000,
          },
          freeGifts: [],
          maximumOffer: 0,
        },
      ];
    });

    it('should add new rule correctly', () => {
      const initialLength = component.couponList.length;
      component.addCoupon();

      expect(component.couponList.length).toBe(initialLength + 1);
      expect(component.couponList[component.couponList.length - 1]).toEqual({
        minimum: 0,
        discountValue: {
          discountType: DiscountType.FixedAmount,
          discount: 0,
        },
        freeGifts: [],
        maximumOffer: 0,
      });
    });

    it('should remove rule correctly', () => {
      component.couponList = [
        {minimum: 10000, discountValue: {discountType: DiscountType.FixedAmount, discount: 1000}, freeGifts: [], maximumOffer: 0},
        {minimum: 20000, discountValue: {discountType: DiscountType.FixedAmount, discount: 2000}, freeGifts: [], maximumOffer: 0},
      ];

      component.removeCoupon(0);

      expect(component.couponList.length).toBe(1);
      expect(component.couponList[0].minimum).toBe(20000);
    });

    it('should not remove rule when only one exists', () => {
      component.couponList = [
        {minimum: 10000, discountValue: {discountType: DiscountType.FixedAmount, discount: 1000}, freeGifts: [], maximumOffer: 0},
      ];

      component.removeCoupon(0);

      expect(component.couponList.length).toBe(1);
    });
  });

  describe('Discount Type Switching', () => {
    beforeEach(() => {
      component.ngOnInit();
      component.couponList = [
        {
          minimum: 10000,
          discountValue: {
            discountType: DiscountType.FixedAmount,
            discount: 1000,
          },
          freeGifts: [],
          maximumOffer: 0,
        },
      ];
    });

    it('should switch discount type correctly', () => {
      const coupon = component.couponList[0];
      component.switchDiscountType(coupon, DiscountType.FixedPercent);

      expect(coupon.discountValue.discountType).toBe(DiscountType.FixedPercent);
      expect(coupon.discountValue.discount).toBe(0);
    });

    it('should switch to no discount correctly', () => {
      const coupon = component.couponList[0];
      component.switchDiscountType(coupon, DiscountType.NoDiscount);

      expect(coupon.discountValue.discountType).toBe(DiscountType.NoDiscount);
      expect(coupon.discountValue.discount).toBe(0);
    });
  });

  describe('Form Validation', () => {
    beforeEach(() => {
      component.ngOnInit();
    });

    it('should validate required fields', () => {
      expect(component.detailForm.valid).toBe(false);

      component.detailForm.patchValue({
        name: '测试活动',
        displayName: '测试',
        dateRange: {
          startTime: new Date(),
          endTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        },
      });

      expect(component.detailForm.get('name')?.valid).toBe(true);
      expect(component.detailForm.get('displayName')?.valid).toBe(true);
    });

    it('should validate date range', () => {
      const startTime = new Date();
      const endTime = new Date(startTime.getTime() - 24 * 60 * 60 * 1000); // 结束时间早于开始时间

      component.detailForm.patchValue({
        dateRange: {startTime, endTime},
      });

      // 这里应该有自定义验证器来检查日期范围
      // 具体实现取决于组件中的验证逻辑
    });
  });

  describe('Save Functionality', () => {
    beforeEach(() => {
      component.ngOnInit();
      component.detailForm.patchValue({
        name: '测试活动',
        displayName: '测试',
        type: FullDiscountPresentType.AmountFullReduction,
        ruleType: RuleType.Ladder,
        dateRange: {
          startTime: new Date(),
          endTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        },
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        whetherRestrictUsers: false,
        introduce: '测试活动介绍',
        showLabelInCommodity: true,
      });
      component.couponList = [
        {
          minimum: 10000,
          discountValue: {
            discountType: DiscountType.FixedAmount,
            discount: 1000,
          },
          freeGifts: [],
          maximumOffer: 0,
        },
      ];
    });

    it('should save activity successfully', () => {
      spyOn(component, 'save').and.callThrough();
      mockDataService.mutate.and.returnValue(of({upsertFullDiscountPresent: mockFullDiscountPresent}));

      component.save();

      expect(component.save).toHaveBeenCalled();
      expect(mockDataService.mutate).toHaveBeenCalled();
    });

    it('should handle save errors', () => {
      spyOn(component, 'save').and.callThrough();
      mockDataService.mutate.and.throwError('Save failed');

      expect(() => component.save()).toThrow();
    });
  });
});
