import {Component, ChangeDetectionStrategy, Input} from '@angular/core';
import {BaseNgModelComponent} from '../../base/base-ng-model.component';
import {JumpType} from '../../../generated-admin-types';
import {NG_VALUE_ACCESSOR} from '@angular/forms';
import {CUSTOM_PAGES, MEMBERSHIP_PLANS} from '../../../graphql/graphql';
import {COUPONS} from '../../coupons-list/graphql';
import {LIST as FULL_GIFT_LIST} from '../../full-gift-list/graphql';
import {LIST as HALF_PRICE_LIST} from '../../half-price-list/graphql';
import {PURCHASE_PREMIUMS} from '../../mark-up-buy-list/graphql';
import {PACKAGE_DISCOUNTS} from '../../package-discount/graphql/graphql';
import {COUPON_BUNDLES, FIRST_CUSTOMER_BENEFITS} from '../../coupon-package/graphql';
import {BLIND_BOX_ACTIVITY_LIST, WISH_BOX_ACTIVITY_LIST} from '../../blind-box/graphql';
import {PRODUCTS} from '../../product-list/graphql';
import {FORUM_CATEGORY_LIST} from '../../store-notes/graphql';

export const appJumpOptionsConfig = [
  {
    label: '不跳转',
    value: 'blank',
  },
  {
    label: 'url',
    value: 'url',
  },
  {
    label: '商品',
    value: 'productDetails',
  },
  {
    label: '活动页',
    value: 'activePage',
  },
  {
    label: '优惠券',
    value: 'coupon',
  },
  {
    label: '优惠券礼包',
    value: 'couponBundle',
  },
  {
    label: '满减活动',
    value: 'fullDiscount',
  },
  {
    label: '第X件Y折活动',
    value: 'discountByQuantity',
  },
  {
    label: '加价购活动',
    value: 'purchasePremium',
  },
  {
    label: '打包一口价',
    value: 'packageDiscount',
  },
  {
    label: '会员卡页面',
    value: 'memberShipPlan',
  },
  {
    label: '会员卡列表页',
    value: 'membershipPlanListPage',
  },
  {
    label: '小程序',
    value: 'smallProgram',
  },
  {
    label: '签到页面',
    value: 'checkinPage',
  },
  {
    label: '盲盒活动页',
    value: 'blindBoxActivity',
  },
  {
    label: '积分商城',
    value: 'pointsMall',
  },
  {
    label: '论坛',
    value: 'forum',
  },
  {
    label: '论坛主分类',
    value: 'forumCategory',
  },
  {
    label: '商品购买弹框',
    value: 'popupPurchase',
  },
  {
    label: '盲盒2.0活动',
    value: 'wishBoxActivity',
  },
];
interface JumpItem {
  [x: string]: string;
  jumpType: JumpType;
  jumpValue: string;
}

export const staticJumpTypes = ['blank', 'forum', 'pointsMall', 'membershipPlanListPage'];
@Component({
  selector: 'app-jump',
  templateUrl: './app-jump.component.html',
  styleUrls: ['./app-jump.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: AppJumpComponent,
      multi: true,
    },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppJumpComponent extends BaseNgModelComponent<JumpItem> {
  @Input() appendTo = 'ng-select';
  @Input() required = true;
  CUSTOM_PAGES = CUSTOM_PAGES;
  COUPONS = COUPONS;
  FULL_GIFT_LIST = FULL_GIFT_LIST;
  HALF_PRICE_LIST = HALF_PRICE_LIST;
  PURCHASE_PREMIUMS = PURCHASE_PREMIUMS;
  MEMBERSHIP_PLANS = MEMBERSHIP_PLANS;
  PACKAGE_PRICE = PACKAGE_DISCOUNTS;
  COUPON_BUNDLES = COUPON_BUNDLES;
  FIRST_CUSTOMER_BENEFITS = FIRST_CUSTOMER_BENEFITS;
  BLIND_BOX_ACTIVITY_LIST = BLIND_BOX_ACTIVITY_LIST;
  WISH_BOX_ACTIVITY_LIST = WISH_BOX_ACTIVITY_LIST;
  FORUM_CATEGORY_LIST = FORUM_CATEGORY_LIST;

  PRODUCTS = PRODUCTS;
  customPageSelectFilter = {type: {contains: 'activePage'}};
  couponSelectFilter = {state: {in: ['normal', 'notStarted']}, enable: {eq: true}};
  couponBundleFIlter = {state: {eq: 'normal'}};
  fullDiscountSelectFilter = {status: {in: ['normal', 'notStarted']}};
  // fullDiscountSelectFilter = {status: {in: ['normal', 'notStarted']}, type: {notEq: 'amountFullPresent'}};
  // actuallyPaidSelectFilter = {status: {in: ['normal', 'notStarted']}, type: {eq: 'amountFullPresent'}};
  discountByQuantitySelectFilter = {status: {in: ['normal', 'notStarted']}};
  purchasePremiumSelectFilter = {state: {in: ['normal', 'notStarted']}};
  memberShipPlanSelectFilter = {state: {in: ['shelf']}};
  packagePriceSelectFilter = {status: {in: ['normal', 'notStarted']}};
  productsFilter = {enabled: {eq: true}};
  forumCategoryFilter = {parentId: {isNull: true}};

  optionsConfig = appJumpOptionsConfig;

  get showJumpValue() {
    return this.value.jumpType && !staticJumpTypes.includes(this.value.jumpType);
  }

  constructor() {
    super();
  }

  ngOnInit() {}
  ngModelChange() {
    // console.log('ngModelChange');
    this.onChange(this.value);
  }
  writeValue(value: JumpItem): void {
    this.value = value;
  }
}
