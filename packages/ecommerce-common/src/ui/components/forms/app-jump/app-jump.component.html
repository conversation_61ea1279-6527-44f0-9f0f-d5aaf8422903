<div *ngIf="value">
  <div class="w-100">
    <div class="label-text">跳转类型</div>
    <select
      class="w-100"
      [required]="required"
      [(ngModel)]="value.jumpType"
      (ngModelChange)="value.jumpValue = ''"
      (ngModelChange)="ngModelChange()"
    >
      <option *ngFor="let item of optionsConfig" [value]="item.value">{{ item.label }}</option>
    </select>
  </div>
  <div *ngIf="showJumpValue">
    <div class="label-text">跳转链接</div>
    <div *ngIf="value.jumpType === 'url' || value.jumpType === 'smallProgram'">
      <input
        [required]="required"
        class="w-100"
        type="text"
        [(ngModel)]="value.jumpValue"
        (ngModelChange)="ngModelChange()"
      />
    </div>
    <div *ngIf="value.jumpType === 'productDetails'">
      <app-select-product
        [(ngModel)]="value.jumpValue"
        [required]="required"
        [onlyEnabled]="true"
        [appendTo]="appendTo"
        (ngModelChange)="ngModelChange()"
      ></app-select-product>
    </div>
    <div *ngIf="value.jumpType === 'activePage'">
      <app-select-async
        [(ngModel)]="value.jumpValue"
        [required]="required"
        [gql]="CUSTOM_PAGES"
        searchKey="title"
        labelKey="title"
        [filter]="customPageSelectFilter"
        [appendTo]="appendTo"
        (ngModelChange)="ngModelChange()"
      ></app-select-async>
    </div>
    <div *ngIf="value.jumpType === 'coupon'">
      <app-select-async
        [(ngModel)]="value.jumpValue"
        [required]="required"
        [gql]="COUPONS"
        [filter]="couponSelectFilter"
        [appendTo]="appendTo"
        (ngModelChange)="ngModelChange()"
      ></app-select-async>
    </div>
    <div *ngIf="value.jumpType === 'couponBundle'">
      <app-select-async
        [(ngModel)]="value.jumpValue"
        [required]="required"
        [gql]="COUPON_BUNDLES"
        [filter]="couponBundleFIlter"
        [appendTo]="appendTo"
        (ngModelChange)="ngModelChange()"
      ></app-select-async>
    </div>
    <!-- <div *ngIf="value.jumpType === 'firstCustomerBenefitBundle'">
      <app-select-async
        [(ngModel)]="value.jumpValue"
        [required]="required"
        [gql]="FIRST_CUSTOMER_BENEFITS"
        [appendTo]="appendTo"
        (ngModelChange)="ngModelChange()"
      ></app-select-async>
    </div> -->
    <div *ngIf="value.jumpType === 'fullDiscount'">
      <app-select-async
        [(ngModel)]="value.jumpValue"
        [required]="required"
        [gql]="FULL_GIFT_LIST"
        [filter]="fullDiscountSelectFilter"
        [appendTo]="appendTo"
        (ngModelChange)="ngModelChange()"
      ></app-select-async>
    </div>

    <div *ngIf="value.jumpType === 'discountByQuantity'">
      <app-select-async
        [(ngModel)]="value.jumpValue"
        [required]="required"
        [gql]="HALF_PRICE_LIST"
        [filter]="discountByQuantitySelectFilter"
        [appendTo]="appendTo"
        (ngModelChange)="ngModelChange()"
      ></app-select-async>
    </div>

    <div *ngIf="value.jumpType === 'purchasePremium'">
      <app-select-async
        [(ngModel)]="value.jumpValue"
        [required]="required"
        [gql]="PURCHASE_PREMIUMS"
        [filter]="purchasePremiumSelectFilter"
        [appendTo]="appendTo"
        (ngModelChange)="ngModelChange()"
      ></app-select-async>
    </div>

    <div *ngIf="value.jumpType === 'packageDiscount'">
      <app-select-async
        [(ngModel)]="value.jumpValue"
        [required]="required"
        [gql]="PACKAGE_PRICE"
        [filter]="packagePriceSelectFilter"
        [appendTo]="appendTo"
        (ngModelChange)="ngModelChange()"
      ></app-select-async>
    </div>

    <div *ngIf="value.jumpType === 'memberShipPlan'">
      <app-select-async
        [(ngModel)]="value.jumpValue"
        [required]="required"
        [gql]="MEMBERSHIP_PLANS"
        [filter]="memberShipPlanSelectFilter"
        [appendTo]="appendTo"
        (ngModelChange)="ngModelChange()"
      ></app-select-async>
    </div>

    <div *ngIf="value.jumpType === 'checkinPage'">
      <app-select-async
        [(ngModel)]="value.jumpValue"
        [required]="required"
        [gql]="CUSTOM_PAGES"
        searchKey="title"
        labelKey="title"
        [filter]="{type: {eq: 'checkinPage'}}"
        [appendTo]="appendTo"
        (ngModelChange)="ngModelChange()"
      ></app-select-async>
    </div>

    <div *ngIf="value.jumpType === 'blindBoxActivity'">
      <app-select-async
        [(ngModel)]="value.jumpValue"
        [required]="required"
        [gql]="BLIND_BOX_ACTIVITY_LIST"
        [filter]="{statue: {in: ['normal', 'notStarted']}}"
        [appendTo]="appendTo"
        (ngModelChange)="ngModelChange()"
      ></app-select-async>
    </div>

    <div *ngIf="value.jumpType === 'popupPurchase'">
      <app-select-async
        [(ngModel)]="value.jumpValue"
        [required]="required"
        [gql]="PRODUCTS"
        [filter]="productsFilter"
        [appendTo]="appendTo"
        (ngModelChange)="ngModelChange()"
      ></app-select-async>
    </div>

    <div *ngIf="value.jumpType === 'wishBoxActivity'">
      <app-select-async
        [(ngModel)]="value.jumpValue"
        [required]="required"
        [gql]="WISH_BOX_ACTIVITY_LIST"
        [filter]="{status: {eq: 'opened'}}"
        [appendTo]="appendTo"
        (ngModelChange)="ngModelChange()"
      ></app-select-async>
    </div>

    <div *ngIf="value.jumpType === 'forumCategory'">
      <app-select-async
        [(ngModel)]="value.jumpValue"
        [required]="required"
        [gql]="FORUM_CATEGORY_LIST"
        [filter]="forumCategoryFilter"
        [appendTo]="appendTo"
        (ngModelChange)="ngModelChange()"
      ></app-select-async>
    </div>
  </div>
</div>
