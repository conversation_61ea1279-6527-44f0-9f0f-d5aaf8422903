/* eslint-disable @typescript-eslint/no-explicit-any */
import {Component, ChangeDetectionStrategy, Input, Output, EventEmitter, OnChanges, SimpleChanges} from '@angular/core';
import {<PERSON>ValueAccessor} from '@angular/forms';
// import {JumpType} from '../../../generated-shop-types';
import {NG_VALUE_ACCESSOR} from '@angular/forms';
import {CUSTOM_PAGES, MEMBERSHIP_PLANS} from '../../../graphql/graphql';
import {COUPONS} from '../../coupons-list/graphql';
import {LIST as FULL_GIFT_LIST} from '../../full-gift-list/graphql';
import {LIST as HALF_PRICE_LIST} from '../../half-price-list/graphql';
import {PURCHASE_PREMIUMS} from '../../mark-up-buy-list/graphql';
import {DISTRIBUTOR_LIST} from '../../../graphql/graphql';
import {PACKAGE_DISCOUNTS} from '../../package-discount/graphql/graphql';
import {COUPON_BUNDLES, FIRST_CUSTOMER_BENEFITS} from '../../coupon-package/graphql';
import {BLIND_BOX_ACTIVITY_LIST, WISH_BOX_ACTIVITY_LIST} from '../../blind-box/graphql';
import {staticJumpTypes} from '../app-jump/app-jump.component';
import {PRODUCTS} from '../../product-list/graphql';
import {FORUM_CATEGORY_LIST} from '../../store-notes/graphql';

// interface JumpItem {
//   [x: string]: string;
//   jumpType: JumpType;
//   jumpValue: string;
// }

type OptionsType = {
  label: string;
  value: string;
};

// 用于设置小程序外部跳转进入小程序应用的跳转设置
@Component({
  selector: 'app-jump-external',
  templateUrl: './app-jump-external.component.html',
  styleUrls: ['./app-jump-external.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: AppJumpExternalComponent,
      multi: true,
    },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppJumpExternalComponent implements ControlValueAccessor, OnChanges {
  @Input() showLabel = true;
  @Input() jumpType = 'blank';
  @Input() appendTo = 'ng-select';
  @Input() required = true;
  @Input() excludes: string[]; // 可根据需求 排除不需要的optionConfig
  @Input() queryFilter: any = {};
  @Output() change = new EventEmitter();

  onChange = (t: any) => {};
  onTouched = () => {};

  CUSTOM_PAGES = CUSTOM_PAGES;
  COUPONS = COUPONS;
  FULL_GIFT_LIST = FULL_GIFT_LIST;
  HALF_PRICE_LIST = HALF_PRICE_LIST;
  PURCHASE_PREMIUMS = PURCHASE_PREMIUMS;
  MEMBERSHIP_PLANS = MEMBERSHIP_PLANS;
  DISTRIBUTOR_LIST = DISTRIBUTOR_LIST;
  PACKAGE_PRICE = PACKAGE_DISCOUNTS;
  COUPON_BUNDLES = COUPON_BUNDLES;
  FIRST_CUSTOMER_BENEFITS = FIRST_CUSTOMER_BENEFITS;
  BLIND_BOX_ACTIVITY_LIST = BLIND_BOX_ACTIVITY_LIST;
  PRODUCTS = PRODUCTS;
  WISH_BOX_ACTIVITY_LIST = WISH_BOX_ACTIVITY_LIST;
  FORUM_CATEGORY_LIST = FORUM_CATEGORY_LIST;

  staticJumpTypes = staticJumpTypes;

  customPageSelectFilter = {type: {contains: 'activePage'}};
  couponSelectFilter = {state: {in: ['normal', 'notStarted']}, enable: {eq: true}};
  couponBundleFIlter = {state: {eq: 'normal'}};
  fullDiscountSelectFilter = {status: {in: ['normal', 'notStarted']}};
  discountByQuantitySelectFilter = {status: {in: ['normal', 'notStarted']}};
  purchasePremiumSelectFilter = {state: {in: ['normal', 'notStarted']}};
  memberShipPlanSelectFilter = {state: {in: ['shelf']}};
  packagePriceSelectFilter = {status: {in: ['normal', 'notStarted']}};
  checkinPageSelectFilter = {type: {eq: 'checkinPage'}};
  blindBoxActivityFilter = {statue: {in: ['normal', 'notStarted']}};
  wishBoxActivityFilter = {status: {eq: 'opened'}};
  productsFilter = {enabled: {eq: true}};
  forumCategoryFilter = {parentId: {isNull: true}};

  _value: any;

  get value() {
    return this._value;
  }

  set value(v) {
    this._value = v;
    this.onChange(this._value);
  }

  get isShow() {
    return !this.staticJumpTypes.includes(this.jumpType);
  }

  optionsConfig: OptionsType[];

  constructor() {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['queryFilter']) {
      console.log(changes['queryFilter']);
      this.customPageSelectFilter = {...this.customPageSelectFilter, ...this.queryFilter};
      this.checkinPageSelectFilter = {...this.checkinPageSelectFilter, ...this.queryFilter};
      // this.productsFilter = {...this.productsFilter, ...this.queryFilter};
    }
  }

  writeValue(obj: any): void {
    this.value = obj;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    // throw new Error('Method not implemented.');
  }

  setDisabledState?(isDisabled: boolean): void {
    // throw new Error('Method not implemented.');
  }

  ngOnInit() {
    console.log(this.jumpType);
  }

  ngModelChange() {
    // console.log('ngModelChange');
    this.change.emit(this.value);
  }
}
