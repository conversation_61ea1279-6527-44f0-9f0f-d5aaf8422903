<div *ngIf="isShow">
  <div *ngIf="showLabel" class="label-text">跳转链接</div>
  <div *ngIf="jumpType === 'url' || jumpType === 'smallProgram'">
    <input [required]="required" class="w-100" type="text" [(ngModel)]="value" (ngModelChange)="ngModelChange()" />
  </div>
  <div *ngIf="jumpType === 'productDetails'">
    <app-select-product
      [(ngModel)]="value"
      [required]="required"
      [onlyEnabled]="true"
      [appendTo]="appendTo"
      (ngModelChange)="ngModelChange()"
    ></app-select-product>
  </div>
  <div *ngIf="jumpType === 'activePage'">
    <app-select-async
      [(ngModel)]="value"
      [required]="required"
      [gql]="CUSTOM_PAGES"
      searchKey="title"
      labelKey="title"
      [filter]="customPageSelectFilter"
      [appendTo]="appendTo"
      (ngModelChange)="ngModelChange()"
    ></app-select-async>
  </div>
  <div *ngIf="jumpType === 'coupon'">
    <app-select-async
      [(ngModel)]="value"
      [required]="required"
      [gql]="COUPONS"
      [filter]="couponSelectFilter"
      [appendTo]="appendTo"
      (ngModelChange)="ngModelChange()"
    ></app-select-async>
  </div>
  <div *ngIf="jumpType === 'couponBundle'">
    <app-select-async
      [(ngModel)]="value"
      [required]="required"
      [gql]="COUPON_BUNDLES"
      [filter]="couponBundleFIlter"
      [appendTo]="appendTo"
      (ngModelChange)="ngModelChange()"
    ></app-select-async>
  </div>
  <div *ngIf="jumpType === 'fullDiscount'">
    <app-select-async
      [(ngModel)]="value"
      [required]="required"
      [gql]="FULL_GIFT_LIST"
      [filter]="fullDiscountSelectFilter"
      [appendTo]="appendTo"
      (ngModelChange)="ngModelChange()"
    ></app-select-async>
  </div>

  <div *ngIf="jumpType === 'discountByQuantity'">
    <app-select-async
      [(ngModel)]="value"
      [required]="required"
      [gql]="HALF_PRICE_LIST"
      [filter]="discountByQuantitySelectFilter"
      [appendTo]="appendTo"
      (ngModelChange)="ngModelChange()"
    ></app-select-async>
  </div>

  <div *ngIf="jumpType === 'purchasePremium'">
    <app-select-async
      [(ngModel)]="value"
      [required]="required"
      [gql]="PURCHASE_PREMIUMS"
      [filter]="purchasePremiumSelectFilter"
      [appendTo]="appendTo"
      (ngModelChange)="ngModelChange()"
    ></app-select-async>
  </div>

  <div *ngIf="jumpType === 'packageDiscount'">
    <app-select-async
      [(ngModel)]="value"
      [required]="required"
      [gql]="PACKAGE_PRICE"
      [filter]="packagePriceSelectFilter"
      [appendTo]="appendTo"
      (ngModelChange)="ngModelChange()"
    ></app-select-async>
  </div>

  <div *ngIf="jumpType === 'memberShipPlan'">
    <app-select-async
      [(ngModel)]="value"
      [required]="required"
      [gql]="MEMBERSHIP_PLANS"
      [filter]="memberShipPlanSelectFilter"
      [appendTo]="appendTo"
      (ngModelChange)="ngModelChange()"
    ></app-select-async>
  </div>

  <div *ngIf="jumpType === 'checkinPage'">
    <app-select-async
      [(ngModel)]="value"
      [required]="required"
      [gql]="CUSTOM_PAGES"
      searchKey="title"
      labelKey="title"
      [filter]="checkinPageSelectFilter"
      [appendTo]="appendTo"
      (ngModelChange)="ngModelChange()"
    ></app-select-async>
  </div>

  <div *ngIf="jumpType === 'blindBoxActivity'">
    <app-select-async
      [(ngModel)]="value"
      [required]="required"
      [gql]="BLIND_BOX_ACTIVITY_LIST"
      [filter]="blindBoxActivityFilter"
      [appendTo]="appendTo"
      (ngModelChange)="ngModelChange()"
    ></app-select-async>
  </div>

  <div *ngIf="jumpType === 'popupPurchase'">
    <app-select-async
      [(ngModel)]="value"
      [required]="required"
      [gql]="PRODUCTS"
      [filter]="productsFilter"
      [appendTo]="appendTo"
      (ngModelChange)="ngModelChange()"
    ></app-select-async>
  </div>

  <div *ngIf="jumpType === 'wishBoxActivity'">
    <app-select-async
      [(ngModel)]="value"
      [required]="required"
      [gql]="WISH_BOX_ACTIVITY_LIST"
      [filter]="wishBoxActivityFilter"
      [appendTo]="appendTo"
      (ngModelChange)="ngModelChange()"
    ></app-select-async>
  </div>
  <div *ngIf="jumpType === 'forumCategory'">
    <app-select-async
      [(ngModel)]="value"
      [required]="required"
      [gql]="FORUM_CATEGORY_LIST"
      [filter]="forumCategoryFilter"
      [appendTo]="appendTo"
      (ngModelChange)="ngModelChange()"
    ></app-select-async>
  </div>
</div>
