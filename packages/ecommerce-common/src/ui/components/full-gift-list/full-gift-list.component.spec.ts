import {ComponentFixture, TestBed} from '@angular/core/testing';
import {Router, ActivatedRoute} from '@angular/router';
import {of} from 'rxjs';
import {DataService, NotificationService} from '@vendure/admin-ui/core';
import {FullGiftListComponent} from './full-gift-list.component';
import {FullDiscountPresent, FullDiscountPresentType, ActivityStatus, RuleType} from '../../generated-shop-types';

describe('FullGiftListComponent', () => {
  let component: FullGiftListComponent;
  let fixture: ComponentFixture<FullGiftListComponent>;
  let mockDataService: jasmine.SpyObj<DataService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockRoute: jasmine.SpyObj<ActivatedRoute>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;

  const mockFullDiscountPresents: FullDiscountPresent[] = [
    {
      id: '1',
      name: '满100减10活动',
      displayName: '满100减10',
      type: FullDiscountPresentType.AmountFullReduction,
      remarks: '测试活动1',
      status: ActivityStatus.Normal,
      startTime: new Date().toISOString(),
      endTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      introduce: '满100减10优惠活动',
      ruleType: RuleType.Ladder,
      ruleValues: [
        {
          minimum: 10000,
          discountValue: {
            discountType: 'fixedAmount',
            discount: 1000,
          },
          maximumOffer: 0,
        },
      ],
      applicableProduct: {
        applicableType: 'all',
        productIds: [],
      },
      stackingDiscountSwitch: false,
      stackingPromotionTypes: [],
      whetherRestrictUsers: false,
      memberPlanIds: [],
      showLabelInCommodity: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '2',
      name: '满3件减5元活动',
      displayName: '满3件减5元',
      type: FullDiscountPresentType.QuantityFullReduction,
      remarks: '测试活动2',
      status: ActivityStatus.Normal,
      startTime: new Date().toISOString(),
      endTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      introduce: '满3件减5元优惠活动',
      ruleType: RuleType.Cycle,
      ruleValues: [
        {
          minimum: 3,
          discountValue: {
            discountType: 'fixedAmount',
            discount: 500,
          },
          maximumOffer: 0,
        },
      ],
      applicableProduct: {
        applicableType: 'all',
        productIds: [],
      },
      stackingDiscountSwitch: false,
      stackingPromotionTypes: [],
      whetherRestrictUsers: false,
      memberPlanIds: [],
      showLabelInCommodity: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ];

  beforeEach(async () => {
    const dataServiceSpy = jasmine.createSpyObj('DataService', ['query', 'mutate']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const routeSpy = jasmine.createSpyObj('ActivatedRoute', [], {
      queryParams: of({}),
      params: of({}),
    });
    const notificationServiceSpy = jasmine.createSpyObj('NotificationService', ['success', 'error']);

    await TestBed.configureTestingModule({
      declarations: [FullGiftListComponent],
      providers: [
        {provide: DataService, useValue: dataServiceSpy},
        {provide: Router, useValue: routerSpy},
        {provide: ActivatedRoute, useValue: routeSpy},
        {provide: NotificationService, useValue: notificationServiceSpy},
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(FullGiftListComponent);
    component = fixture.componentInstance;
    mockDataService = TestBed.inject(DataService) as jasmine.SpyObj<DataService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockRoute = TestBed.inject(ActivatedRoute) as jasmine.SpyObj<ActivatedRoute>;
    mockNotificationService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;

    // 模拟数据服务返回
    mockDataService.query.and.returnValue(
      of({
        fullDiscountPresents: {
          totalItems: mockFullDiscountPresents.length,
          items: mockFullDiscountPresents,
        },
      }),
    );
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct query parameters', () => {
    expect(component.queryParam).toEqual({
      name: null,
      status: null,
      type: null,
      remarks: null,
    });
  });

  it('should have correct type options', () => {
    expect(component.typeOptions).toEqual([
      {label: '满N元减/送', value: 'amountFullReduction'},
      {label: '满N件减/送', value: 'quantityFullReduction'},
      {label: '实付满赠', value: 'amountFullPresent'},
    ]);
  });

  describe('formatRuleText', () => {
    it('should format amount full reduction rule correctly', () => {
      const item = mockFullDiscountPresents[0];
      const result = component.formatRuleText(item);

      expect(result).toEqual(['满100元减10元']);
    });

    it('should format quantity full reduction rule correctly', () => {
      const item = mockFullDiscountPresents[1];
      const result = component.formatRuleText(item);

      expect(result).toEqual(['每满3件减5元']);
    });

    it('should format rule with percentage discount correctly', () => {
      const item: FullDiscountPresent = {
        ...mockFullDiscountPresents[0],
        ruleValues: [
          {
            minimum: 10000,
            discountValue: {
              discountType: 'fixedPercent',
              discount: 80, // 8折
            },
            maximumOffer: 0,
          },
        ],
      };

      const result = component.formatRuleText(item);
      expect(result).toEqual(['满100元打8折']);
    });

    it('should format rule with free gifts correctly', () => {
      const item: FullDiscountPresent = {
        ...mockFullDiscountPresents[0],
        ruleValues: [
          {
            minimum: 10000,
            discountValue: {
              discountType: 'fixedAmount',
              discount: 1000,
            },
            freeGiftValues: [
              {
                freeGiftId: '1',
                freeGiftName: '测试赠品',
                freeGiftPrice: 0,
                freeGiftProductId: '1',
                maximumOffer: 1,
                priority: 1,
              },
            ],
            maximumOffer: 2,
          },
        ],
      };

      const result = component.formatRuleText(item);
      expect(result).toEqual(['满100元减10元，送2种赠品']);
    });

    it('should handle empty minimum value', () => {
      const item: FullDiscountPresent = {
        ...mockFullDiscountPresents[0],
        ruleValues: [
          {
            minimum: 0,
            discountValue: {
              discountType: 'fixedAmount',
              discount: 1000,
            },
            maximumOffer: 0,
          },
        ],
      };

      const result = component.formatRuleText(item);
      expect(result).toEqual(['']);
    });
  });

  describe('typeTranslate', () => {
    it('should translate type correctly', () => {
      expect(component.typeTranslate('amountFullReduction')).toBe('满N元减/送');
      expect(component.typeTranslate('quantityFullReduction')).toBe('满N件减/送');
      expect(component.typeTranslate('amountFullPresent')).toBe('实付满赠');
      expect(component.typeTranslate('unknown')).toBe('');
    });
  });

  describe('copyDetail', () => {
    it('should navigate to detail page with copy parameter', async () => {
      const activityId = '123';
      await component.copyDetail(activityId);

      expect(mockRouter.navigate).toHaveBeenCalledWith(['/extensions/ecommerce/full-gift-list', activityId], {
        queryParams: {copy: true},
      });
    });
  });

  describe('fail', () => {
    it('should call failure mutation and refresh on success', () => {
      const activityId = '123';
      mockDataService.mutate.and.returnValue(of({failureFullDiscountPresent: {id: activityId}}));
      spyOn(component, 'refresh');

      component.fail(activityId);

      expect(mockDataService.mutate).toHaveBeenCalledWith(component.FAILURE, {id: activityId});
      expect(mockNotificationService.success).toHaveBeenCalledWith('操作成功');
      expect(component.refresh).toHaveBeenCalled();
    });
  });

  describe('query filtering', () => {
    it('should build correct query options with filters', () => {
      component.queryParam = {
        name: '测试',
        status: 'normal',
        type: 'amountFullReduction',
        remarks: '备注',
      };

      // 触发查询构建
      const queryFn = (component as any).queryFn;
      const options = queryFn(0, 10);

      expect(options.options.filter).toEqual({
        name: {contains: '测试'},
        status: {contains: 'normal'},
        type: {eq: 'amountFullReduction'},
        remarks: {contains: '备注'},
      });
    });

    it('should build query options without filters when queryParam is empty', () => {
      component.queryParam = {
        name: null,
        status: null,
        type: null,
        remarks: null,
      };

      const queryFn = (component as any).queryFn;
      const options = queryFn(0, 10);

      expect(options.options.filter).toEqual({
        name: undefined,
        status: undefined,
        type: undefined,
        remarks: undefined,
      });
    });
  });
});
