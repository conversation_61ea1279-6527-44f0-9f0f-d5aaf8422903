@import '../../../global.scss';

.multi-tr-table {
  max-width: 100%;
  overflow: auto;
  .top-tr {
    margin-top: 12px;
    background-color: var(--color-weight-125);
    // td {
    // padding: 0;
    // div {
    //   padding: 12px 8px;
    // }
    // }
    &:hover {
      background-color: unset;
    }
  }
  td {
    margin-bottom: 8px;
    vertical-align: middle !important;
    border-left: 1px solid var(--color-weight-200);
    border-bottom: 1px solid var(--color-weight-200);
    .table-customer-info {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      white-space: nowrap;
    }
  }
}
