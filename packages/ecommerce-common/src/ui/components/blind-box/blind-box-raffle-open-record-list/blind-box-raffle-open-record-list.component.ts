/* eslint-disable @typescript-eslint/no-explicit-any */
import {Component, OnInit, ChangeDetectionStrategy} from '@angular/core';
import {FormBuilder} from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {ClrLoadingState} from '@clr/angular';
import {DataService, ModalService, NotificationService} from '@vendure/admin-ui/core';

import {CustomBaseListComponent} from '../../base/custom-base-list.component';
import {
  BLIND_BOX_BUY_LIST,
  BLIND_BOX_EXPORT_TASK_CREATE,
  BLIND_BOX_STATISTICS_EXPORT_TASK_CREATE,
  REFUND_WISH_BOX_ACTIVITY_BUY,
  WISH_BLIND_BOX_BUY_LIST,
} from '../graphql';
import {wishboxStatusObject, getCurrentAssistProgress} from '../const';
import {timePlusEightH} from '../../../utils/handle-date-param';

import {DateTime} from 'luxon';
import {BehaviorSubject, map, Observable, firstValueFrom} from 'rxjs';
import {utils, writeFile} from 'xlsx';
import {toFixedPrice} from '../../../utils/number';
import {DateRangeDialogComponent} from '../../dialog/date-range-dialog/date-range-dialog.component';
import {CustomInputsDialogComponent} from '../../dialog/custom-inputs-dialog/custom-inputs-dialog.component';
import {SortOrder} from '../../../generated-admin-types';

@Component({
  selector: 'app-blind-box-raffle-open-record-list',
  templateUrl: './blind-box-raffle-open-record-list.component.html',
  styleUrls: ['./blind-box-raffle-open-record-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BlindBoxRaffleOpenRecordListComponent extends CustomBaseListComponent<any, any> implements OnInit {
  dateRange: {startTime: string | undefined; endTime: string | undefined};

  exportLoading$: Observable<ClrLoadingState>;
  exportLoadingSubj = new BehaviorSubject(ClrLoadingState.DEFAULT);

  buyTypeOptions = [
    {label: '全部', value: ''},
    // {label: '待付款', value: 'pendingPay'},
    {label: '已付款', value: 'paid'},
    {label: '已退款', value: 'refunded'},
  ];
  wishboxStatus = wishboxStatusObject;

  getCurrentAssistProgress = getCurrentAssistProgress;

  constructor(
    router: Router,
    route: ActivatedRoute,
    private dataService: DataService,
    private fb: FormBuilder,
    private modal: ModalService,
    private notification: NotificationService,
  ) {
    super(router, route);
    super.setQueryFn(
      () => this.dataService.query(WISH_BLIND_BOX_BUY_LIST),
      res => res.wishBoxActivityBuys,
      (skip, take) => {
        return this.handleQueryParamsInput(skip, take);
      },
    );
    this.form = this.fb.group({
      code: undefined,
      recordCode: undefined,
      payStatus: '',
      startTime: undefined,
      endTime: undefined,
      blindBoxName: undefined,
      distributorName: undefined,
      customerName: undefined,
      customerPhone: undefined,
    });
  }

  ngOnInit() {
    this.exportLoading$ = this.exportLoadingSubj.pipe(map(res => res));
    super.ngOnInit();
    const {startTime, endTime} = this.form.getRawValue();
    this.dateRange = {startTime, endTime};
  }

  handleQueryParamsInput(skip: number, take: number) {
    const {code, recordCode, payStatus, startTime, endTime} = this.form.getRawValue();
    return {
      recordCode,
      options: {
        skip,
        take,
        filter: {
          code: code ? {eq: code} : undefined,
          status: {eq: 'opened'},
          payStatus: payStatus ? {eq: payStatus} : undefined,
          paymentAt:
            startTime && endTime
              ? {
                  between: {start: timePlusEightH(startTime), end: timePlusEightH(endTime)},
                }
              : undefined,
        },
        sort: {
          updatedAt: SortOrder.Desc,
        },
      },
    };
  }

  handleDateChange(range: {startTime?: string | Date; endTime?: string | Date}) {
    console.log(range);
    const {startTime, endTime} = range;
    this.form.controls.startTime.setValue(startTime);
    this.form.controls.endTime.setValue(endTime);
    if (startTime && endTime) {
      this.query();
    }
  }

  handleStateFilterBtn(val: string) {
    this.form.controls.payStatus.setValue(val);
    this.query();
  }

  handlePerPageChange(perPage: number) {
    this.setQueryParam({page: 1, perPage}, {replaceUrl: true});
  }

  getPickUpVariantOptions(options: {name: string}[]) {
    const mapped = options.map(o => o.name);
    return mapped.join(' ');
  }

  refundable(status: string) {
    return status === 'paid';
  }

  refundBuy(id: string) {
    this.modal
      .fromComponent(CustomInputsDialogComponent, {
        locals: {
          inputs: [
            {
              label: '拒绝原因',
              key: 'field',
              inputType: 'string',
              value: undefined,
            },
          ],
          title: '主动退款',
        },
      })
      .subscribe(rv => {
        if (rv?.length) {
          const refundReason = rv[0].value;
          this.dataService
            .mutate(REFUND_WISH_BOX_ACTIVITY_BUY, {wishBoxActivityBuyId: id, refundReason})
            .subscribe(res => {
              if (res) {
                this.notification.success('退款成功');
                this.refresh();
              }
            });
        }
      });
  }

  handleClickExport() {
    this.modal
      .dialog({
        title: '提示',
        body: '导出盲盒开盒列表数据将会话费少许时间请等待',
        buttons: [
          {
            label: '确认导出',
            returnValue: true,
            type: 'primary',
          },
          {
            label: '取消导出',
            returnValue: false,
            type: 'secondary',
          },
        ],
      })
      .subscribe(rv => {
        if (rv) {
          this.exportLoadingSubj.next(ClrLoadingState.LOADING);
          this.handleCreateExportTask()
            .then(res => {
              if (res) {
                this.notification.success('提交导出成功，导出正在执行中，请到导出记录列表查询');
              }
            })
            .catch(err => {
              console.log(err);
            })
            .finally(() => {
              this.exportLoadingSubj.next(ClrLoadingState.DEFAULT);
            });
        }
      });
  }

  handleCreateExportTask() {
    const {code, status, startTime, endTime, blindBoxName, distributorName, customerName, customerPhone} =
      this.form.getRawValue();
    return firstValueFrom(
      this.dataService.mutate<{blindBoxExportTaskCreate: any}>(BLIND_BOX_EXPORT_TASK_CREATE, {
        exportType: 'blindBoxOrder',
        blindBoxOrderQueryParam: {
          blindBoxOrderCode: code ?? undefined,
          blindBoxName: blindBoxName ?? undefined,
          blindBoxStartTime: startTime ? timePlusEightH(startTime) : undefined,
          blindBoxEndTime: endTime ? timePlusEightH(endTime) : undefined,
          distributorName: distributorName ?? undefined,
          customerName: customerName ?? undefined,
          customerPhone: customerPhone ?? undefined,
          blindBoxOrderState: status ? status : undefined,
        },
      }),
    );
  }

  handleQueryExportData(returnArr: any[] = [], page = 0) {
    this.dataService
      .query<{getBlindBoxBuys: any}>(BLIND_BOX_BUY_LIST, this.handleQueryParamsInput(page * 1000, 1000), 'no-cache')
      .mapStream(res => res.getBlindBoxBuys)
      .subscribe(res => {
        if (res) {
          const {totalItems, items} = res;
          const newRv = [...returnArr, ...items];
          const rest = totalItems - (page + 1) * 1000;
          if (rest > 0) {
            console.log('存在剩余未查数据');
            this.handleQueryExportData(newRv, ++page);
          } else {
            console.log('已获取全部数据');
            this.exportLoadingSubj.next(ClrLoadingState.DEFAULT);
            this.handleExportData(newRv);
          }
        }
      });
  }

  handleExportData(items: any[]) {
    const tempDate = items.map(m => {
      const openRecords = (m.blindBoxOpenRecords ?? [])
        .map((r: any, i: number) => {
          return '第' + (i + 1) + '次开盒商品：' + r.blindBoxItem?.productVariant?.name ?? '';
        })
        .join('；\n');
      return {
        开盒编号: m.code,
        用户: m.customer?.lastName,
        用户手机号: m.customer?.phoneNumber,
        盲盒购买价: m.blindBoxActivity?.price ? toFixedPrice(m.blindBoxActivity?.price / 100, 2) : '',
        购买时间: m.paymentAt ? new Date(m.paymentAt).toLocaleString() : '',
        助力次数: m.assistCount,
        分销员: m.distributor?.name,
        开盒记录: openRecords,
        提货商品: m.pickupOpenRecord?.blindBoxItem?.productVariant?.name,
        订单状态: this.wishboxStatus[m.status as string],
      };
    });
    const workSheet = utils.json_to_sheet(tempDate);
    const book = utils.book_new();
    utils.book_append_sheet(book, workSheet, '汇总表');
    writeFile(book, '盲盒开盒列表数据 ' + DateTime.fromJSDate(new Date()).toFormat('yyyy-MM-dd HH:mm') + '.xlsx');
  }

  handleClickExportStatistics() {
    this.modal
      .fromComponent(DateRangeDialogComponent, {
        locals: {
          title: '选择导出时间范围',
          suffix: '',
          showTimePicker: false,
          dateArgs: 'yyyy-MM-dd',
        },
        size: 'md',
        closable: true,
      })
      .subscribe(res => {
        if (res) {
          console.log(res);
          const {startTime, endTime} = res;
          if (startTime && endTime) {
            this.exportLoadingSubj.next(ClrLoadingState.LOADING);
            this.handleCreateExportStatisticsTask(startTime, endTime)
              .then(r => {
                if (r) {
                  this.notification.success('提交导出成功，导出正在执行中，请到导出记录列表查询');
                }
              })
              .catch(err => {
                console.log(err);
              })
              .finally(() => {
                this.exportLoadingSubj.next(ClrLoadingState.DEFAULT);
              });
          }
        }
      });
  }

  handleCreateExportStatisticsTask(startTime: string | Date, endTime: string | Date) {
    return firstValueFrom(
      this.dataService.mutate(BLIND_BOX_STATISTICS_EXPORT_TASK_CREATE, {
        exportType: 'blindBoxStatistics',
        blindBoxStatisticsQueryParam: {startTime, endTime},
      }),
    );
  }
}
