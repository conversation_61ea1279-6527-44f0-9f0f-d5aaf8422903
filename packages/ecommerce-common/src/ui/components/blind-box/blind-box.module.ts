import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {ComponentSharedModule} from '../../component-shared.module';
import {EcommerceCommonPipeSharedModule} from '../../ecommerce-common-pipe-shared.module';
import {BlindBoxActivityDetailComponent} from './blind-box-activity-detail/blind-box-activity-detail.component';
import {BlindBoxActivityListComponent} from './blind-box-activity-list/blind-box-activity-list.component';
import {BlindBoxConfigComponent} from './blind-box-config/blind-box-config.component';
import {BlindBoxDetailComponent} from './blind-box-detail/blind-box-detail.component';
import {BlindBoxListComponent} from './blind-box-list/blind-box-list.component';

import {ImagePickerModule} from '../image-picker/image-picker.module';
import {BlindBoxSelectDialogComponent} from './blind-box-select-dialog/blind-box-select-dialog.component';
import {BlindBoxSelectorComponent} from './blind-box-selector/blind-box-selector.component';
import {BlindBoxProbabilityDialogComponent} from './blind-box-probability-dialog/blind-box-probability-dialog.component';
import {BlindBoxStatisticsComponent} from './blind-box-statistics/blind-box-statistics.component';

import {CouponSelectDialogModule} from '../coupon-select-dialog/coupon-select-dialog.module';
import {BlindBoxOpenRecordListComponent} from './blind-box-open-record-list/blind-box-open-record-list.component';
import {BlindBoxOpenRecordDetailComponent} from './blind-box-open-record-detail/blind-box-open-record-detail.component';
import {DateRangeModule} from '../date-range/date-range.module';
import {PromotionDialogModule} from '../promotion-dialog/promotion-dialog.module';
import {CpBannerSortDialogModule} from '../custom-page/cp-banner-sort-dialog/cp-banner-sort-dialog.module';

import {BlindBoxRaffleActivityDetailComponent} from './blind-box-raffle-activity-detail/blind-box-raffle-activity-detail.component';
import {BlindBoxRaffleActivityListComponent} from './blind-box-raffle-activity-list/blind-box-raffle-activity-list.component';

import {ActivityProductSelectorModule} from '../products/activity-product-selector/activity-product-selector.module';
import {BlindBoxRaffleOpenRecordListComponent} from './blind-box-raffle-open-record-list/blind-box-raffle-open-record-list.component';
import {BlindBoxRaffleOpenRecordDetailComponent} from './blind-box-raffle-open-record-detail/blind-box-raffle-open-record-detail.component';
import {CustomInputsDialogModule} from '../dialog/custom-inputs-dialog/custom-inputs-dialog.module';

const COMPS = [
  BlindBoxActivityDetailComponent,
  BlindBoxActivityListComponent,
  BlindBoxConfigComponent,
  BlindBoxDetailComponent,
  BlindBoxListComponent,
  BlindBoxSelectDialogComponent,
  BlindBoxSelectorComponent,
  BlindBoxProbabilityDialogComponent,
  BlindBoxOpenRecordListComponent,
  BlindBoxOpenRecordDetailComponent,
  BlindBoxStatisticsComponent,
  BlindBoxRaffleActivityDetailComponent,
  BlindBoxRaffleActivityListComponent,
  BlindBoxRaffleOpenRecordDetailComponent,
  BlindBoxRaffleOpenRecordListComponent,
];

@NgModule({
  imports: [
    CommonModule,
    ComponentSharedModule,
    EcommerceCommonPipeSharedModule,
    ImagePickerModule,
    CouponSelectDialogModule,
    DateRangeModule,
    PromotionDialogModule,
    CpBannerSortDialogModule,
    ActivityProductSelectorModule,
    CustomInputsDialogModule,
  ],
  declarations: [...COMPS],
})
export class BlindBoxModule {}
