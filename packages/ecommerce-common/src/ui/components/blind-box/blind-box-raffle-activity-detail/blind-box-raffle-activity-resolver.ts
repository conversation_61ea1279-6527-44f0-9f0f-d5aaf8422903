/* eslint-disable @typescript-eslint/no-explicit-any */
import {Injectable} from '@angular/core';
import {Router} from '@angular/router';
import {BaseEntityResolver, DataService} from '@vendure/admin-ui/core';
import {WISH_BOX_ACTIVITY} from '../graphql';

@Injectable({
  providedIn: 'root',
})
export class BlindBoxRaffleActivityResolver extends BaseEntityResolver<any> {
  constructor(dataService: DataService, router: Router) {
    super(
      router,
      {
        id: '',
      },
      id =>
        dataService
          .query<any>(WISH_BOX_ACTIVITY, {
            wishBoxActivityId: id,
          })
          .refetchOnChannelChange()
          .mapStream(item => {
            console.log(item);
            const res = item.wishBoxActivity;
            return res;
          }),
    );
  }
}
