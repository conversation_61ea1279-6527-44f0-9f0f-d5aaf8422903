/* eslint-disable @typescript-eslint/no-explicit-any */
import {Component, OnInit, ChangeDetectorRef} from '@angular/core';
import {AbstractControl, FormArray, FormBuilder, FormGroup, Validators, ValidatorFn} from '@angular/forms';
import {Router, ActivatedRoute} from '@angular/router';
import {
  BaseDetailComponent,
  DataService,
  LanguageCode,
  ModalService,
  NotificationService,
  ServerConfigService,
} from '@vendure/admin-ui/core';
import {BlindBoxActivityBoxLink} from '../const';
// import {cloneDeep} from 'lodash';
import {UPSERT_WISH_BLIND_BOX_ACTIVITY} from '../graphql';
import {cloneDeep} from 'lodash';
import dayjs from 'dayjs';
import {Maybe, Product, ProductVariant, PromotionType, Scalars} from '../../../generated-admin-types';
import {MemberPriceProductSelectorComponent} from '../../member-price-activity/member-price-product-selector/member-price-product-selector.component';

enum WishBoxActivityPeriodUnit {
  Day = 'day',
  Week = 'week',
  Month = 'month',
  Year = 'year',
  Forever = 'forever',
}

enum WishBoxActivityStatus {
  Draft = 'draft',
  Opened = 'opened',
  Closed = 'closed',
}

type WishBoxActivityOpenStrategy = {
  id?: Maybe<Scalars['ID']>;
  count?: Maybe<Scalars['Int']>;
  price?: Maybe<Scalars['Int']>;
  wishProbability?: Maybe<Scalars['Int']>;
  baseProbability?: Maybe<Scalars['Int']>;
  status?: Maybe<WishBoxActivityOpenStrategyStatus>;
  sort?: Maybe<Scalars['Int']>;
};

enum WishBoxActivityOpenStrategyStatus {
  Normal = 'normal',
  Deleted = 'deleted',
}

type WishBoxActivityPrize = {
  id?: Maybe<Scalars['ID']>;
  boxType?: Maybe<WishBoxType>;
  probability?: Maybe<Scalars['Int']>;
  targetId?: Maybe<Scalars['Int']>;
  status?: Maybe<WishBoxActivityPrizeStatus>;
  productVariant?: ProductVariant;
};

enum WishBoxType {
  Free = 'free',
  Normal = 'normal',
}

enum WishBoxActivityPrizeStatus {
  Normal = 'normal',
  Deleted = 'deleted',
}

enum WishBoxActivityPrizeValidityMode {
  Relative = 'relative',
  Absolute = 'absolute',
}

type WishBoxActivity = {
  id?: Maybe<Scalars['ID']>;
  name?: Maybe<Scalars['String']>;
  remarks?: Maybe<Scalars['String']>;
  startAt?: Maybe<Scalars['DateTime']>;
  endAt?: Maybe<Scalars['DateTime']>;
  period?: Maybe<Scalars['Int']>;
  periodUnit?: Maybe<WishBoxActivityPeriodUnit>;
  periodPerLimit?: Maybe<Scalars['Int']>;
  freeBoxOpen?: Maybe<Scalars['Boolean']>;
  freeBoxPeriod?: Maybe<Scalars['Int']>;
  freeBoxPeriodUnit?: Maybe<WishBoxActivityPeriodUnit>;
  freeBoxPeriodPerLimit?: Maybe<Scalars['Int']>;
  freeBoxPerWinLimit?: Maybe<Scalars['Int']>;
  freeBoxPrizeValidityDay?: Maybe<Scalars['Int']>;
  freeBoxPrizeValidityMode?: Maybe<WishBoxActivityPrizeValidityMode>;
  freeBoxPrizeValidityExpireAt: Maybe<Scalars['DateTime']>;
  freeBoxPrizeValidityStartAt: Maybe<Scalars['DateTime']>;
  rules?: Maybe<Scalars['String']>;
  status?: Maybe<WishBoxActivityStatus>;
  wishBoxActivityOpenStrategies?: Maybe<Array<WishBoxActivityOpenStrategy>>;
  wishBoxActivityPrizes?: Maybe<Array<WishBoxActivityPrize>>;
  titleImage?: Maybe<Scalars['String']>;
  shareCover?: Maybe<Scalars['String']>;
};

@Component({
  selector: 'app-blind-box-raffle-activity-detail',
  templateUrl: './blind-box-raffle-activity-detail.component.html',
  styleUrls: ['./blind-box-raffle-activity-detail.component.scss'],
})
export class BlindBoxRaffleActivityDetailComponent extends BaseDetailComponent<any> implements OnInit {
  inited = false;
  isCopy = false;
  detailForm: FormGroup;
  boxesTableData: BlindBoxActivityBoxLink[] = [];
  boxesMap = new Map<string, {box: BlindBoxActivityBoxLink; form: FormGroup}>();

  items: WishBoxActivityPrize[] = [];
  curItemsProductValue: {product: Product; variants: ProductVariant[]}[];

  prizesMap = new Map<number, {form: FormGroup; origin: ProductVariant}>();
  freePrizesMap = new Map<number, {form: FormGroup; origin: ProductVariant}>();

  btnCount = 3;

  limitTypes = [
    {
      label: '天',
      value: 'day',
    },
    {
      label: '周',
      value: 'week',
    },
    {
      label: '月',
      value: 'month',
    },
  ];

  resolveWith: (result?: any) => void;

  get btnArr() {
    return this.detailForm.get(['btnArr']) as FormArray;
  }

  get prizeArr() {
    return this.detailForm.get(['prizeArr']) as FormArray;
  }

  get freePrizeArr() {
    return this.detailForm.get(['freePrizeArr']) as FormArray;
  }

  get disableSave() {
    return this.detailForm.invalid || this.detailForm.pristine;
  }

  get disableEdit() {
    return this.isCopy ? false : this.id;
  }

  get getassistPosterType() {
    return this.detailForm?.getRawValue()?.assistPosterType;
  }

  get getOpenBoxMode() {
    return this.detailForm?.getRawValue()?.openBoxMode;
  }

  constructor(
    route: ActivatedRoute,
    router: Router,
    serverConfig: ServerConfigService,
    protected dataService: DataService,
    private fb: FormBuilder,
    private ref: ChangeDetectorRef,
    private modal: ModalService,
    private notification: NotificationService,
  ) {
    // this.ngOnIniç
    super(route, router, serverConfig, dataService);
    this.detailForm = this.fb.group(
      {
        id: undefined,
        name: [undefined, Validators.required],
        remarks: undefined,
        rules: undefined,
        dateRange: {startTime: undefined, endTime: undefined},
        period: [undefined, Validators.required],
        periodUnit: ['day', Validators.required],
        periodPerLimit: [undefined, Validators.required],
        freeBoxOpen: false,
        freeBoxPeriod: [undefined, Validators.required],
        freeBoxPeriodUnit: ['day', Validators.required],
        freeBoxPeriodPerLimit: [undefined, Validators.required],
        freePrizeIds: [[]],
        claimRestrictionRadio: [false, Validators.required],
        freeBoxPerWinLimit: undefined,
        freeBoxPrizeValidityDay: undefined,
        freePrizeDateRange: {startTime: undefined, endTime: undefined},
        freeBoxPrizeValidityMode: ['relative', Validators.required],
        btnArr: this.fb.array([]),
        freePrizeArr: this.fb.array([]),
        prizeArr: this.fb.array([]),
        titleImage: undefined,
        shareCover: undefined,
      },
      {
        validators: this.cusDetailFormValidator(),
      },
    );
  }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.isCopy = params['copy'];
    });
    this.init();
  }

  protected setFormValues(entity: WishBoxActivity, languageCode: LanguageCode): void {
    // if (!entity?.id) {
    //   return;
    // }
    const {
      id,
      name,
      remarks,
      startAt,
      endAt,
      rules,
      period,
      periodUnit,
      periodPerLimit,
      freeBoxOpen,
      freeBoxPeriod,
      freeBoxPeriodUnit,
      freeBoxPeriodPerLimit,
      freeBoxPerWinLimit,
      freeBoxPrizeValidityDay,
      freeBoxPrizeValidityMode,
      freeBoxPrizeValidityStartAt,
      freeBoxPrizeValidityExpireAt,
      wishBoxActivityOpenStrategies,
      wishBoxActivityPrizes,
      titleImage,
      shareCover,
    } = entity ?? {};
    this.detailForm.patchValue({
      id,
      name,
      remarks,
      dateRange: {startTime: startAt, endTime: endAt},
      freePrizeDateRange: {startTime: freeBoxPrizeValidityStartAt, endTime: freeBoxPrizeValidityExpireAt},
      freeBoxPrizeValidityMode: freeBoxPrizeValidityMode || 'relative',
      rules,
      period,
      periodUnit: periodUnit ?? WishBoxActivityPeriodUnit.Week,
      periodPerLimit,
      freeBoxOpen,
      freeBoxPeriod,
      freeBoxPeriodUnit: freeBoxPeriodUnit ?? WishBoxActivityPeriodUnit.Week,
      freeBoxPeriodPerLimit,
      claimRestrictionRadio: freeBoxPerWinLimit === -1 ? false : true,
      freeBoxPerWinLimit: freeBoxPerWinLimit === -1 ? undefined : freeBoxPerWinLimit,
      freeBoxPrizeValidityDay: freeBoxPrizeValidityDay || undefined,
      titleImage,
      shareCover,
    });
    this.setBtnsFormArrayValue(cloneDeep(wishBoxActivityOpenStrategies ?? []));
    this.setPrizesFromArrayValue(cloneDeep(wishBoxActivityPrizes ?? []));

    setTimeout(() => {
      this.inited = true;
      this.detailForm.markAsPristine();
      this.ref.detectChanges();
    });

    // console.log(this.boxesTableData, this.curRounds, this.detailForm);
  }

  setBtnsFormArrayValue(btns: WishBoxActivityOpenStrategy[]) {
    const tempArr = [...btns, ...Array.from({length: 3}, _ => undefined)];
    tempArr.length = 3;
    tempArr?.forEach((b, i) => {
      const {id, count, price, baseProbability, wishProbability} = b ?? {};
      const tempBtnGroup = this.fb.group({
        id,
        sort: i,
        count: [count, Validators.required],
        price: [price, Validators.required],
        baseProbability: [baseProbability, Validators.required],
        wishProbability: [wishProbability, Validators.required],
      });
      this.btnArr.push(tempBtnGroup);
    });
  }

  setPrizesFromArrayValue(prizes: WishBoxActivityPrize[]) {
    const normalPrize = prizes.filter(p => p.boxType === WishBoxType.Normal);
    const freePrize = prizes.filter(p => p.boxType === WishBoxType.Free);

    this.items = normalPrize;
    normalPrize.forEach(p => {
      const {id, probability, targetId} = p;
      const tempPrizeGroup = this.fb.group({
        id,
        boxType: WishBoxType.Normal,
        probability,
        targetId,
        status: p.status || WishBoxActivityPrizeStatus.Normal,
      });
      this.prizeArr?.push(tempPrizeGroup);
      this.prizesMap.set(p.targetId as number, {origin: p.productVariant!, form: tempPrizeGroup});
    });

    const tempArr: string[] = [];
    freePrize.forEach(p => {
      const {id, probability, targetId, productVariant} = p;
      tempArr.push(productVariant?.product?.id as string);
      const tempPrizeGroup = this.fb.group({
        id,
        boxType: WishBoxType.Free,
        probability,
        targetId,
        status: p.status || WishBoxActivityPrizeStatus.Normal,
      });
      this.freePrizeArr?.push(tempPrizeGroup);
      this.freePrizesMap.set(p.targetId as number, {origin: p.productVariant!, form: tempPrizeGroup});
    });
    this.detailForm.controls.freePrizeIds.setValue(tempArr);
  }

  getOptionsName(v: ProductVariant) {
    return v.options?.map(o => o.name).join(' ');
  }

  getSkuState(v: ProductVariant) {
    return v.enabled ? (v.stockOnHand - v.stockAllocated > 0 ? '正常' : '0库存') : '已失效';
  }

  cusDetailFormValidator(): ValidatorFn {
    return (ctrl: AbstractControl) => {
      const {
        dateRange,
        claimRestrictionRadio,
        freeBoxPerWinLimit,
        freePrizeIds,
        freeBoxOpen,
        // freeBoxPrizeValidityDay,
        // freeBoxPrizeValidityMode,
        // freePrizeDateRange,
      } = (ctrl as FormGroup).getRawValue();
      const {startTime, endTime} = dateRange;
      if (!startTime || !endTime || dayjs(startTime).isAfter(dayjs(endTime))) {
        return {dateError: true};
      }
      if (claimRestrictionRadio && freeBoxPerWinLimit < 1) {
        return {requireFreeBoxPerWinLimit: true};
      }
      if (freeBoxOpen) {
        if (!freePrizeIds.length) {
          return {requireFreePrizes: true};
        }
        // if (freeBoxPrizeValidityMode === 'absolute' && !) {
        //   //
        // }
      }
      return null;
    };
  }

  descriptionValidator(): ValidatorFn {
    return (ctrl: AbstractControl) => {
      const splitRichText = ctrl.value?.split('</p><p>').join('').split(' ').join('');
      if (!splitRichText || splitRichText === '<p></p>') {
        return {requireDescription: true};
      } else {
        return null;
      }
    };
  }

  probabilitiesValidator(): ValidatorFn {
    return (ctrl: AbstractControl) => {
      const vals = (ctrl as FormArray).getRawValue();
      // if (vals.every(e => !e.targetProbability)) {
      //   return {mustHaveOneProbability: true};
      // }
      const valsTotal = vals.reduce((sum, i) => {
        return sum + i.targetProbability;
      }, 0);
      if (valsTotal > 100) {
        return {sumMustLessHundred: true};
      }
      return null;
    };
  }

  handleDataConversion(items: WishBoxActivityPrize[]) {
    if (!items?.length) {
      return [];
    }
    const productIds = Array.from(new Set(items.map(i => i?.productVariant?.product?.id)));
    // const skuIds = items.map(i => i.id);
    const tempArr = productIds.map(pId => {
      const temp = items.filter(item => {
        return item.productVariant?.product?.id === pId;
      });
      return {
        product: {...temp?.[0]?.productVariant?.product, id: pId} as Product,
        variants: temp.map(m => m.productVariant ?? ({id: m.targetId} as unknown as ProductVariant)),
      };
    });
    return tempArr;
  }

  openSkuSelectDialog() {
    this.curItemsProductValue = this.handleDataConversion(this.items);
    const inputValue = this.curItemsProductValue?.map(m => {
      return {
        id: '',
        product: m.product,
        memberPriceProductVariant: m.variants.map(v => ({
          id: v.id,
          productVariant: v,
          memberPriceAmount: 0,
          memberDiscount: 0,
          discountType: '',
        })),
      };
    });

    const disableSkuFn = (v: ProductVariant) => {
      return !v.enabled || v.stockOnHand - v.stockAllocated <= 0;
    };

    this.modal
      .fromComponent(MemberPriceProductSelectorComponent, {
        locals: {
          memberPriceProducts: inputValue ?? [],
          promotionType: PromotionType.BlindBox,
          disableSkuFn: disableSkuFn,
          // queryFilter: inputFilter,
        },
        closable: true,
        size: 'xl',
      })
      .subscribe(res => {
        //
        console.log(res);
        if (res?.length) {
          // 记录试手气商品sku与盲盒奖池商品sku重复的数据
          const duplicateSkus: string[] = [];
          const resSkus = res
            .map(p => {
              if (p.delete) {
                return [];
              } else {
                return p.skus.filter(s => s.checked);
              }
            })
            ?.flat();
          const resDeletedSkus = res
            .map(p => {
              if (p.delete) {
                return p.skus;
              } else {
                return p.skus.filter(s => !s.checked);
              }
            })
            ?.flat();
          const prevPrizeSkuIds = this.prizeArr.getRawValue().map((m: any) => m.targetId);
          console.log(resSkus);
          // 处理新增
          resSkus.forEach(p => {
            // 试手气页面免费赠送商品只存在一个sku
            const skuId = Number(p.id);
            // 试手气商品sku与盲盒奖池商品sku不能重复
            const isDuplicate =
              this.freePrizesMap.get(skuId)?.form?.getRawValue()?.status === WishBoxActivityPrizeStatus.Normal;
            if (isDuplicate) {
              duplicateSkus.push(this.freePrizesMap.get(skuId)!.origin?.name);
            } else {
              if (!prevPrizeSkuIds.includes(skuId)) {
                const tempGroup = this.fb.group({
                  id: undefined,
                  targetId: skuId,
                  probability: undefined,
                  boxType: WishBoxType.Normal,
                  status: WishBoxActivityPrizeStatus.Normal,
                });
                // console.log(tempGroup);
                this.prizeArr.push(tempGroup);
                this.prizesMap.set(skuId, {form: tempGroup, origin: p});

                const tempItem = {
                  ...tempGroup.getRawValue(),
                  productVariant: p,
                } as WishBoxActivityPrize;
                this.items.push(tempItem);
              } else {
                const findInMap = this.prizesMap.get(skuId);
                // if (findInMap?.form?.getRawValue()?.id) {
                findInMap!.form.controls.status.setValue(WishBoxActivityPrizeStatus.Normal);
                // }
                if (!this.items.find(f => f.targetId === skuId)) {
                  this.items.push({
                    ...findInMap!.form.getRawValue(),
                    productVariant: p,
                  });
                }
              }
            }
          });

          // 处理删除
          const curPizeSkuIds = this.prizeArr.getRawValue().map(p => p.targetId);
          resDeletedSkus.forEach(p => {
            const pId = Number(p.id);
            const findInMap = this.prizesMap.get(pId);
            if (curPizeSkuIds.includes(pId)) {
              if (findInMap?.form?.getRawValue()?.id) {
                findInMap.form.controls.status.setValue(WishBoxActivityPrizeStatus.Deleted);
              } else {
                const findIndex = this.prizeArr.controls.findIndex(ctrl => ctrl.getRawValue().targetId === pId);
                this.prizeArr.removeAt(findIndex);
                this.prizesMap.delete(pId);
              }
              const findIndex = this.items.findIndex(f => f.targetId === pId);
              if (findIndex !== -1) {
                this.items.splice(findIndex, 1);
              }
            }
          });

          if (duplicateSkus.length) {
            const tempStr = '以下规格在试手气奖池中已存在，不能重复添加：\n' + duplicateSkus.join('\n');
            this.notification.warning(tempStr);
          }

          // console.log(tempIds, tempDeletes, curIds, newIds, this.detailForm);
          this.detailForm.markAsDirty();
          // this.queryProducts(newIds);
        }
      });
  }

  deletePrize(i: string) {
    const id = Number(i);
    const findIndex = this.items.findIndex(fi => fi.targetId === id);
    this.items.splice(findIndex, 1);

    const findForm = this.prizesMap.get(id)?.form;
    if (findForm?.getRawValue()?.id) {
      findForm.controls.status.setValue(WishBoxActivityPrizeStatus.Deleted);
    } else {
      const findInControlArr = this.prizeArr.controls.findIndex(fg => {
        const fv = fg.getRawValue();
        return fv.targetId === id;
      });
      this.prizesMap.delete(id);
      this.prizeArr.removeAt(findInControlArr);
    }
  }

  disableCheckFn() {
    return (p: Product) => {
      return {
        disabled: p.variants.length !== 1,
        options: {
          message: '仅可选商品规格数量为1的商品',
        },
      };
    };
  }

  getFreePrizeSelectOrigin(prods: Product[]) {
    if (prods?.length) {
      // 试手气商品sku与盲盒奖池商品sku不能重复
      const duplicateSkus: string[] = [];
      const normalPrize = cloneDeep(this.items);
      prods = prods.filter(f => {
        const skuId = Number(f.variants?.[0]?.id);
        const findDuplicate = normalPrize.find(n => n.targetId === skuId);
        if (findDuplicate) {
          duplicateSkus.push(findDuplicate.productVariant?.name || '');
          return false;
        } else {
          return true;
        }
      });
      if (duplicateSkus.length) {
        // 如果存在重复的 需要重新刷新组件绑定值 以刷新列表的数据 将重复的规格行去掉
        this.detailForm.controls.freePrizeIds.setValue(prods.map(p => p.id));
        const tempStr = '以下规格在盲盒奖池中已存在，不能重复添加：\n' + duplicateSkus.join('\n');
        this.notification.warning(tempStr);
        return;
      }

      const prevFreePrizeSkuIds = this.freePrizeArr.getRawValue().map((m: any) => m.targetId);
      prods.forEach(p => {
        // 试手气页面免费赠送商品只存在一个sku
        const skuId = Number(p.variants?.[0]?.id);
        if (skuId) {
          // 处理新增
          if (!prevFreePrizeSkuIds.includes(skuId)) {
            const tempGroup = this.fb.group({
              id: undefined,
              targetId: skuId,
              probability: undefined,
              boxType: WishBoxType.Free,
              status: WishBoxActivityPrizeStatus.Normal,
            });
            // console.log(tempGroup);
            this.freePrizeArr.push(tempGroup);
            this.freePrizesMap.set(skuId, {form: tempGroup, origin: p.variants?.[0]});
          } else {
            const findInMap = this.freePrizesMap.get(skuId);
            if (findInMap?.form?.getRawValue()?.id) {
              findInMap.form.controls.status.setValue(WishBoxActivityPrizeStatus.Normal);
            }
          }
        }
      });

      // 处理删除
      const newFreePrizeSkuIds = prods.map(p => Number(p.variants?.[0]?.id)).filter(Boolean);
      prevFreePrizeSkuIds.forEach(pId => {
        const findInMap = this.freePrizesMap.get(pId);
        if (!newFreePrizeSkuIds.includes(pId)) {
          if (findInMap?.form?.getRawValue()?.id) {
            findInMap.form.controls.status.setValue(WishBoxActivityPrizeStatus.Deleted);
          } else {
            const findIndex = this.freePrizeArr.controls.findIndex(ctrl => ctrl.getRawValue().targetId === pId);
            this.freePrizeArr.removeAt(findIndex);
            this.freePrizesMap.delete(pId);
          }
        } else {
          if (findInMap?.form?.getRawValue()?.id) {
            findInMap.form.controls.status.setValue(WishBoxActivityPrizeStatus.Normal);
          }
        }
      });
    }
  }

  save(isCreate = false) {
    // console.log(this.detailForm);
    const isNew = isCreate || this.isCopy;
    const fv = this.detailForm.getRawValue();
    const {
      id,
      dateRange,
      freePrizeDateRange,
      claimRestrictionRadio,
      freeBoxPerWinLimit,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      freePrizeIds,
      prizeArr,
      freePrizeArr,
      btnArr,
      ...rest
    } = fv;
    const strategies = btnArr.map((m: any) => {
      const {id: sId, ...sRest} = m;
      return {
        ...sRest,
        id: isNew ? undefined : sId,
      };
    });
    const prizes = [...prizeArr, ...freePrizeArr].map((m: any) => {
      const {id: pId, ...pRest} = m;
      return {
        ...pRest,
        id: isNew ? undefined : pId,
      };
    });
    const input = {
      id: isNew ? undefined : id,
      status: 'opened',
      startAt: dateRange.startTime,
      endAt: dateRange.endTime,
      freeBoxPrizeValidityStartAt: freePrizeDateRange.startTime,
      freeBoxPrizeValidityExpireAt: freePrizeDateRange.endTime,
      freeBoxPerWinLimit: claimRestrictionRadio ? freeBoxPerWinLimit : -1,
      wishBoxActivityOpenStrategies: strategies,
      wishBoxActivityPrizes: prizes,
      ...rest,
    };
    // console.log(input);
    this.dataService.mutate(UPSERT_WISH_BLIND_BOX_ACTIVITY, {input}).subscribe(res => {
      // console.log(res);
      if (res) {
        this.notification.success('操作成功');
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        this.router.navigate(['../'], {relativeTo: this.route, replaceUrl: true});
      }
    });
  }
}
