<vdr-page-block *ngIf="inited">
  <vdr-action-bar>
    <vdr-ab-left></vdr-ab-left>
    <vdr-ab-right>
      <button
        *vdrIfPermissions="['CreateWishBoxActivity', 'UpdateWishBoxActivity']"
        [disabled]="disableSave"
        class="btn btn-primary"
        (click)="save()"
      >
        保存
      </button>
      <button class="btn" [routerLink]="'../'">取消</button>
    </vdr-ab-right>
  </vdr-action-bar>

  <div class="card">
    <div class="card-header">基本信息</div>
    <div class="card-block">
      <form [formGroup]="detailForm">
        <app-form-field label="盲盒活动名称（最多输入10个字）" for="name">
          <input id="name" type="text" formControlName="name" [maxLength]="10" />
        </app-form-field>

        <app-form-field label="名称备注（仅限内部人员查阅，最多输入30个字）">
          <input id="remarks" type="text" formControlName="remarks" />
        </app-form-field>

        <app-form-field label="活动时间">
          <date-range [isActivityRange]="true" formControlName="dateRange"></date-range>
        </app-form-field>

        <app-form-field label="标题图片">
          <image-picker formControlName="titleImage"></image-picker>
        </app-form-field>

        <app-form-field label="分享封面图片">
          <image-picker formControlName="shareCover"></image-picker>
        </app-form-field>

        <vdr-form-field label="购买盲盒次数限制" class="mb3">
          <div>
            <div class="c-999 mb2">在规定的时间周期里面，每个用户最多能购买开盒的次数</div>
            <label-value label="周期时间">
              <div class="flex items-center">
                <input type="number" formControlName="period" [min]="1" appInteger />
                <ng-select
                  [items]="limitTypes"
                  formControlName="periodUnit"
                  [searchable]="false"
                  [clearable]="false"
                  bindLabel="label"
                  bindValue="value"
                  class="ml2"
                ></ng-select>
              </div>
            </label-value>
            <label-value label="出现次数" class="mt2">
              <input type="number" formControlName="periodPerLimit" [min]="1" appInteger />次
            </label-value>
          </div>
        </vdr-form-field>

        <app-form-field label="按钮设置">
          <div class="clr-row">
            <ng-container *ngFor="let btn of btnArr.controls; index as i">
              <div class="clr-col btn-config-container" [formGroup]="btn">
                <div class="c-header">
                  按钮 {{ i + 1 }}{{ !i ? '（左边）' : btnArr.length === i + 1 ? '（右边）' : '（中间）' }}
                </div>
                <label-value label="拆开次数" class="mt2">
                  <input type="number" formControlName="count" />
                </label-value>
                <label-value label="盲盒活动价格" class="mt2">
                  <price-input formControlName="price"></price-input>
                </label-value>
                <label-value label="开出心愿商品概率" class="mt2">
                  <input type="number" formControlName="wishProbability" />
                </label-value>
                <label-value label="开出基础商品概率" class="mt2">
                  <input type="number" formControlName="baseProbability" />
                </label-value>
              </div>
            </ng-container>
          </div>
        </app-form-field>

        <app-form-field label="奖池奖励选择">
          <button class="btn btn-link" (click)="openSkuSelectDialog()">选择参与盲盒的商品sku</button>
        </app-form-field>
        <div *ngIf="items?.length">
          <table class="table">
            <thead>
              <tr>
                <th class="left align-middle">商品名称</th>
                <th class="left align-middle">sku规格名称</th>
                <th class="left align-middle">sku销售价</th>
                <th class="left align-middle">库存</th>
                <th class="left align-middle">状态</th>
                <th class="align-middle">操作</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let item of items; let i = index">
                <tr *ngIf="prizesMap?.get(item.targetId)?.form as itemForm" [formGroup]="itemForm">
                  <td class="left align-middle">
                    <div>
                      <condition-tooltip
                        [content]="item.productVariant?.product?.name"
                        [contentRow]="2"
                      ></condition-tooltip>
                    </div>
                  </td>
                  <td class="left align-middle">
                    <div class="flex-col justify-evenly" style="gap: 4px">
                      <condition-tooltip
                        [content]="getOptionsName(item.productVariant)"
                        [contentWidth]="'5.5rem'"
                      ></condition-tooltip>
                    </div>
                  </td>
                  <td class="left align-middle">
                    {{ item.productVariant?.price | tokenCNY }}
                  </td>
                  <td class="left align-middle">
                    {{ item.productVariant?.stockOnHand - item.productVariant?.stockAllocated }}
                  </td>
                  <td class="left align-middle">
                    {{ getSkuState(item.productVariant) }}
                  </td>
                  <td class="align-middle no-wrap operation-td">
                    <button type="button" class="btn-link is-danger" (click)="deletePrize(item.targetId)">
                      <clr-icon shape="trash" class="is-danger"></clr-icon>
                      删除
                    </button>
                  </td>
                </tr>
              </ng-container>
            </tbody>
          </table>
        </div>
      </form>
    </div>
  </div>

  <div class="card">
    <div class="card-header">试手气页面设置</div>
    <div class="card-block" [formGroup]="detailForm">
      <label-value label="是否开启试手气页面">
        <clr-toggle-wrapper>
          <input type="checkbox" formControlName="freeBoxOpen" id="" clrToggle />
        </clr-toggle-wrapper>
      </label-value>
      <vdr-form-field label="试手气页面设置" class="mb3">
        <div>
          <div class="c-999 mb2">在规定时间周期里面，每个用户最多能出现试手气的次数</div>
          <label-value label="周期时间">
            <div class="flex items-center">
              <input type="number" formControlName="freeBoxPeriod" [min]="1" appInteger />
              <ng-select
                [items]="limitTypes"
                formControlName="freeBoxPeriodUnit"
                [clearable]="false"
                [searchable]="false"
                bindLabel="label"
                bindValue="value"
                class="ml2"
              ></ng-select>
            </div>
          </label-value>
          <label-value label="出现次数" class="mt2">
            <input type="number" formControlName="freeBoxPeriodPerLimit" [min]="1" appInteger />次
          </label-value>
        </div>
      </vdr-form-field>

      <!-- <label-value label="使用时间">
        <div class="flex items-center">
          <input type="number" formControlName="assistValidityPeriod" [min]="1" appInteger />小时
        </div>
      </label-value>
      <div class="c-999">
        开盒后剩余的邀请助力时间，超过有效时间后,不可再助力。再发起一轮助力时，有效时间重新开始倒计时
      </div> -->
      <app-form-field label="使用时间" class="mt3">
        <div class="flex-col">
          <div class="flex items-center justify-start mb2 no-wrap">
            <clr-radio-container style="width: 24px">
              <clr-radio-wrapper>
                <input type="radio" formControlName="freeBoxPrizeValidityMode" value="relative" clrRadio />
                <label> </label>
              </clr-radio-wrapper>
            </clr-radio-container>
            自领券起
            <input type="number" formControlName="freeBoxPrizeValidityDay" [min]="1" style="width: 100px" />天内有效
          </div>
          <div class="flex items-center justify-start mb2">
            <clr-radio-container style="width: 24px">
              <clr-radio-wrapper>
                <input type="radio" formControlName="freeBoxPrizeValidityMode" value="absolute" clrRadio />
                <label> </label>
              </clr-radio-wrapper>
            </clr-radio-container>
            <date-range formControlName="freePrizeDateRange"></date-range>
          </div>
        </div>
      </app-form-field>

      <app-form-field label="获得兑换券次数设置" class="mt3">
        <div class="flex-col">
          <div class="flex items-center justify-start mb2">
            <clr-radio-container
              ><clr-radio-wrapper>
                <input type="radio" formControlName="claimRestrictionRadio" [value]="false" clrRadio />
                <label>每人获得次数不限 </label>
              </clr-radio-wrapper></clr-radio-container
            >
          </div>
          <div class="flex items-center justify-start mb2">
            <clr-radio-container
              ><clr-radio-wrapper>
                <input type="radio" formControlName="claimRestrictionRadio" [value]="true" clrRadio />
                <label style="white-space: nowrap">每人最多可获</label>
              </clr-radio-wrapper></clr-radio-container
            >
            <input type="number" formControlName="freeBoxPerWinLimit" [min]="1" />次
          </div>
        </div>
        <!-- <clr-radio-container>
          <clr-radio-wrapper>
            <input type="radio" formControlName="isGiftForUnconvertedUser" [value]="true" clrRadio />
            <label>发放奖励</label>
          </clr-radio-wrapper>
          <clr-radio-wrapper>
            <input type="radio" formControlName="isGiftForUnconvertedUser" [value]="false" clrRadio />
            <label>不发放奖励</label>
          </clr-radio-wrapper>
        </clr-radio-container> -->
      </app-form-field>

      <!-- <div class="mt2 flex items-center nowrap">
        <label>试手气抽奖奖励选择</label>
        <app-activity-product-selector
          formControlName="luckProductIds"
          [notActivity]="true"
          [disableVirtualTypes]="['coupon', 'memberCard']"
          triggerLabel="指定商品"
        ></app-activity-product-selector>
      </div> -->

      <app-form-field label="试手气抽奖奖励选择">
        <app-activity-product-selector
          formControlName="freePrizeIds"
          [notActivity]="true"
          [disableVirtualTypes]="['coupon', 'memberCard']"
          [disableCheckFn]="disableCheckFn()"
          triggerLabel="指定商品"
          (getProductsOrigin)="getFreePrizeSelectOrigin($event)"
        ></app-activity-product-selector>
      </app-form-field>
    </div>
  </div>

  <div [formGroup]="detailForm">
    <app-form-field label="活动规则">
      <vdr-rich-text-editor formControlName="rules"></vdr-rich-text-editor>
    </app-form-field>
  </div>
</vdr-page-block>
