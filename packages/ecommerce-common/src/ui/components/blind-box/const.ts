import {
  ComponentType,
  ComponentValue,
  Coupon,
  Customer,
  Distributor,
  ProductVariant,
} from '../../generated-admin-types';
import {DataService, ModalService, NotificationService} from '@vendure/admin-ui/core';
import {BLIND_BOX_BUY_REFUND} from './graphql';

enum OpenBoxMode {
  AssistOnce = 'assistOnce',
  AssistMultiple = 'assistMultiple',
}

type ID = string;
type DateTime = string | undefined;
type Int = number;

export enum LimitType {
  unlimited,
  day,
  week,
  month,
  year,
  forever,
  order,
}

export enum BlindBoxItemType {
  product = 'product',
}

export type BlindBoxItem = {
  id?: ID;
  createdAt?: DateTime;
  updatedAt?: DateTime;
  blindBox?: BlindBox;
  blindBoxId?: ID;
  productVariant: ProductVariant;
  targetId: ID;
  type: BlindBoxItemType;
  baseProbability: Int;
  isWishItem?: Boolean;
  openBoxImageUrl?: String;
};

export type BlindBox = {
  id: ID;
  createdAt: DateTime;
  updatedAt: DateTime;
  wishItemId: ID;
  price: Int;
  name: String;
  remarks: String;
  description: String;
  img: String;
  blindBoxItems: BlindBoxItem[];
  wishBlindBoxItem: BlindBoxItem;
};

export type ProductProbability = {
  blindBoxItemId: ID;
  targetProbability: Int;
};

export type BlindBoxActivityBoxLink = {
  id?: ID;
  createdAt?: DateTime;
  updatedAt?: DateTime;
  blindBoxActivity?: BlindBoxActivity;
  blindBoxActivityId?: ID;
  blindBox?: BlindBox;
  blindBoxId: ID;
  maxAssistLimit?: Int;
  assistLimits: Array<number>;
  productProbabilities: Array<ProductProbability[]>;
};

export type BlindBoxActivity = {
  id: ID;
  createdAt: DateTime;
  updatedAt: DateTime;
  name: String;
  remarks: String;
  description: String;
  startAt: DateTime;
  endAt: DateTime;
  enabled: Boolean;
  statue: string;
  isFreeShipping: Boolean;
  freeShippingThreshold: Int;
  price: Int;
  baseBlindBoxProbability: Int;
  baseBlindBox: BlindBox;
  baseBlindBoxId: ID;
  blindBoxActivityBoxLinks: BlindBoxActivityBoxLink[];
  assistLimit: Int;
  assistPagePoster: ComponentValue;
  assistPosterType: ComponentType;
  openBoxMode: OpenBoxMode;
  stock: Int;
};

export type BlindBoxActivityLimitConfig = {
  id: string;
  createdAt: DateTime;
  updatedAt: DateTime;
  maxBoxPurchaseLimit: Int;
  maxAssistLimit: Int;
  maxRefundLimit: Int;
  assistValidityPeriod: Int;
  purchaseLimitPeriod: LimitType;
  purchaseLimit: Int;
  assistLimitPeriod: LimitType;
  assistLimit: Int;
  refundLimitPeriod: LimitType;
  refundLimit: Int;
};

export type AssistGift = {
  id?: ID;
  createdAt?: DateTime;
  updatedAt?: DateTime;
  giftName?: String;
  giftImage?: String;
  type?: string;
  coupon?: Coupon;
  targetId?: ID;
};

export type AssistGiftConfig = {
  id: ID;
  isGiftForConvertedUser: Boolean;
  isGiftForUnconvertedUser: Boolean;
  convertedGift: AssistGift;
  convertedGiftId: ID;
  nonConvertedGiftLevels: Array<{convertedGiftId: ID; level: Int}>;
};

export type BlindBoxOpenRecord = {
  id: ID;
  createdAt: DateTime;
  updatedAt: DateTime;
  blindBoxBuy: BlindBoxBuy;
  blindBoxBuyId: ID;
  blindBox: BlindBox;
  blindBoxId: ID;
  customer: Customer;
  customerId: ID;
  blindBoxItem: BlindBoxItem;
  resultId: ID;
  isWishedItem: Boolean;
  openedAt: DateTime;
  assistCount: Int;
  currentAllProbability: JSON;
  isFreeShipping: Boolean;
};

export type BlindBoxRefundRecord = {
  id: ID;
  createdAt: DateTime;
  updatedAt: DateTime;
  customer: Customer;
  customerId: ID;
  blindBoxBuy: BlindBoxBuy;
  blindBoxBuyId: ID;
  refundAmount: Int;
  refundAt: DateTime;
};

export type BlindBoxBuy = {
  id: ID;
  createdAt: DateTime;
  updatedAt: DateTime;
  code: String;
  customer: Customer;
  customerId: ID;
  price: Int;
  wishBlindBoxItem: BlindBoxItem;
  blindBoxOpenRecords: [BlindBoxOpenRecord];
  baseBlindBox: BlindBox;
  baseBlindBoxId: ID;
  wishBlindBox: BlindBox;
  wishBlindBoxId: ID;
  paymentMetadata: JSON;
  status: string;
  payStatus: string;
  paymentAt: DateTime;
  assistLimit: Int;
  assistCount: Int;
  assistExpireAt: DateTime;
  blindBoxActivity: BlindBoxActivity;
  blindBoxActivityId: ID;
  distributor: Distributor;
  distributorId: ID;
  pickupOpenRecord: BlindBoxOpenRecord;
  pickupOpenRecordId: ID;
  pickupAt: DateTime;
  blindBoxRefundRecord: BlindBoxRefundRecord;
};

export const blindboxStatusObject: {[key: string]: string} = {
  pendingOpen: '待开启',
  opened: '已开启',
  assisting: '助力中',
  delivered: '已提货',
  pendingDelivery: '待提货',
  refunded: '已退款',
};

export const wishboxStatusObject: {[key: string]: string} = {
  pendingPay: '待付款',
  paid: '已付款',
  refunded: '已退款',
};

export const calculateProbabilities = (
  products: {
    targetId: string;
    lockProbability: boolean;
    baseProbability: number | null;
    price: number;
  }[],
) => {
  /**
   * 计算盲盒内产品的概率分布。
   *
   * 参数:
   *   products: Array<Object>，包含每个产品的信息，每个对象有这些属性：
   *     - targetId: Number/产品ID
   *     - price: Number/产品价格
   *     - baseProbability: (Number | null)/指定概率
   *
   * 返回:
   *   返回包含每个产品的最终概率的列表。
   */

  // 验证输入数据
  let totalSpecifiedProb = 0;

  products.forEach(product => {
    const specifiedProb = product.baseProbability;
    if (specifiedProb !== null && specifiedProb !== undefined && product.lockProbability) {
      if (specifiedProb < 0 || specifiedProb > 100) {
        throw new Error(`产品ID ${product.targetId} 的指定概率 ${specifiedProb} 不合法（范围应是 0-100%）。`);
      }
      totalSpecifiedProb += specifiedProb;
    }
  });

  if (totalSpecifiedProb > 100) {
    throw new Error('锁定概率的总和不能超过 100%');
  }

  // 剩余概率
  const remainingProb = 100 - totalSpecifiedProb;

  // 找到未指定概率的产品
  const unspecifiedProducts = products.filter(product => !product.lockProbability);

  // 计算未指定概率产品的反比例权重
  const inverseTotalPrice = unspecifiedProducts.reduce((sum, product) => sum + 100 / product.price, 0);

  // 计算未指定概率的产品的反比例概率
  unspecifiedProducts.forEach(product => {
    const inverseWeight = 100 / product.price / inverseTotalPrice; // 权重按反价格比例计算
    product.baseProbability = Number((inverseWeight * remainingProb).toFixed(2)); // 按权重分配剩余概率
  });

  // 因为总概率是100%，4舍5入取整可能会导致1精度丢失问题；所以要做最后演算，判断未指定概率的产品分配比例后的比例总和，是否等于剩余概率
  const remainingTotalProb = unspecifiedProducts.reduce((sum, product) => sum + product.baseProbability!, 0);
  const probError = remainingTotalProb - remainingProb;
  if (probError !== 0) {
    const sortedProducts = unspecifiedProducts.sort((a, b) => a.price - b.price);
    // sortedProducts[sortedProducts.length - 1].baseProbability! -= probError;
    const temp = sortedProducts[sortedProducts.length - 1].baseProbability;
    sortedProducts[sortedProducts.length - 1].baseProbability = Number((temp! - probError).toFixed(2));
  }

  // 分配最终概率
  // products.forEach(product => {
  //   if (product.baseProbability !== null && product.baseProbability !== undefined) {
  //     product.final_prob = product.specified_prob; // 使用指定概率
  //   } else {
  //     product.final_prob = product.calculated_prob; // 使用计算的概率
  //   }
  // });
  console.log(products);
  return products;
};

export const getCurrentAssistProgress = (buyInfo: BlindBoxBuy) => {
  if (buyInfo?.blindBoxOpenRecords?.length) {
    return buyInfo.blindBoxOpenRecords.length === 1 && buyInfo.blindBoxOpenRecords[0].isWishedItem
      ? '--'
      : `${buyInfo.assistCount}/${buyInfo.assistLimit}`;
  } else {
    return '--';
  }
};

export const refundableStatus = ['pendingOpen', 'opened', 'assisting', 'pendingDelivery'];

export const refundBlindboxBuy = (
  id: string,
  dataService: DataService,
  dialog: ModalService,
  notification: NotificationService,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  resolve?: () => any,
) => {
  dialog
    .dialog({
      title: '提示',
      body: '是否要退款该订单？',
      buttons: [
        {
          label: '确认',
          type: 'danger',
          returnValue: true,
        },
        {
          label: '取消',
          type: 'secondary',
          returnValue: false,
        },
      ],
    })
    .subscribe(rv => {
      if (rv) {
        dataService.mutate(BLIND_BOX_BUY_REFUND, {blindBoxBuyId: id}).subscribe(res => {
          if (res) {
            if (resolve) {
              resolve();
            }
            notification.success('退款成功');
          }
        });
      }
    });
};
