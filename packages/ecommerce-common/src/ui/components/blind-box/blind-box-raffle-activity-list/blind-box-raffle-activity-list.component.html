<vdr-page-block>
  <vdr-action-bar>
    <vdr-ab-left>
      <div class="flex items-center" [formGroup]="form">
        <label-value label="盲盒名称">
          <input type="text" formControlName="name" (keydown.enter)="query()" />
        </label-value>
        <label-value label="名称备注">
          <input type="text" formControlName="remarks" (keydown.enter)="query()" />
        </label-value>
        <div class="ml2 flex nowrap">
          <button class="btn btn-primary" (click)="query()">查询</button>
          <button class="btn" (click)="reset()">重置</button>
        </div>
      </div>
    </vdr-ab-left>
    <vdr-ab-right>
      <div class="flex items-center">
        <button *vdrIfPermissions="['CreateWishBoxActivity']" class="btn btn-primary" [routerLink]="['./create']">
          创建
        </button>
      </div>
    </vdr-ab-right>
  </vdr-action-bar>

  <vdr-data-table
    [items]="items$ | async"
    [currentPage]="currentPage$ | async"
    [itemsPerPage]="itemsPerPage$ | async"
    [totalItems]="totalItems$ | async"
    (pageChange)="setPageNumber($event)"
    (itemsPerPageChange)="setItemsPerPage($event)"
    w
  >
    <vdr-dt-column>盲盒名称</vdr-dt-column>
    <vdr-dt-column>名称备注</vdr-dt-column><vdr-dt-column>盲盒活动价格</vdr-dt-column>
    <vdr-dt-column>盲盒数量</vdr-dt-column>
    <vdr-dt-column>状态</vdr-dt-column>
    <vdr-dt-column>操作</vdr-dt-column>
    <ng-template let-item="item">
      <td class="left">{{ item.name }}</td>
      <td class="left">{{ item.remarks }}</td>
      <td class="left">{{ getPrice(item) }}</td>
      <td class="left">{{ item.wishBoxActivityOpenStrategies?.length }}</td>
      <td class="left">{{ activityStatus[item.status] }}</td>
      <td class="left">
        <button
          *vdrIfPermissions="['CreateWishBoxActivity']"
          class="btn btn-link"
          [routerLink]="['./' + item.id]"
          [queryParams]="{copy: true}"
        >
          复制
        </button>
        <button *vdrIfPermissions="['UpdateWishBoxActivity']" class="btn btn-link" [routerLink]="['./' + item.id]">
          编辑
        </button>
        <!-- <promotion-dialog-trigger [locals]="{id: item.id, type: 'fullDiscountActivity'}">
          <button class="btn btn-link">推广</button>
        </promotion-dialog-trigger> -->
        <ng-container *ngIf="item.status !== 'closed'">
          <button *vdrIfPermissions="['DeleteWishBoxActivity']" class="btn btn-link error" (click)="deleteBox(item.id)">
            删除
          </button>
        </ng-container>
      </td>
    </ng-template>
  </vdr-data-table>
</vdr-page-block>
