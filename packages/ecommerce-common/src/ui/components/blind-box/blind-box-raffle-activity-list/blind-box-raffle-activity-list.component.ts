/* eslint-disable @typescript-eslint/no-explicit-any */
import {Component, OnInit} from '@angular/core';
import {Router, ActivatedRoute} from '@angular/router';
import {FormBuilder} from '@angular/forms';
import {DataService, ModalService, NotificationService} from '@vendure/admin-ui/core';
import {CustomBaseListComponent} from '../../base/custom-base-list.component';
import {WISH_BOX_ACTIVITY_LIST, UPSERT_WISH_BLIND_BOX_ACTIVITY} from '../graphql';
import {SortOrder} from '../../../generated-admin-types';

@Component({
  selector: 'app-blind-box-raffle-activity-list',
  templateUrl: './blind-box-raffle-activity-list.component.html',
  styleUrls: ['./blind-box-raffle-activity-list.component.scss'],
})
export class BlindBoxRaffleActivityListComponent extends CustomBaseListComponent<any, any> implements OnInit {
  activityStatus = {
    draft: '草稿',
    opened: '正常',
    closed: '已删除',
  };

  constructor(
    router: Router,
    route: ActivatedRoute,
    private dataService: DataService,
    private fb: FormBuilder,
    private modal: ModalService,
    private notification: NotificationService,
  ) {
    super(router, route);
    super.setQueryFn(
      () => this.dataService.query(WISH_BOX_ACTIVITY_LIST),
      res => res.wishBoxActivities,
      (skip, take) => {
        const {name, remarks} = this.form.getRawValue();
        return {
          options: {
            skip,
            take,
            filter: {
              name: name ? {contains: name} : undefined,
              remarks: remarks ? {contains: remarks} : undefined,
            },
            sort: {
              updatedAt: SortOrder.Desc,
            },
          },
        };
      },
    );

    this.form = this.fb.group({
      name: undefined,
      remarks: undefined,
    });
  }

  ngOnInit() {
    super.ngOnInit();
  }

  reset() {
    this.form.reset();
    this.query();
  }

  getPrice(item: any) {
    const sorted = item?.wishBoxActivityOpenStrategies
      ?.toSorted((a: any, b: any) => a.price - b.price)
      ?.map((m: any) => m.price);
    const start = sorted?.[0] ?? 0;
    const end = sorted?.[sorted?.length - 1];
    return start === end
      ? `${parseFloat((start / 100).toFixed(2))}元`
      : `${parseFloat((start / 100).toFixed(2))}~${parseFloat((end / 100).toFixed(2))}元`;
  }

  deleteBox(id: string) {
    this.modal
      .dialog({
        title: '提示',
        body: '确认删除该盲盒活动？',
        buttons: [
          {
            type: 'danger',
            returnValue: true,
            label: '删除',
          },
          {
            type: 'secondary',
            returnValue: false,
            label: '取消',
          },
        ],
      })
      .subscribe(rv => {
        if (!rv) {
          return;
        }
        const input = {
          id,
          status: 'closed',
        };
        this.dataService.mutate(UPSERT_WISH_BLIND_BOX_ACTIVITY, {input}).subscribe(res => {
          if (res) {
            this.query();
            this.notification.success('删除成功');
          }
        });
      });
  }
}
