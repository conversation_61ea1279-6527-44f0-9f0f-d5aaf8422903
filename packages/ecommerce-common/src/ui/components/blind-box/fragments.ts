import gql from 'graphql-tag';

export const BLIND_BOX_FRAGMENT = gql`
  fragment blindBox on BlindBox {
    id
    wishItemId
    price
    name
    remarks
    description
    img
    blindBoxItems {
      id
      targetId
      type
      blindBoxId
      baseProbability
      productVariant {
        id
        name
        price
        stockOnHand
        stockAllocated
        enabled
        product {
          id
          name
          enabled
        }
        options {
          id
          name
        }
      }
      isWishItem
      openBoxImageUrl
    }
  }
`;

// export const BlindBoxActivityBoxLink = gql`
//   fragment blindBoxActivityBoxLink on BlindBoxActivityBoxLink {
//     id
//     createdAt
//     updatedAt
//     blindBoxActivity {
//       id
//     }
//     blindBoxActivityId
//     blindBox {
//       id
//     }
//     blindBoxId
//     maxAssistLimit
//     productProbabilities {
//       blindBoxItemId
//     }
//   }
// `;

export const BLIND_BOX_ACTIVITY_CONFIG = gql`
  fragment blindBoxActivityLimitConfig on BlindBoxActivityLimitConfig {
    id
    createdAt
    updatedAt
    maxBoxPurchaseLimit
    maxAssistLimit
    maxRefundLimit
    assistValidityPeriod
    purchaseLimitPeriod
    purchaseLimit
    assistLimitPeriod
    assistLimit
    refundLimitPeriod
    refundLimit
  }
`;

export const BLIND_BOX_ASSIST_GIFT_FRAGMENT = gql`
  fragment assistGift on AssistGift {
    id
    createdAt
    updatedAt
    giftName
    giftImage
    type
    coupon {
      id
      name
    }
    targetId
  }
`;

export const BLIND_BOX_ACTIVITY_FRAGMENT = gql`
  fragment blindBoxActivity on BlindBoxActivity {
    id
    createdAt
    updatedAt
    name
    remarks
    description
    startAt
    endAt
    enabled
    statue
    isFreeShipping
    freeShippingThreshold
    price
    baseBlindBoxProbability
    baseBlindBox {
      ...blindBox
    }
    baseBlindBoxId
    blindBoxActivityBoxLinks {
      id
      blindBoxId
      blindBox {
        ...blindBox
      }
      maxAssistLimit
      assistLimits
      productProbabilities {
        targetProbability
        blindBoxItemId
      }
    }
    assistLimit
    assistPagePoster {
      pictures {
        imgHeight
        imgWidth
        imgUrl
        videoUrl
        alt
        jump {
          startAxisX
          startAxisY
          endPointAxisX
          endPointAxisY
          jumpType
          jumpValue
          jumpName
        }
      }
    }
    assistPosterType
    openBoxMode
    stock
    sold
  }
  ${BLIND_BOX_FRAGMENT}
`;

export const BLIND_BOX_BUY_FRAGMENT = gql`
  fragment BlindBoxBuyFragment on BlindBoxBuy {
    id
    createdAt
    updatedAt
    code
    status
    pickupAt
    paymentAt
    paymentMetadata
    blindBoxActivity {
      id
      name
    }
    wishBlindBox {
      id
      name
      price
      img
    }
    wishBlindBoxItem {
      id
      productVariant {
        id
        name
        featuredAsset {
          id
          preview
        }
        price
        product {
          id
          name
          featuredAsset {
            id
            preview
          }
        }
      }
    }
    customer {
      id
      lastName
      phoneNumber
    }
    distributor {
      id
      name
    }
    blindBoxOpenRecords {
      id
      openedAt
      isWishedItem
      blindBoxItem {
        id
        productVariant {
          id
          name
          price
          featuredAsset {
            id
            preview
          }
          product {
            id
            name
            featuredAsset {
              id
              preview
            }
          }
        }
      }
    }
    pickupOpenRecord {
      id
      blindBoxItem {
        id
        productVariant {
          id
          name
          product {
            id
            name
          }
        }
      }
    }
    blindBoxRefundRecord {
      id
      refundAt
    }
    assistLimit
    assistCount
    assistExpireAt
  }
`;
export const WISH_BOX_ACTIVITY_BUY_FRAGMENT = gql`
  fragment WishBoxActivityBuy on WishBoxActivityBuy {
    id
    createdAt
    updatedAt
    activityId
    activity {
      id
      name
    }
    customerId
    activityOpenStrategyId
    activityOpenStrategy {
      id
      count
      price
    }
    customer {
      id
      lastName
      phoneNumber
    }
    wishBoxActivityRecord {
      id
      boxType
      isEmpty
      code
      startAt
      expireAt
      status
      order {
        id
        distributorOrder {
          id
          distributor {
            id
            name
          }
        }
      }
      wishBoxActivityRecordItems {
        id
        createdAt
        updatedAt
        type
        productVariant {
          id
          name
          featuredAsset {
            id
            preview
          }
          price
          product {
            id
            name
            featuredAsset {
              id
              preview
            }
          }
        }
      }
      orderId
      refundReason
    }
    price
    code
    paymentMetadata
    paymentMethod
    paymentAt
    status
    payStatus
  }
`;

export const WISH_BOX_ACTIVITY_FRAGMENT = gql`
  fragment WishBoxActivity on WishBoxActivity {
    id
    createdAt
    updatedAt
    name
    remarks
    startAt
    endAt
    period
    periodUnit
    periodPerLimit
    freeBoxOpen
    freeBoxPeriod
    freeBoxPeriodUnit
    freeBoxPeriodPerLimit
    freeBoxPerWinLimit
    freeBoxPrizeValidityDay
    freeBoxPrizeValidityMode
    freeBoxPrizeValidityStartAt
    freeBoxPrizeValidityExpireAt
    rules
    status
    wishBoxActivityOpenStrategies {
      id
      count
      price
      wishProbability
      baseProbability
      status
      sort
    }
    wishBoxActivityPrizes {
      id
      targetId
      probability
      boxType
      status
      productVariant {
        id
        name
        price
        stockOnHand
        stockAllocated
        enabled
        productId
        product {
          id
          name
          enabled
        }
        options {
          id
          name
        }
      }
    }
    titleImage
    shareCover
  }
`;
