/* eslint-disable @typescript-eslint/no-explicit-any */
import {Injectable} from '@angular/core';
import {Router} from '@angular/router';
import {BaseEntityResolver, DataService} from '@vendure/admin-ui/core';
import {WISH_BLIND_BOX_BUY_DETAIL} from '../graphql';
// import {ORDER_DETAIL} from '../graphql/graphql';

@Injectable({
  providedIn: 'root',
})
export class BlindBoxRaffleOpenRecordResolver extends BaseEntityResolver<any> {
  constructor(dataService: DataService, router: Router) {
    super(
      router,
      {
        id: '',
      },
      id =>
        dataService
          .query<any>(WISH_BLIND_BOX_BUY_DETAIL, {
            wishBoxActivityBuyId: id,
          })
          .refetchOnChannelChange()
          .mapStream(item => {
            console.log(item);
            const res = item.wishBoxActivityBuy;
            return res;
          }),
    );
  }
}
