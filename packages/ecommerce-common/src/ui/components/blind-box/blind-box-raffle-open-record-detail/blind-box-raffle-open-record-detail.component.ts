/* eslint-disable @typescript-eslint/no-explicit-any */
import {Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef} from '@angular/core';
import {FormGroup} from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {
  BaseDetailComponent,
  DataService,
  LanguageCode,
  ModalService,
  NotificationService,
  ServerConfigService,
} from '@vendure/admin-ui/core';

// import dayjs from 'dayjs';
import {cloneDeep} from 'lodash';
import {interval, map, Observable, takeWhile} from 'rxjs';
import {BlindBoxBuy, wishboxStatusObject, getCurrentAssistProgress} from '../const';
import {REFUND_WISH_BOX_ACTIVITY_BUY} from '../graphql';
import {CustomInputsDialogComponent} from '../../dialog/custom-inputs-dialog/custom-inputs-dialog.component';

@Component({
  selector: 'app-blind-box-raffle-open-record-detail',
  templateUrl: './blind-box-raffle-open-record-detail.component.html',
  styleUrls: ['./blind-box-raffle-open-record-detail.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BlindBoxRaffleOpenRecordDetailComponent extends BaseDetailComponent<BlindBoxBuy> implements OnInit {
  orderInfo: any;
  detailForm: FormGroup;
  deadline: Date;
  restTimeStamp = 1;
  wishItemInfo: any;
  openRecords: any;

  countDown$: Observable<{returnValue: string; counting: boolean}>;

  blindboxStatus = wishboxStatusObject;

  get refundable() {
    return this.orderInfo?.payStatus === 'paid';
  }

  getCurrentAssistProgress = getCurrentAssistProgress;

  constructor(
    route: ActivatedRoute,
    router: Router,
    protected dataService: DataService,
    protected serverConfig: ServerConfigService,
    private modal: ModalService,
    private notification: NotificationService,
    private ref: ChangeDetectorRef,
  ) {
    super(route, router, serverConfig, dataService);

    this.detailForm = new FormGroup({});
  }

  ngOnInit() {
    this.init();
  }

  protected setFormValues(entity: any, languageCode: LanguageCode): void {
    // throw new Error('Method not implemented.');
    console.log(entity);
    if (!entity) {
      return;
    }
    this.orderInfo = cloneDeep(entity);

    this.wishItemInfo = this.orderInfo.wishBlindBoxItem?.productVariant;

    this.openRecords = this.orderInfo.wishBoxActivityRecord;

    if (this.orderInfo.assistExpireAt) {
      this.deadline = new Date(this.orderInfo.assistExpireAt);
    }
    this.countDown$ = interval(1000).pipe(
      map(() => {
        const now = new Date();
        const timeLeft = Math.max(this.deadline.getTime() - now.getTime(), 0);
        this.restTimeStamp = timeLeft;
        console.log(timeLeft);
        const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeLeft / (1000 * 60 * 60)) % 24);
        const minutes = Math.floor((timeLeft / (1000 * 60)) % 60);
        const seconds = Math.floor((timeLeft / 1000) % 60);

        return {returnValue: `${days}天${hours}小时${minutes}分钟${seconds}秒`, counting: timeLeft >= 0};
      }),
      takeWhile(time => time.counting), // 停止计时器
    );

    this.ref.detectChanges();
  }

  handleRefund() {
    this.modal
      .fromComponent(CustomInputsDialogComponent, {
        locals: {
          inputs: [
            {
              label: '拒绝原因',
              key: 'field',
              inputType: 'string',
              value: undefined,
            },
          ],
          title: '主动退款',
        },
      })
      .subscribe(rv => {
        if (rv?.length) {
          const refundReason = rv[0].value;
          this.dataService
            .mutate(REFUND_WISH_BOX_ACTIVITY_BUY, {wishBoxActivityBuyId: this.id, refundReason})
            .subscribe(res => {
              if (res) {
                this.notification.success('退款成功');
              }
            });
        }
      });
  }
}
