import gql from 'graphql-tag';
import {
  BLIND_BOX_FRAGMENT,
  BLIND_BOX_ACTIVITY_FRAGMENT,
  BLIND_BOX_ASSIST_GIFT_FRAGMENT,
  BLIND_BOX_BUY_FRAGMENT,
  WISH_BOX_ACTIVITY_FRAGMENT,
  WISH_BOX_ACTIVITY_BUY_FRAGMENT,
} from './fragments';

export const BLIND_BOX_CONFIG = gql`
  query blindBoxActivityLimitConfig {
    blindBoxActivityLimitConfig {
      id
      createdAt
      updatedAt
      maxBoxPurchaseLimit
      maxAssistLimit
      maxRefundLimit
      assistValidityPeriod
      purchaseLimitPeriod
      purchaseLimit
      assistLimitPeriod
      assistLimit
      refundLimitPeriod
      refundLimit
    }
  }
`;

export const BLIND_BOX_ASSIST_GIFT_CONFIG = gql`
  query assistGiftConfig($assistGiftConfigId: ID) {
    assistGiftConfig(assistGiftConfigId: $assistGiftConfigId) {
      id
      createdAt
      updatedAt
      isGiftForConvertedUser
      isGiftForUnconvertedUser
      convertedGift {
        ...assistGift
      }
      convertedGiftId
      nonConvertedGiftLevels {
        convertedGiftId
        level
      }
    }
  }
  ${BLIND_BOX_ASSIST_GIFT_FRAGMENT}
`;

export const BLIND_BOX_ASSIST_GIFTS = gql`
  query assistGifts($options: AssistGiftListOptions) {
    assistGifts(options: $options) {
      items {
        ...assistGift
      }
      totalItems
    }
  }
  ${BLIND_BOX_ASSIST_GIFT_FRAGMENT}
`;

export const UPSERT_BLIND_BOX_CONFIG = gql`
  mutation upsertBlindBoxActivityLimitConfig($input: BlindBoxActivityLimitConfigInput!) {
    upsertBlindBoxActivityLimitConfig(input: $input) {
      id
    }
  }
`;

export const UPSERT_BLIND_BOX_ASSIST_GIFT_CONFIG = gql`
  mutation upsertAssistGiftConfig($input: AssistGiftConfigInput!) {
    upsertAssistGiftConfig(input: $input) {
      id
    }
  }
`;

export const BLIND_BOX_LIST = gql`
  query blindBoxes($options: BlindBoxListOptions) {
    blindBoxes(options: $options) {
      items {
        ...blindBox
      }
      totalItems
    }
  }
  ${BLIND_BOX_FRAGMENT}
`;

export const BLIND_BOX_DETAIL = gql`
  query blindBox($blindBoxId: ID!) {
    blindBox(blindBoxId: $blindBoxId) {
      ...blindBox
    }
  }
  ${BLIND_BOX_FRAGMENT}
`;

export const UPSERT_BLIND_BOX = gql`
  mutation upsertBlindBox($input: BlindBoxInput!) {
    upsertBlindBox(input: $input) {
      id
    }
  }
`;

export const DELETE_BLIND_BOX = gql`
  mutation deleteBlindBox($blindBoxId: ID!) {
    deleteBlindBox(blindBoxId: $blindBoxId) {
      result
      message
    }
  }
`;

export const BLIND_BOX_ACTIVITY_LIST = gql`
  query blindBoxActivities($options: BlindBoxActivityListOptions) {
    blindBoxActivities(options: $options) {
      items {
        ...blindBoxActivity
      }
      totalItems
    }
  }
  ${BLIND_BOX_ACTIVITY_FRAGMENT}
`;

export const BLIND_BOX_ACTIVITY_DETAIL = gql`
  query blindBoxActivity($blindBoxActivityId: ID!) {
    blindBoxActivity(blindBoxActivityId: $blindBoxActivityId) {
      ...blindBoxActivity
    }
  }
  ${BLIND_BOX_ACTIVITY_FRAGMENT}
`;

export const UPSERT_BLIND_BOX_ACTIVITY = gql`
  mutation upsertBlindBoxActivity($input: BlindBoxActivityInput!) {
    upsertBlindBoxActivity(input: $input) {
      id
    }
  }
`;

export const FAILURE_BLIND_BOX_ACTIVITY = gql`
  mutation failureBlindBoxActivity($blindBoxActivityId: ID!) {
    failureBlindBoxActivity(blindBoxActivityId: $blindBoxActivityId) {
      id
    }
  }
`;

export const DELETE_BLIND_BOX_ACTIVITY = gql`
  mutation deleteBlindBoxActivity($blindBoxActivityId: ID!) {
    deleteBlindBoxActivity(blindBoxActivityId: $blindBoxActivityId) {
      result
      message
    }
  }
`;

export const BLIND_BOX_BUY_LIST = gql`
  query getBlindBoxBuys(
    $options: BlindBoxBuyListOptions
    $blindBoxName: String
    $distributorName: String
    $customerName: String
    $customerPhone: String
  ) {
    getBlindBoxBuys(
      options: $options
      blindBoxName: $blindBoxName
      distributorName: $distributorName
      customerName: $customerName
      customerPhone: $customerPhone
    ) {
      items {
        id
        createdAt
        updatedAt
        code
        status
        paymentAt
        blindBoxActivity {
          id
          name
          price
        }
        wishBlindBox {
          id
          name
          price
          img
        }
        customer {
          id
          lastName
          phoneNumber
        }
        distributor {
          id
          name
        }
        blindBoxOpenRecords {
          id
          openedAt
          isWishedItem
          blindBoxItem {
            id
            productVariant {
              id
              name
            }
          }
        }
        pickupOpenRecord {
          id
          blindBoxItem {
            id
            productVariant {
              id
              name
              price
              featuredAsset {
                id
                preview
              }
              options {
                id
                name
              }
              product {
                id
                name
                featuredAsset {
                  id
                  preview
                }
              }
            }
          }
        }
        assistLimit
        assistCount
      }
      totalItems
    }
  }
`;

export const BLIND_BOX_BUY_DETAIL = gql`
  query getBlindBoxBuy($blindBoxBuyId: ID!) {
    getBlindBoxBuy(blindBoxBuyId: $blindBoxBuyId) {
      ...BlindBoxBuyFragment
    }
  }
  ${BLIND_BOX_BUY_FRAGMENT}
`;

export const BLIND_BOX_BUY_REFUND = gql`
  mutation refundBlindBox($blindBoxBuyId: ID!) {
    refundBlindBox(blindBoxBuyId: $blindBoxBuyId) {
      ...BlindBoxBuyFragment
    }
  }
  ${BLIND_BOX_BUY_FRAGMENT}
`;

export const BLIND_BOX_STATISTICS = gql`
  query totalBlindBoxStatistics($startTime: DateTime, $endTime: DateTime) {
    totalBlindBoxStatistics(startTime: $startTime, endTime: $endTime) {
      assistCustomerCount
      assistCount
      assistCouponCustomerCount
      couponPayStatistics
      couponPayAmount
      payConversionRate
      assistNewCustomerCount
      assistNewCustomerCouponCount
      assistNewCustomerPayCount
      assistNewCustomerPayConversionRate
      assistNotPayCustomerFirstPayCount
      assistPayCustomerFirstPayCount
      blindBoxActivityVisit
      blindBoxActivityPayCustomerCount
      blindBoxPayConversionRate
      blindBoxActivityStatistics {
        blindBoxId
        blindBox {
          id
          name
          wishBlindBoxItem {
            id
            productVariant {
              id
              name
            }
          }
        }
        purchaseCustomerCount
        openWishBlindBoxCustomerCount
        openNotWishBlindBoxCustomerCount
        shareAssistCustomerCount
        assistOpenBlindBoxCustomerCount
      }
      couponStatistics {
        couponId
        couponName
        couponCustomerCount
        couponPayCustomerCount
        payConversionRate
      }
    }
  }
`;

export const BLIND_BOX_EXPORT_TASK_CREATE = gql`
  mutation blindBoxExportTaskCreate($exportType: ExportType, $blindBoxOrderQueryParam: BlindBoxOrderQueryParamInput!) {
    blindBoxExportTaskCreate(exportType: $exportType, blindBoxOrderQueryParam: $blindBoxOrderQueryParam) {
      id
      createdAt
      updatedAt
      completedAt
      status
      fileUrl
      errorMessage
      exportType
      progress
      queryParams
      administrator {
        id
        lastName
      }
    }
  }
`;

export const BLIND_BOX_STATISTICS_EXPORT_TASK_CREATE = gql`
  mutation blindBoxStatisticsExportTaskCreate(
    $exportType: ExportType
    $blindBoxStatisticsQueryParam: BlindBoxStatisticsQueryParamInput!
  ) {
    blindBoxStatisticsExportTaskCreate(
      exportType: $exportType
      blindBoxStatisticsQueryParam: $blindBoxStatisticsQueryParam
    ) {
      id
      createdAt
      updatedAt
      completedAt
      status
      fileUrl
      errorMessage
      exportType
      progress
      queryParams
      administrator {
        id
        lastName
      }
    }
  }
`;

export const WISH_BOX_ACTIVITY_LIST = gql`
  query wishBoxActivities($options: WishBoxActivityListOptions) {
    wishBoxActivities(options: $options) {
      items {
        id
        name
        remarks
        status
        wishBoxActivityOpenStrategies {
          id
          price
        }
      }
      totalItems
    }
  }
`;

export const WISH_BOX_ACTIVITY = gql`
  query wishBoxActivity($wishBoxActivityId: ID!, $options: WishBoxActivityListOptions) {
    wishBoxActivity(wishBoxActivityId: $wishBoxActivityId, options: $options) {
      ...WishBoxActivity
    }
  }
  ${WISH_BOX_ACTIVITY_FRAGMENT}
`;

export const UPSERT_WISH_BLIND_BOX_ACTIVITY = gql`
  mutation upsertWishBoxActivity($input: WishBoxActivityInput!) {
    upsertWishBoxActivity(input: $input) {
      id
    }
  }
`;

export const WISH_BLIND_BOX_BUY_LIST = gql`
  query wishBoxActivityBuys($recordCode: String, $options: WishBoxActivityBuyListOptions) {
    wishBoxActivityBuys(recordCode: $recordCode, options: $options) {
      items {
        ...WishBoxActivityBuy
      }
      totalItems
    }
  }
  ${WISH_BOX_ACTIVITY_BUY_FRAGMENT}
`;

export const WISH_BLIND_BOX_BUY_DETAIL = gql`
  query wishBoxActivityBuy($wishBoxActivityBuyId: ID!, $options: WishBoxActivityBuyListOptions) {
    wishBoxActivityBuy(wishBoxActivityBuyId: $wishBoxActivityBuyId, options: $options) {
      ...WishBoxActivityBuy
    }
  }
  ${WISH_BOX_ACTIVITY_BUY_FRAGMENT}
`;

export const REFUND_WISH_BOX_ACTIVITY_BUY = gql`
  mutation refundWishBoxActivityBuy($wishBoxActivityBuyId: ID!, $refundReason: String) {
    refundWishBoxActivityBuy(wishBoxActivityBuyId: $wishBoxActivityBuyId, refundReason: $refundReason) {
      ...WishBoxActivityBuy
    }
  }
  ${WISH_BOX_ACTIVITY_BUY_FRAGMENT}
`;
