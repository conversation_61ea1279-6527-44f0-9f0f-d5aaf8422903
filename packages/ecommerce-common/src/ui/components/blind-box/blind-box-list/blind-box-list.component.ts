/* eslint-disable @typescript-eslint/no-explicit-any */
import {Component, OnInit} from '@angular/core';
import {Router, ActivatedRoute} from '@angular/router';
import {FormBuilder} from '@angular/forms';
import {DataService, ModalService, NotificationService} from '@vendure/admin-ui/core';
import {CustomBaseListComponent} from '../../base/custom-base-list.component';
import {BLIND_BOX_LIST, DELETE_BLIND_BOX} from '../graphql';
import {SortOrder} from '../../../generated-admin-types';

@Component({
  selector: 'app-blind-box-list',
  templateUrl: './blind-box-list.component.html',
  styleUrls: ['./blind-box-list.component.scss'],
})
export class BlindBoxListComponent extends CustomBaseListComponent<any, any> implements OnInit {
  constructor(
    router: Router,
    route: ActivatedRoute,
    private dataService: DataService,
    private fb: FormBuilder,
    private modal: ModalService,
    private notification: NotificationService,
  ) {
    super(router, route);
    super.setQueryFn(
      () => this.dataService.query(BLIND_BOX_LIST),
      res => res.blindBoxes,
      (skip, take) => {
        const {name, remarks} = this.form.getRawValue();
        return {
          options: {
            skip,
            take,
            filter: {
              name: name ? {contains: name} : undefined,
              remarks: remarks ? {contains: remarks} : undefined,
            },
            sort: {
              updatedAt: SortOrder.Desc,
            },
          },
        };
      },
    );

    this.form = this.fb.group({
      name: undefined,
      remarks: undefined,
    });
  }

  ngOnInit() {
    super.ngOnInit();
  }

  reset() {
    this.form.reset();
    this.query();
  }

  deleteBox(id: string) {
    this.modal
      .dialog({
        title: '提示',
        body: '确认删除该商品盲盒？',
        buttons: [
          {
            type: 'danger',
            returnValue: true,
            label: '删除',
          },
          {
            type: 'secondary',
            returnValue: false,
            label: '取消',
          },
        ],
      })
      .subscribe(rv => {
        if (!rv) {
          return;
        }
        this.dataService.mutate(DELETE_BLIND_BOX, {blindBoxId: id}).subscribe(res => {
          if (res) {
            this.query();
            this.notification.success('删除成功');
          }
        });
      });
  }
}
