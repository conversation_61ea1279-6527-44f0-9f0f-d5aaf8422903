<vdr-page-block>
  <vdr-action-bar>
    <vdr-ab-left>
      <div class="flex clr-flex-row">
        <vdr-entity-info [entity]="entity$ | async"></vdr-entity-info>
        <!-- <clr-toggle-wrapper *vdrIfPermissions="['UpdateCatalog', 'UpdateProduct']">
          <input type="checkbox" clrToggle name="enabled" [formControl]="detailForm.get(['product', 'enabled'])" />
          <label>{{ 'common.enabled' | translate }}</label>
        </clr-toggle-wrapper> -->
      </div>
      <!-- <vdr-language-selector
        [disabled]="isNew$ | async"
        [availableLanguageCodes]="availableLanguages$ | async"
        [currentLanguageCode]="languageCode$ | async"
        (languageCodeChange)="setLanguage($event)"
      ></vdr-language-selector> -->
    </vdr-ab-left>

    <vdr-ab-right>
      <vdr-action-bar-items locationId="product-detail"></vdr-action-bar-items>

      <button
        *ngIf="isNew$ | async; else updateButton"
        #addBtn
        [disabled]="disabledUpdate"
        [clrLoading]="loading$ | async"
        class="btn btn-primary"
        (click)="create()"
      >
        {{ createGift ? '创建赠品' : '创建' }}
        <!-- {{ 'common.create' | translate }} -->
      </button>

      <ng-template #updateButton>
        <button
          *vdrIfPermissions="['UpdateCatalog', 'UpdateProduct']"
          [disabled]="disabledUpdate"
          [clrLoading]="loading$ | async"
          class="btn btn-primary"
          (click)="save()"
        >
          {{ '保存' }}
        </button>
      </ng-template>
    </vdr-ab-right>
  </vdr-action-bar>

  <form class="form" [formGroup]="detailForm" *ngIf="product$ | async as product">
    <button type="submit" hidden x-data="prevents enter key from triggering other buttons"></button>
    <clr-tabs>
      <clr-tab>
        <button clrTabLink (click)="navigateToTab('details')">
          {{ '商品详情' }}
        </button>
        <clr-tab-content *clrIfActive="(activeTab$ | async) === 'details'">
          <div class="clr-row">
            <div class="clr-col">
              <section class="form-block" formGroupName="product">
                <ng-container *ngIf="!(isNew$ | async)">
                  <ng-container *vdrIfMultichannel>
                    <vdr-form-item [label]="'common.channels' | translate" *vdrIfDefaultChannelActive>
                      <div class="flex channel-assignment items-center">
                        <ng-container *ngFor="let channel of productChannels$ | async">
                          <vdr-chip
                            *ngIf="!isDefaultChannel(channel.code)"
                            icon="times-circle"
                            (iconClick)="removeFromChannel(channel.id)"
                          >
                            <vdr-channel-badge [channelCode]="channel.code"></vdr-channel-badge>
                            {{ channel.code | channelCodeToLabel }}
                          </vdr-chip>
                        </ng-container>
                        <button class="btn btn-sm" (click)="assignToChannel()">
                          <clr-icon shape="layers"></clr-icon>
                          {{ 'catalog.assign-to-channel' | translate }}
                        </button>
                      </div>
                    </vdr-form-item>
                  </ng-container>
                </ng-container>
                <vdr-form-field [label]="'catalog.product-name' | translate" for="name">
                  <input
                    id="name"
                    type="text"
                    formControlName="name"
                    [maxlength]="80"
                    [readonly]="!(['UpdateCatalog', 'UpdateProduct'] | hasPermission)"
                    (input)="updateSlug($event.target.value)"
                  />
                </vdr-form-field>
                <div
                  class="auto-rename-wrapper"
                  [class.visible]="(isNew$ | async) === false && detailForm.get(['product', 'name'])?.dirty"
                >
                  <clr-checkbox-wrapper>
                    <input clrCheckbox type="checkbox" id="auto-update" formControlName="autoUpdateVariantNames" />
                    <label>{{ 'catalog.auto-update-product-variant-name' | translate }}</label>
                  </clr-checkbox-wrapper>
                </div>
                <vdr-form-field
                  [label]="'catalog.slug' | translate"
                  for="slug"
                  [errors]="{pattern: 'catalog.slug-pattern-error' | translate}"
                >
                  <input
                    id="slug"
                    type="text"
                    formControlName="slug"
                    [readonly]="!(['UpdateCatalog', 'UpdateProduct'] | hasPermission)"
                  />
                </vdr-form-field>
                <vdr-rich-text-editor
                  formControlName="description"
                  [readonly]="!(['UpdateCatalog', 'UpdateProduct'] | hasPermission)"
                  [label]="'common.description' | translate"
                ></vdr-rich-text-editor>

                <vdr-form-field label="所属商品分组" *ngIf="!isGift">
                  <app-collection-selector
                    formControlName="collectionIds"
                    (getCollectionsOrigin)="handleCollectionIdsChange($event)"
                  ></app-collection-selector>
                </vdr-form-field>

                <section formGroupName="customFields" *ngIf="customFields.length">
                  <!-- <label>{{ 'common.custom-fields' | translate }}</label> -->
                  <!-- <vdr-tabbed-custom-fields
                    entityName="Product"
                    [customFields]="filterCustomFields"
                    [customFieldsFormGroup]="detailForm.get(['product', 'customFields'])"
                    [readonly]="!(['UpdateCatalog', 'UpdateProduct'] | hasPermission)"
                  ></vdr-tabbed-custom-fields> -->

                  <app-form-field label="详情图片（多张图片时，可以点击拖拽商品前后顺序）" for="particulars">
                    <!-- <div formGroupName="customFields"> -->
                    <image-picker [multiple]="true" formControlName="particulars"></image-picker>
                    <!-- </div> -->
                  </app-form-field>

                  <!-- <app-form-field label="商品单位" for="unit">
                    <input formControlName="unit" />
                  </app-form-field>
                  <app-form-field label="商品价格" for="price">
                    <app-price-input formControlName="price"></app-price-input>
                  </app-form-field> -->
                  <app-form-field label="划线价" for="markingPrice">
                    <app-price-input formControlName="markingPrice"></app-price-input>
                  </app-form-field>

                  <app-form-field label="线下销量" [formGroup]="detailForm.get('product')">
                    <input type="number" formControlName="offlineSales" style="width: 163px" />
                  </app-form-field>

                  <app-form-field label="分享封面">
                    <image-picker formControlName="shareCover"></image-picker>
                  </app-form-field>

                  <ng-container *ngIf="!isGift">
                    <app-form-field label="开售类型" for="none">
                      <div class="p1">
                        <!-- <app-radio
                          label="立即开售"
                          value="immediate"
                          formControlName="putOnSaleType"
                          (change)="handlePutOnSaleType($event.target.value)"
                        >
                        </app-radio>
                        <app-radio
                          label="定时开售"
                          value="scheduled"
                          formControlName="putOnSaleType"
                          (change)="handlePutOnSaleType($event.target.value)"
                        >
                          <vdr-datetime-picker
                            *ngIf="putOnSaleTypeCtrl.value === 'scheduled'"
                            formControlName="putOnSaleTime"
                            style="width: 300px"
                          ></vdr-datetime-picker>
                        </app-radio>
                        <app-radio
                          label="放入仓库"
                          value="manual"
                          formControlName="putOnSaleType"
                          (change)="handlePutOnSaleType($event.target.value)"
                        >
                        </app-radio> -->
                        <div class="flex items-center flex-wrap mb2 mt2">
                          <clr-radio-wrapper class="flex items-center">
                            <input
                              type="radio"
                              clrRadio
                              value="immediate"
                              name="putOnSaleType"
                              formControlName="putOnSaleType"
                              (click)="handlePutOnSaleType($event, 'immediate')"
                            />
                            <label class="mr2">立即开售</label>
                          </clr-radio-wrapper>
                        </div>
                        <div class="flex items-center flex-wrap mb2 mt2">
                          <clr-radio-wrapper class="flex items-center">
                            <input
                              type="radio"
                              clrRadio
                              value="scheduled"
                              name="putOnSaleType"
                              formControlName="putOnSaleType"
                              (click)="handlePutOnSaleType($event, 'scheduled')"
                            />
                            <label class="mr2">定时开售</label>
                          </clr-radio-wrapper>
                          <app-date-picker
                            *ngIf="putOnSaleTypeCtrl.value === 'scheduled'"
                            formControlName="putOnSaleTime"
                            style="width: 300px"
                          ></app-date-picker>
                        </div>
                        <div class="flex items-center flex-wrap mb2 mt2">
                          <clr-radio-wrapper class="flex items-center">
                            <input
                              type="radio"
                              clrRadio
                              value="manual"
                              name="putOnSaleType"
                              formControlName="putOnSaleType"
                              (click)="handlePutOnSaleType($event, 'manual')"
                            />
                            <label class="mr2">放入仓库</label>
                          </clr-radio-wrapper>
                        </div>
                      </div>
                    </app-form-field>
                    <app-form-field label="定时下架" for="none">
                      <input
                        type="checkbox"
                        clrToggle
                        formControlName="timedTakedown"
                        (click)="handleTimedTakedown($event.target.checked)"
                      />
                      <app-date-picker
                        *ngIf="productCustomFields.value.timedTakedown"
                        formControlName="takedownTime"
                        style="width: 300px"
                      ></app-date-picker>
                    </app-form-field>
                    <app-form-field label="是否隐藏" for="hidden">
                      <input type="checkbox" clrToggle formControlName="hidden" />
                    </app-form-field>
                    <app-form-field label="详情不展示购物车" for="hidden">
                      <input
                        type="checkbox"
                        clrToggle
                        formControlName="isHiddenCart"
                        (click)="handleHideCart($event.target.checked)"
                      />
                    </app-form-field>
                    <app-form-field label="是否参与会员折扣" for="none">
                      <input type="checkbox" clrToggle formControlName="isVipProduct" />
                    </app-form-field>

                    <app-form-field label="限购" for="none">
                      <form>
                        <div ngModelGroup="limitModalGroup">
                          <clr-checkbox-wrapper>
                            <input
                              [(ngModel)]="limitModalGroup.enable"
                              [ngModelOptions]="{standalone: true}"
                              type="checkbox"
                              clrCheckbox
                              (change)="handleSwitchLimitType($event.target.checked)"
                            />
                            <label> 限制每人可购买数量 </label>
                          </clr-checkbox-wrapper>
                          <div *ngIf="limitModalGroup.enable">
                            <div class="flex items-center flex-wrap mb2">
                              <clr-radio-wrapper>
                                <input
                                  [(ngModel)]="limitModalGroup.radioVal"
                                  [ngModelOptions]="{standalone: true}"
                                  value="forever"
                                  type="radio"
                                  clrRadio
                                  (change)="radioLimitType($event.target.value)"
                                />
                                <label>终身限购</label>
                              </clr-radio-wrapper>
                              <div *ngIf="limitModalGroup.radioVal === 'forever'">
                                <span class="limit-count-input"
                                  ><input
                                    [(ngModel)]="limitModalGroup.foreverCount"
                                    [ngModelOptions]="{standalone: true}"
                                    type="number"
                                    min="1"
                                    (change)="handleLimitCount($event.target.value)"
                                  />件</span
                                >
                              </div>
                            </div>
                            <div class="flex items-center mb2 limit-type-period">
                              <clr-radio-wrapper>
                                <input
                                  [(ngModel)]="limitModalGroup.radioVal"
                                  [ngModelOptions]="{standalone: true}"
                                  value="period"
                                  type="radio"
                                  clrRadio
                                  (change)="radioLimitType($event.target.value)"
                                />
                                <label>按周期限购</label>
                              </clr-radio-wrapper>
                              <div *ngIf="limitModalGroup.radioVal === 'period'" class="limit-type-period">
                                <ng-select
                                  [(ngModel)]="limitModalGroup.limitType"
                                  [ngModelOptions]="{standalone: true}"
                                  [items]="limitTimeTypeOptions"
                                  [clearable]="false"
                                  [addTag]="false"
                                  [searchable]="false"
                                  bindValue="value"
                                  bindLabel="label"
                                  appendTo="ng-select"
                                  (ngModelChange)="selecLimitTimeType($event)"
                                ></ng-select
                                ><input
                                  [(ngModel)]="limitModalGroup.periodCount"
                                  [ngModelOptions]="{standalone: true}"
                                  type="number"
                                  min="1"
                                  (change)="handleLimitCount($event.target.value)"
                                />
                                件
                              </div>
                            </div>
                            <div class="flex items-center mb2">
                              <clr-radio-wrapper>
                                <input
                                  [(ngModel)]="limitModalGroup.radioVal"
                                  [ngModelOptions]="{standalone: true}"
                                  value="order"
                                  type="radio"
                                  clrRadio
                                  (change)="radioLimitType($event.target.value)"
                                />
                                <label>订单限购</label>
                              </clr-radio-wrapper>
                              <div *ngIf="limitModalGroup.radioVal === 'order'">
                                <span class="limit-count-input"
                                  ><input
                                    [(ngModel)]="limitModalGroup.orderCount"
                                    [ngModelOptions]="{standalone: true}"
                                    type="number"
                                    min="1"
                                    (change)="handleLimitCount($event.target.value)"
                                  />件</span
                                >
                              </div>
                            </div>
                          </div>
                        </div>
                      </form>
                    </app-form-field>
                  </ng-container>
                </section>

                <div *ngIf="virtualType !== 'memberCard'" [formGroup]="purchaseForm">
                  <app-form-field label="指定购买">
                    <input
                      formControlName="isMembershipPlanPurchase"
                      name="isMembershipPlanPurchase"
                      [value]="true"
                      type="checkbox"
                      clrCheckbox
                    />
                    <label> 指定会员用户购买 </label>
                  </app-form-field>

                  <app-form-field *ngIf="isLimitCustomer" label="请选择拥有哪些权益卡的用户可以购买该商品">
                    <app-select-async
                      [gql]="MEMBERSHIP_PLANS"
                      [resolveFn]="selectMembershipPlanResolveFn()"
                      [filter]="membershipPlanFilter"
                      [multiple]="true"
                      formControlName="membershipPlanIds"
                      class="member-card-select"
                      (selected)="handleSelectMembershipPlanIds($event)"
                    ></app-select-async>
                  </app-form-field>

                  <app-form-field *ngIf="isLimitCustomer" label="选择提示跳转会员卡页面">
                    <ng-select
                      [items]="guideMembershipPlansOptions"
                      bindValue="value"
                      bindLabel="label"
                      formControlName="guideMembershipPlanId"
                      class="member-card-select"
                      (change)="handleSelectGuideMembershipPlan($event)"
                    ></ng-select>
                  </app-form-field>
                </div>

                <vdr-custom-detail-component-host
                  locationId="product-detail"
                  [entity$]="entity$"
                  [detailForm]="detailForm"
                ></vdr-custom-detail-component-host>
              </section>
            </div>
            <div class="clr-col-md-auto">
              <vdr-assets
                [assets]="assetChanges.assets || product.assets"
                [featuredAsset]="product.featuredAsset"
                [updatePermissions]="updatePermissions"
                (change)="handleAssetChange($event)"
              ></vdr-assets>
              <div class="facets">
                <vdr-facet-value-chip
                  *ngFor="let facetValue of facetValues$ | async"
                  [facetValue]="facetValue"
                  [removable]="['UpdateCatalog', 'UpdateProduct'] | hasPermission"
                  (remove)="removeProductFacetValue(facetValue.id)"
                ></vdr-facet-value-chip>
                <button
                  class="btn btn-sm btn-secondary"
                  *vdrIfPermissions="['UpdateCatalog', 'UpdateProduct']"
                  (click)="selectProductFacetValue()"
                >
                  <clr-icon shape="plus"></clr-icon>
                  {{ 'catalog.add-facets' | translate }}
                </button>
              </div>
            </div>
          </div>

          <div *ngIf="isNew$ | async">
            <h4>{{ 'catalog.product-variants' | translate }}</h4>

            <vdr-generate-product-variants
              [virtualType]="virtualType"
              [sourceVariants]="variants$ | async"
              [sourceOptionGroups]="product.optionGroups"
              (variantsChange)="handleProductCreateVariantGenerate($event)"
            ></vdr-generate-product-variants>
          </div>
        </clr-tab-content>
      </clr-tab>
      <clr-tab *ngIf="!(isNew$ | async)">
        <button clrTabLink (click)="navigateToTab('variants')">
          {{ 'catalog.product-variants' | translate }}
        </button>
        <clr-tab-content *clrIfActive="(activeTab$ | async) === 'variants'">
          <section class="form-block">
            <div class="view-mode">
              <!-- <div class="btn-group">
                <button
                  class="btn btn-secondary-outline"
                  (click)="variantDisplayMode = 'card'"
                  [class.btn-primary]="variantDisplayMode === 'card'"
                >
                  <clr-icon shape="list"></clr-icon>
                  <span class="full-label">{{ '卡片展示规格' }}</span>
                </button>
                <button
                  class="btn"
                  (click)="variantDisplayMode = 'table'"
                  [class.btn-primary]="variantDisplayMode === 'table'"
                >
                  <clr-icon shape="table"></clr-icon>
                  <span class="full-label">{{ '表格展示规格' }}</span>
                </button>
              </div> -->
              <div class="variant-filter">
                <input [formControl]="filterInput" placeholder="搜索名称或商品库存编码" />
                <button class="icon-button" (click)="filterInput.setValue('')">
                  <clr-icon shape="times"></clr-icon>
                </button>
              </div>
              <div class="flex-spacer"></div>
              <div *ngIf="!virtualType">
                <a
                  *vdrIfPermissions="['UpdateCatalog', 'UpdateProduct']"
                  [routerLink]="['./', 'manage-variants']"
                  class="btn btn-secondary edit-variants-btn mb0 mr0"
                >
                  <clr-icon shape="add-text"></clr-icon>
                  {{ 'catalog.manage-variants' | translate }}
                </a>
              </div>
            </div>

            <div class="pagination-row mt4" *ngIf="10 < (paginationConfig$ | async)?.totalItems">
              <vdr-items-per-page-controls
                [itemsPerPage]="itemsPerPage$ | async"
                (itemsPerPageChange)="setItemsPerPage($event)"
              ></vdr-items-per-page-controls>

              <vdr-pagination-controls
                [id]="(paginationConfig$ | async)?.id"
                [currentPage]="currentPage$ | async"
                [itemsPerPage]="itemsPerPage$ | async"
                (pageChange)="setPage($event)"
              ></vdr-pagination-controls>
            </div>

            <vdr-product-variants-table
              *ngIf="variantDisplayMode === 'table'"
              [virtualType]="virtualType"
              [variants]="variants$ | async"
              [paginationConfig]="paginationConfig$ | async"
              [optionGroups]="product.optionGroups"
              [channelPriceIncludesTax]="channelPriceIncludesTax$ | async"
              [productVariantsFormArray]="detailForm.get('variants')"
              [pendingAssetChanges]="variantAssetChanges"
              [isPointsProduct]="isPointsProduct"
              (assetChange)="variantAssetChange($event)"
            ></vdr-product-variants-table>
            <!-- <vdr-product-variants-list
              *ngIf="variantDisplayMode === 'card'"
              [variants]="variants$ | async"
              [paginationConfig]="paginationConfig$ | async"
              [channelPriceIncludesTax]="channelPriceIncludesTax$ | async"
              [pendingFacetValueChanges]="variantFacetValueChanges"
              [optionGroups]="product.optionGroups"
              [productVariantsFormArray]="detailForm.get('variants')"
              [taxCategories]="taxCategories$ | async"
              [customFields]="customVariantFields"
              [customOptionFields]="customOptionFields"
              [activeLanguage]="languageCode$ | async"
              [pendingAssetChanges]="variantAssetChanges"
              (assignToChannel)="assignVariantToChannel($event)"
              (removeFromChannel)="removeVariantFromChannel($event)"
              (assetChange)="variantAssetChange($event)"
              (updateProductOption)="updateProductOption($event)"
              (selectionChange)="selectedVariantIds = $event"
            ></vdr-product-variants-list> -->
          </section>
          <!-- <div class="pagination-row mt4" *ngIf="10 < (paginationConfig$ | async)?.totalItems">
            <vdr-items-per-page-controls
              [itemsPerPage]="itemsPerPage$ | async"
              (itemsPerPageChange)="setItemsPerPage($event)"
            ></vdr-items-per-page-controls>

            <vdr-pagination-controls
              [id]="(paginationConfig$ | async)?.id"
              [currentPage]="currentPage$ | async"
              [itemsPerPage]="itemsPerPage$ | async"
              (pageChange)="setPage($event)"
            ></vdr-pagination-controls>
          </div> -->
        </clr-tab-content>
      </clr-tab>
    </clr-tabs>
  </form>
</vdr-page-block>
