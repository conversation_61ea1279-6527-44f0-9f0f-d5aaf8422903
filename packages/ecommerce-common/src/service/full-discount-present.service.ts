import {Injectable} from '@nestjs/common';
import {KvsService, MemoryStorageService, cacheableAccess, CacheKeyManagerService} from '@scmally/kvs';
import {
  ChannelService,
  EntityNotFoundError,
  ID,
  idsAreEqual,
  LanguageCode,
  ListQueryBuilder,
  ListQueryOptions,
  ProductService,
  ProductVariant,
  Promotion,
  PromotionService,
  RelationPaths,
  RequestContext,
  RequestContextService,
  TransactionalConnection,
} from '@vendure/core';
import {generatePublicId} from '@vendure/core/dist/common/generate-public-id';
import {In} from 'typeorm';
import {
  DiscountActivity,
  FullDiscountPresent,
  PackageDiscount,
  PaymentRewardActivity,
  SelectiveGiftActivity,
} from '../entities';
import {
  ActivityStatus,
  DeletionResult,
  FullDiscountPresentInput,
  ProgramLinkInput,
  RuleValue,
} from '../generated-admin-types';
import {
  ApplicableProduct,
  ApplicableType,
  DiscountType,
  FreeGiftValue,
  FullDiscountPresentType,
  PromotionType,
  RuleType,
} from '../generated-shop-types';
import {circulationDiscount} from '../promotion/action/circulation.discount.action';
import {ladderDiscount} from '../promotion/action/ladder.discount.action';
// import {CheckerIsRestricted} from '../promotion/conditions/checker-restricted';
import {DEFAULT_CACHE_TIMEOUT} from '../consts';
import {customerGroupList} from '../promotion/conditions/customer.group.list.conditions';
import {productQuantityContain} from '../promotion/conditions/product.quantity.conditions';
import {ActivityProduct, ActivityUtils} from '../utils';
import {CacheService} from './cache.service';
import {CommonService} from './common.service';
import {CustomerProductVariantService} from './custom-product-variant.service';
import {FreeGiftService} from './free-gift.service';
import {ProductCustomService} from './product-custom.service';
import {ProductPromotionActiveService} from './product-promotion-active.service';
import {PromotionResultDetailService} from './promotion-result-detail.service';
@Injectable()
export class FullDiscountPresentService {
  // protected checkerIsRestricted: CheckerIsRestricted;
  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private promotionService: PromotionService,
    private productPromotionActiveService: ProductPromotionActiveService,
    private freeGiftService: FreeGiftService,
    private commonService: CommonService,
    private productService: ProductService,
    private requestContextService: RequestContextService,
    private kvsService: KvsService,
    private promotionResultDetailService: PromotionResultDetailService,
    private cacheService: CacheService,
    private memoryStorageService: MemoryStorageService,
    private productCustomService: ProductCustomService,
    private customerProductVariantService: CustomerProductVariantService,
  ) {
    // this.checkerIsRestricted = new CheckerIsRestricted(
    //   this.connection,
    //   this.customerService,
    //   this.orderService,
    //   this.promotionActivityService,
    //   this.promotionService,
    // );
  }

  async getFullDiscountPresentLink(ctx: RequestContext, input: ProgramLinkInput) {
    const fullDiscountPresentId = input.id;
    const fullDiscountPresent = await this.findOne(ctx, fullDiscountPresentId);
    if (!fullDiscountPresent) {
      throw new EntityNotFoundError('FullDiscountPresent', fullDiscountPresentId);
    }
    const urlLink = await this.commonService.generateSmallProgramLink(
      ctx,
      {
        id: String(fullDiscountPresent.promotion.id),
        type: input.type,
      },
      input.path ?? '',
    );
    return urlLink;
  }
  async getFullDiscountPresentQRCode(ctx: RequestContext, input: ProgramLinkInput) {
    const fullDiscountPresentId = input.id;
    const fullDiscountPresent = await this.findOne(ctx, fullDiscountPresentId);
    if (!fullDiscountPresent) {
      throw new EntityNotFoundError('FullDiscountPresent', fullDiscountPresentId);
    }
    if (fullDiscountPresent.smallProgramQRCodeLink) {
      return fullDiscountPresent.smallProgramQRCodeLink;
    }
    const qrCodeLink = await this.commonService.generateSmallProgramQRCodeLink(
      ctx,
      {
        id: String(fullDiscountPresent.promotion.id),
        type: input.type,
      },
      input.path ?? '',
    );
    fullDiscountPresent.smallProgramQRCodeLink = qrCodeLink;
    await this.connection.getRepository(ctx, FullDiscountPresent).save(fullDiscountPresent);
    await this.cacheService.removeCache([
      CacheKeyManagerService.fullDiscountPresent(fullDiscountPresent.id, ctx.channelId),
      CacheKeyManagerService.promotion(fullDiscountPresent.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(fullDiscountPresent.promotion.id, ctx.channelId),
    ]);
    return qrCodeLink;
  }
  async upsertFullDiscountPresent(ctx: RequestContext, input: FullDiscountPresentInput) {
    await this.validate(ctx, input);
    // 修改前的可用商品
    let oldApplicableProduct: ApplicableProduct | undefined;
    if (input.id) {
      const fullDiscountPresent = await this.findOne(ctx, input.id);
      if (!fullDiscountPresent) {
        throw new EntityNotFoundError('FullDiscountPresent', input.id);
      }
      oldApplicableProduct = fullDiscountPresent.applicableProduct;
    }
    let status = ActivityStatus.Normal;
    if (new Date() < new Date(input.startTime)) {
      status = ActivityStatus.NotStarted;
    }
    if (new Date() > new Date(input.endTime)) {
      status = ActivityStatus.HaveEnded;
    }
    let fullDiscountPresent = new FullDiscountPresent({
      ...(input as unknown as FullDiscountPresent),
      status,
    });
    for (const ruleValue of fullDiscountPresent.ruleValues) {
      const freeGiftValues = ruleValue.freeGiftValues;
      if (!freeGiftValues) {
        continue;
      }
      for (const freeGiftValue of freeGiftValues) {
        if (!freeGiftValue) {
          continue;
        }
        const freeGiftProductId = freeGiftValue?.freeGiftProductId;
        if (freeGiftProductId) {
          const productVariant = await this.connection
            .getRepository(ctx, ProductVariant)
            .createQueryBuilder('productVariant')
            .leftJoinAndSelect('productVariant.productVariantPrices', 'productVariantPrices')
            .andWhere('productVariant.productId = :productId', {productId: freeGiftProductId})
            .orderBy('productVariantPrices.price', 'ASC')
            .take(1)
            .getOne();
          if (!productVariant) {
            return freeGiftValue;
          }
          freeGiftValue.skuId = String(productVariant.id);
        }
      }
    }
    fullDiscountPresent = await this.channelService.assignToCurrentChannel(fullDiscountPresent, ctx);
    fullDiscountPresent = await this.connection.getRepository(ctx, FullDiscountPresent).save(fullDiscountPresent);
    const promotion = await this.upsertPromotion(ctx, fullDiscountPresent);
    await this.productPromotionActiveService.createProductPromotionActive(
      ctx,
      promotion,
      input.applicableProduct,
      oldApplicableProduct,
    );
    fullDiscountPresent.promotion = promotion;
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, FullDiscountPresent).save(fullDiscountPresent);
    if (input.id) {
      await this.cacheService.removeCache([
        CacheKeyManagerService.fullDiscountPresent(input.id, ctx.channelId),
        CacheKeyManagerService.promotion(promotion.id, ctx.channelId),
        CacheKeyManagerService.promotionIncludeFailure(promotion.id, ctx.channelId),
      ]);
    }
    return this.findOne(ctx, fullDiscountPresent.id);
  }
  async upsertPromotion(ctx: RequestContext, fullDiscountPresent: FullDiscountPresent) {
    let type = PromotionType.FullDiscountPresent;
    if (fullDiscountPresent.type === FullDiscountPresentType.AmountFullPresent) {
      type = PromotionType.ActuallyPaid;
    }
    const promotionInput = {
      couponCode: fullDiscountPresent.promotion ? fullDiscountPresent.promotion.couponCode : generatePublicId(),
      name: fullDiscountPresent.displayName,
      startsAt: fullDiscountPresent.startTime,
      endsAt: fullDiscountPresent.endTime,
      enabled:
        fullDiscountPresent.status === ActivityStatus.Normal ||
        fullDiscountPresent.status === ActivityStatus.NotStarted,
      conditions: [],
      actions: [],
      customFields: {
        type: type,
        isAutomatic: true,
        activityName: fullDiscountPresent.displayName,
      },
      translations: [
        {
          languageCode: LanguageCode.zh,
          name: fullDiscountPresent.displayName,
        },
      ],
    };
    const minimum = fullDiscountPresent.ruleValues[0].minimum;
    //创建满减送参与产品的条件
    promotionInput.conditions.push({
      code: productQuantityContain.code,
      arguments: [
        {name: 'productIds', value: fullDiscountPresent.applicableProduct.productIds},
        {name: 'quantity', value: minimum},
        {name: 'type', value: fullDiscountPresent.applicableProduct.applicableType},
        {name: 'fullDiscountPresentType', value: fullDiscountPresent.type},
      ],
    } as never);
    if (fullDiscountPresent.whetherRestrictUsers) {
      //是否限制参与用户 创建满减送的参与用户的条件
      promotionInput.conditions.push({
        code: customerGroupList.code,
        arguments: [
          {name: 'isOpen', value: fullDiscountPresent.whetherRestrictUsers},
          {name: 'groupType', value: fullDiscountPresent.groupType},
          {name: 'customerGroupIds', value: fullDiscountPresent.memberPlanIds},
        ],
      } as never);
    }
    // 创建优惠类型为循环的满减送的优惠动作
    if (fullDiscountPresent.ruleType === RuleType.Cycle) {
      // const ruleValue = fullDiscountPresent.ruleValues[0];
      promotionInput.actions.push({
        code: circulationDiscount.code,
        arguments: [
          {name: 'productIds', value: fullDiscountPresent.applicableProduct.productIds},
          {name: 'type', value: fullDiscountPresent.applicableProduct.applicableType},
          {name: 'rules', value: fullDiscountPresent.ruleValues},
          {name: 'fullDiscountPresentType', value: fullDiscountPresent.type},
        ],
      } as never);
      // if (ruleValue.discountValue) {
      //   // 创建优惠类型为循环的满减送的优惠动作   满减
      //   promotionInput.actions.push({
      //     code: circulationDiscount.code,
      //     arguments: [
      //       {name: 'discount', value: ruleValue.discountValue.discount},
      //       {name: 'minimum', value: ruleValue.minimum},
      //       {name: 'productIds', value: fullDiscountPresent.applicableProduct.productIds},
      //       {name: 'type', value: fullDiscountPresent.applicableProduct.applicableType},
      //     ],
      //   } as never);
      // }
      // if (ruleValue.freeGiftValues) {
      //   // 创建优惠类型为循环的满减送的优惠动作   满赠
      //   promotionInput.actions.push({
      //     code: circulationPresent.code,
      //     arguments: [
      //       {name: 'minimum', value: ruleValue.minimum},
      //       {name: 'quantityOfGiveaway', value: ruleValue.maximumOffer},
      //       {name: 'freeGiftValues', value: ruleValue.freeGiftValues},
      //       {name: 'productIds', value: fullDiscountPresent.applicableProduct.productIds},
      //       {name: 'type', value: fullDiscountPresent.applicableProduct.applicableType},
      //     ],
      //   } as never);
      // }
      // 创建优惠类型为阶梯的满减送的优惠动作
    } else if (fullDiscountPresent.ruleType === RuleType.Ladder) {
      // 创建优惠类型为阶梯的满减送的优惠动作   满减
      promotionInput.actions.push({
        code: ladderDiscount.code,
        arguments: [
          {name: 'productIds', value: fullDiscountPresent.applicableProduct.productIds},
          {name: 'type', value: fullDiscountPresent.applicableProduct.applicableType},
          {name: 'rules', value: fullDiscountPresent.ruleValues},
          {name: 'fullDiscountPresentType', value: fullDiscountPresent.type},
        ],
      } as never);
      // // 创建优惠类型为阶梯的满减送的优惠动作   满赠
      // promotionInput.actions.push({
      //   code: ladderPresent.code,
      //   arguments: [
      //     {name: 'productIds', value: JSON.stringify(fullDiscountPresent.applicableProduct.productIds)},
      //     {name: 'type', value: fullDiscountPresent.applicableProduct.applicableType},
      //     {name: 'ruleValues', value: JSON.stringify(fullDiscountPresent.ruleValues)},
      //   ],
      // } as never);
    }
    fullDiscountPresent = (await this.findOne(ctx, fullDiscountPresent.id)) as FullDiscountPresent;
    if (fullDiscountPresent?.promotion) {
      const updatePromotion = {
        ...promotionInput,
        id: fullDiscountPresent.promotion.id,
        customFields: {
          type: type,
          isAutomatic: true,
          stackingDiscountSwitch: fullDiscountPresent.stackingDiscountSwitch,
          stackingPromotionTypes: fullDiscountPresent.stackingPromotionTypes,
          activityName: fullDiscountPresent.displayName,
        },
      };
      return (await this.promotionService.updatePromotion(ctx, updatePromotion)) as Promotion;
    } else {
      const promotion = (await this.promotionService.createPromotion(ctx, promotionInput)) as Promotion;
      await this.connection.getRepository(ctx, Promotion).update(promotion.id, {
        customFields: {
          type: type,
          isAutomatic: true,
          stackingDiscountSwitch: fullDiscountPresent.stackingDiscountSwitch,
          stackingPromotionTypes: fullDiscountPresent.stackingPromotionTypes,
          activityName: fullDiscountPresent.displayName,
        },
      });
      return promotion;
    }
  }
  async validate(ctx: RequestContext, input: FullDiscountPresentInput) {
    if (input.startTime > input.endTime) {
      throw new Error('活动开始时间不能大于活动结束时间');
    }
    const ruleValues = input.ruleValues;
    const ruleType = input.ruleType;
    if (ruleValues.length === 0) {
      throw new Error('阶梯满减规则不能为空');
    }
    if (ruleType === RuleType.Cycle) {
      if (ruleValues.length > 1) {
        throw new Error('周期满减规则只能有一个');
      }
    }
    for (const rule of ruleValues) {
      const discountValue = rule.discountValue;
      if (discountValue) {
        if (discountValue.discountType === DiscountType.FixedPercent) {
          if (discountValue.discount >= 100 || discountValue.discount < 0) {
            throw new Error('折扣不能大于100并且不能小于0');
          }
        } else if (discountValue.discountType === DiscountType.FixedAmount) {
          if (discountValue.discount < 0) {
            throw new Error('金额不能小于0');
          }
        } else if (discountValue.discountType === DiscountType.NoDiscount) {
          if (discountValue.discount !== 0) {
            throw new Error('不折扣时金额必须为0');
          }
        }
        if (
          input.type === FullDiscountPresentType.AmountFullPresent &&
          discountValue.discountType !== DiscountType.NoDiscount
        ) {
          throw new Error('实付满赠活动时优惠类型只能为不优惠');
        }
      }
    }
    const inputProduct: ActivityProduct = {
      startTime: input.startTime,
      endTime: input.endTime,
      productIds: input.applicableProduct.productIds as ID[],
      applicableType: input.applicableProduct.applicableType,
    };
    if (input.type !== FullDiscountPresentType.AmountFullPresent) {
      // 验证活动商品和满减送是否冲突
      await this.verifyProductIdsAndFullDiscountConflict(ctx, inputProduct, input.id as ID);
      // 验证活动商品和第X件X折活动是否冲突
      await this.verifyProductIdsAndDiscountActivityConflict(ctx, inputProduct);
      // 验证活动商品和打包一口价活动是否冲突 2024-01-17 产品要求去除打包一口价和满减送的天然互斥
      // await this.verifyProductIdsAndPackageDiscountConflict(ctx, inputProduct);
    } else {
      // 验证实付满赠活动商品和自己是否冲突
      // 实付满赠叠加和不叠加保持互斥，实付满赠叠加之前不互斥(2025-09-04 允许实付满赠叠加)
      const isStacking = !!input.stackingPromotionTypes?.includes(PromotionType.ActuallyPaid);
      await this.verifyProductIdsAndFullDiscountConflict(ctx, inputProduct, input.id as ID, true, isStacking);
    }
  }

  /**
   * 验证活动商品和支付有礼活动是否冲突
   */
  async verifyProductIdsAndPaymentRewardActivityConflict(
    ctx: RequestContext,
    inputProduct: ActivityProduct,
    paymentRewardActivityId?: ID,
  ) {
    const paymentRewardActivities = await this.getNotFailurePaymentRewardActivity(ctx, paymentRewardActivityId);
    for (const paymentRewardActivity of paymentRewardActivities) {
      const paymentRewardActivityProduct: ActivityProduct = {
        startTime: paymentRewardActivity.startTime,
        endTime: paymentRewardActivity.endTime,
        productIds: paymentRewardActivity.applicableProduct.productIds as ID[],
        applicableType: paymentRewardActivity.applicableProduct.applicableType,
      };
      // 判断活动商品是否冲突
      const productId = ActivityUtils.isProductIdConflictWithOtherActivity(paymentRewardActivityProduct, inputProduct);
      if (
        productId &&
        // 判断活动时间是否冲突
        ActivityUtils.isTimeOverlappingWithOtherActivity(paymentRewardActivityProduct, inputProduct)
      ) {
        if (productId === -1) {
          throw new Error(`活动商品和支付有礼活动商品冲突,存在活动商品为全部商品的活动`);
        }
        const product = await this.productService.findOne(ctx, productId);
        throw new Error(`活动商品${product?.name}和支付有礼活动商品冲突`);
      }
    }
  }

  /**
   * 验证活动商品和任选增送活动是否冲突
   */
  async verifyProductIdsAndSelectiveGiftActivityConflict(
    ctx: RequestContext,
    inputProduct: ActivityProduct,
    selectiveGiftActivityId?: ID,
  ) {
    const selectiveGiftActivities = await this.getNotFailureSelectiveGiftActivity(ctx, selectiveGiftActivityId);
    for (const selectiveGiftActivity of selectiveGiftActivities) {
      const selectiveGiftActivityProduct: ActivityProduct = {
        startTime: selectiveGiftActivity.startTime,
        endTime: selectiveGiftActivity.endTime,
        productIds: selectiveGiftActivity.applicableProduct.productIds as ID[],
        applicableType: selectiveGiftActivity.applicableProduct.applicableType,
      };
      // 判断活动商品是否冲突
      const productId = ActivityUtils.isProductIdConflictWithOtherActivity(selectiveGiftActivityProduct, inputProduct);
      if (
        productId &&
        // 判断活动时间是否冲突
        ActivityUtils.isTimeOverlappingWithOtherActivity(selectiveGiftActivityProduct, inputProduct)
      ) {
        if (productId === -1) {
          throw new Error(`活动商品和任选增送活动商品冲突,存在活动商品为全部商品的活动`);
        }
        const product = await this.productService.findOne(ctx, productId);
        throw new Error(`活动商品${product?.name}和任选增送活动商品冲突`);
      }
    }
  }
  /**
   * 验证活动商品和满减送是否冲突
   * @param ctx
   * @param inputProduct
   */
  async verifyProductIdsAndFullDiscountConflict(
    ctx: RequestContext,
    inputProduct: ActivityProduct,
    fullDiscountPresentId?: ID,
    isAmountFullPresent = false,
    isStacking = false,
  ) {
    let fullDiscountPresents = await this.getNotFailureFullDiscountPresent(
      ctx,
      fullDiscountPresentId,
      isAmountFullPresent,
    );
    if (isStacking) {
      // 过滤掉叠加活动的冲突判定
      fullDiscountPresents = fullDiscountPresents.filter(
        item => !item.stackingPromotionTypes?.includes(PromotionType.ActuallyPaid),
      );
    }
    for (const fullDiscountPresent of fullDiscountPresents) {
      const fullDiscountPresentProduct: ActivityProduct = {
        startTime: fullDiscountPresent.startTime,
        endTime: fullDiscountPresent.endTime,
        productIds: fullDiscountPresent.applicableProduct.productIds as ID[],
        applicableType: fullDiscountPresent.applicableProduct.applicableType,
      };
      // 判断活动商品是否冲突
      const productId = ActivityUtils.isProductIdConflictWithOtherActivity(fullDiscountPresentProduct, inputProduct);
      //productId存在说明冲突 则需要判断时间是否冲突
      if (
        // 判断活动时间是否冲突
        productId &&
        ActivityUtils.isTimeOverlappingWithOtherActivity(fullDiscountPresentProduct, inputProduct)
      ) {
        if (productId === -1) {
          throw new Error(`活动商品和满减满赠活动商品冲突,存在活动商品为全部商品的活动`);
        }
        const product = await this.productService.findOne(ctx, productId);
        throw new Error(`活动商品${product?.name}和满减送活动商品冲突`);
      }
    }
  }

  /**
   * 验证活动商品和第X件X折活动是否冲突
   * @param ctx
   * @param inputProduct 活动商品
   * @returns
   */
  async verifyProductIdsAndDiscountActivityConflict(
    ctx: RequestContext,
    inputProduct: ActivityProduct,
    discountActivityId?: ID,
  ) {
    const discountActivitiesList = await this.getNotFailureDiscountActivity(ctx, discountActivityId);
    for (const discountActivities of discountActivitiesList) {
      const discountActivitiesProduct: ActivityProduct = {
        startTime: discountActivities.startTime,
        endTime: discountActivities.endTime,
        productIds: discountActivities.productIds,
        applicableType: ApplicableType.AvailableGoods,
      };
      // 判断活动商品是否冲突
      const productId = ActivityUtils.isProductIdConflictWithOtherActivity(discountActivitiesProduct, inputProduct);
      if (
        productId &&
        // 判断活动时间是否冲突
        ActivityUtils.isTimeOverlappingWithOtherActivity(discountActivitiesProduct, inputProduct)
      ) {
        if (productId === -1) {
          throw new Error(`活动商品和第X件X折活动商品冲突,存在活动商品为全部商品的活动`);
        }
        const product = await this.productService.findOne(ctx, productId);
        throw new Error(`活动商品${product?.name}和第X件X折活动商品冲突`);
      }
    }
  }

  /**
   * 验证活动商品和打包一口价活动是否冲突
   */
  async verifyProductIdsAndPackageDiscountConflict(
    ctx: RequestContext,
    inputProduct: ActivityProduct,
    packageDiscountId?: ID,
  ) {
    const packageDiscounts = await this.getNotFailurePackageDiscount(ctx, packageDiscountId);
    for (const packageDiscount of packageDiscounts) {
      const packageDiscountProduct: ActivityProduct = {
        startTime: packageDiscount.startTime,
        endTime: packageDiscount.endTime,
        productIds: packageDiscount.productIds as ID[],
        applicableType: ApplicableType.AvailableGoods,
      };
      // 判断活动商品是否冲突
      const productId = ActivityUtils.isProductIdConflictWithOtherActivity(packageDiscountProduct, inputProduct);
      if (productId && ActivityUtils.isTimeOverlappingWithOtherActivity(packageDiscountProduct, inputProduct)) {
        if (productId === -1) {
          throw new Error(`活动商品和打包一口价活动商品冲突,存在活动商品为全部商品的活动`);
        }
        const product = await this.productService.findOne(ctx, productId);
        throw new Error(`活动商品${product?.name}和打包一口价活动商品冲突`);
      }
    }
  }
  /**
   * 获取未失效的任选增送活动
   * @param ctx
   * @param selectiveGiftActivityId
   * @param isAmountSelectiveGift
   */
  async getNotFailureSelectiveGiftActivity(ctx: RequestContext, selectiveGiftActivityId?: ID) {
    const qb = this.listQueryBuilder.build(
      SelectiveGiftActivity,
      {},
      {
        relations: ['promotion'],
        ctx,
        channelId: ctx.channelId,
      },
    );
    if (selectiveGiftActivityId) {
      qb.andWhere(`${qb.alias}.id != :id`, {id: selectiveGiftActivityId});
    }
    //活动状态不能为已结束和已失效
    qb.andWhere(`${qb.alias}.status not in(:...status)`, {status: [ActivityStatus.Failure, ActivityStatus.HaveEnded]});
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    const selectiveGiftActivities = await qb.getMany();
    return selectiveGiftActivities;
  }

  /**
   * 获取未失效的支付有礼活动
   */
  getNotFailurePaymentRewardActivity(ctx: RequestContext, paymentRewardActivityId?: ID) {
    const qb = this.listQueryBuilder.build(
      PaymentRewardActivity,
      {},
      {
        ctx,
        channelId: ctx.channelId,
      },
    );
    if (paymentRewardActivityId) {
      qb.andWhere(`${qb.alias}.id != :id`, {id: paymentRewardActivityId});
    }
    //活动状态不能为已结束和已失效
    qb.andWhere(`${qb.alias}.status not in(:...status)`, {status: [ActivityStatus.Failure, ActivityStatus.HaveEnded]});
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    return qb.getMany();
  }

  /**
   * 获取未失效的打包一口价活动
   * @param ctx
   * @param packageDiscountId
   */
  async getNotFailurePackageDiscount(ctx: RequestContext, packageDiscountId?: ID) {
    const qb = this.listQueryBuilder.build(
      PackageDiscount,
      {},
      {
        relations: ['promotion'],
        ctx,
        channelId: ctx.channelId,
      },
    );
    if (packageDiscountId) {
      qb.andWhere(`${qb.alias}.id != :id`, {id: packageDiscountId});
    }
    //活动状态不能为已结束和已失效
    qb.andWhere(`${qb.alias}.status not in(:...status)`, {status: [ActivityStatus.Failure, ActivityStatus.HaveEnded]});
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    const packageDiscounts = await qb.getMany();
    return packageDiscounts;
  }

  /**
   * 获取未失效的满减送活动
   * @param ctx
   * @param id 不包含的活动id
   * @param type 不包含的活动类型
   * @returns
   */
  async getNotFailureFullDiscountPresent(ctx: RequestContext, id?: ID, isAmountFullPresent = false) {
    const qb = this.listQueryBuilder.build(
      FullDiscountPresent,
      {},
      {
        relations: ['promotion'],
        ctx,
        channelId: ctx.channelId,
      },
    );
    if (id) {
      qb.andWhere(`${qb.alias}.id != :id`, {id});
    }
    // 活动类型不为实付满赠
    if (!isAmountFullPresent) {
      qb.andWhere(`${qb.alias}.type != :type`, {type: FullDiscountPresentType.AmountFullPresent});
    } else {
      qb.andWhere(`${qb.alias}.type = :type`, {type: FullDiscountPresentType.AmountFullPresent});
    }
    //活动状态不能为已结束和已失效
    qb.andWhere(`${qb.alias}.status not in(:...status)`, {status: [ActivityStatus.Failure, ActivityStatus.HaveEnded]});
    // qb.andWhere(`${qb.alias}.status != :status`, {status: ActivityStatus.HaveEnded});
    // qb.andWhere(`${qb.alias}.status != :status`, {status: ActivityStatus.Failure});
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    const fullDiscountPresents = await qb.getMany();
    return fullDiscountPresents;
  }

  /**
   * 获取未失效的第X件X折活动
   * @param ctx
   * @param id 不包含的活动id
   * @returns
   */
  async getNotFailureDiscountActivity(ctx: RequestContext, id?: ID) {
    const qb = this.listQueryBuilder.build(
      DiscountActivity,
      {},
      {
        relations: ['promotion'],
        ctx,
        channelId: ctx.channelId,
      },
    );
    if (id) {
      qb.andWhere(`${qb.alias}.id != :id`, {id});
    }
    //活动状态不能为已结束和已失效
    qb.andWhere(`${qb.alias}.status not in(:...status)`, {status: [ActivityStatus.Failure, ActivityStatus.HaveEnded]});
    // qb.andWhere(`${qb.alias}.status != :status`, {status: ActivityStatus.Failure});
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    const discountActivities = await qb.getMany();
    return discountActivities;
  }

  async failureFullDiscountPresent(ctx: RequestContext, id: ID) {
    const fullDiscountPresent = await this.findOne(ctx, id);
    if (!fullDiscountPresent) {
      throw new Error(`FullDiscountPresent with id ${id} not found`);
    }
    fullDiscountPresent.status = ActivityStatus.Failure;
    await this.connection.getRepository(ctx, Promotion).update(fullDiscountPresent.promotion.id, {enabled: false});
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, FullDiscountPresent).save(fullDiscountPresent);
    await this.cacheService.removeCache([
      CacheKeyManagerService.fullDiscountPresent(id, ctx.channelId),
      CacheKeyManagerService.promotion(fullDiscountPresent.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(fullDiscountPresent.promotion.id, ctx.channelId),
    ]);
    await this.productPromotionActiveService.activeProductCacheClear(ctx, fullDiscountPresent.applicableProduct);
    return this.findOne(ctx, id);
  }
  async softDeleteFullDiscountPresent(ctx: RequestContext, id: ID) {
    const fullDiscountPresent = await this.findOne(ctx, id);
    if (!fullDiscountPresent) {
      throw new Error(`FullDiscountPresent with id ${id} not found`);
    }
    fullDiscountPresent.deletedAt = new Date();
    fullDiscountPresent.status = ActivityStatus.Failure;
    await this.connection.getRepository(ctx, Promotion).update(fullDiscountPresent.promotion.id, {enabled: false});
    await this.promotionService.softDeletePromotion(ctx, fullDiscountPresent.promotion.id);
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, FullDiscountPresent).save(fullDiscountPresent);
    await this.cacheService.removeCache([
      CacheKeyManagerService.fullDiscountPresent(id, ctx.channelId),
      CacheKeyManagerService.promotion(fullDiscountPresent.promotion.id, ctx.channelId),
      CacheKeyManagerService.promotionIncludeFailure(fullDiscountPresent.promotion.id, ctx.channelId),
    ]);
    await this.productPromotionActiveService.activeProductCacheClear(ctx, fullDiscountPresent.applicableProduct);
    return {
      result: DeletionResult.Deleted,
      message: 'delete success',
    };
  }
  @cacheableAccess({
    cacheKeyFn: (
      ctx: RequestContext,
      id: ID,
      options?: ListQueryOptions<FullDiscountPresent>,
      relations?: RelationPaths<FullDiscountPresent>,
    ) => {
      if (ctx.apiType === 'shop') {
        return CacheKeyManagerService.fullDiscountPresent(id, ctx.channelId);
      }
      return '';
    },
  })
  async findOne(
    ctx: RequestContext,
    id: ID,
    options?: ListQueryOptions<FullDiscountPresent>,
    relations?: RelationPaths<FullDiscountPresent>,
  ) {
    let fullDiscountPresents: FullDiscountPresent[] | undefined;
    const memoryStorageCacheKey = CacheKeyManagerService.fullDiscountPresent(id, ctx.channelId);
    if (ctx.apiType === 'shop') {
      fullDiscountPresents = this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!fullDiscountPresents) {
      const qb = this.listQueryBuilder.build(FullDiscountPresent, options, {
        relations: [], //(relations ?? []).concat(['promotion']),
        ctx,
        channelId: ctx.channelId,
      });
      qb.andWhere(`${qb.alias}.id = :id`, {id});
      if (ctx.apiType === 'shop') {
        qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
      fullDiscountPresents = await qb.getMany();
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, fullDiscountPresents);
      }
    }
    if (fullDiscountPresents.length > 0) {
      const fullDiscountPresent = fullDiscountPresents[0];
      const promotionId = fullDiscountPresent.promotionId;
      if (promotionId) {
        const promotionMemoryStorageCacheKey = CacheKeyManagerService.promotion(promotionId, ctx.channelId);
        const cache =
          ctx.apiType === 'shop'
            ? {
                id: promotionMemoryStorageCacheKey,
                milliseconds: DEFAULT_CACHE_TIMEOUT,
              }
            : false;
        let promotion: Promotion | undefined | null;
        if (ctx.apiType === 'shop') {
          promotion = this.memoryStorageService.get(promotionMemoryStorageCacheKey);
        }
        if (!promotion) {
          promotion = await this.connection.getRepository(ctx, Promotion).findOne({
            where: {id: promotionId},
            cache: cache,
          });
        }
        fullDiscountPresent.promotion = promotion as Promotion;
      }
      return fullDiscountPresents[0];
    }
    return null;
  }
  async findAll(
    ctx: RequestContext,
    options: ListQueryOptions<FullDiscountPresent>,
    relations: RelationPaths<FullDiscountPresent>,
    isAdmin = false,
  ) {
    const qb = this.listQueryBuilder.build(FullDiscountPresent, options, {
      relations,
      ctx,
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    qb.leftJoinAndSelect(`${qb.alias}.promotion`, 'promotion');
    const [items, totalItems] = await qb.getManyAndCount();
    if (isAdmin && items?.length > 0) {
      for (const item of items) {
        let type = PromotionType.FullDiscountPresent;
        if (item.type === FullDiscountPresentType.AmountFullPresent) {
          type = PromotionType.ActuallyPaid;
        }
        const statisticsPromotion = await this.promotionResultDetailService.statisticsPromotion(
          ctx,
          item.promotion.id,
          type,
        );
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (item as any).statisticsData = statisticsPromotion;
      }
    }
    return {
      items,
      totalItems,
    };
  }

  async findOneByPromotionId(ctx: RequestContext, promotionId: ID) {
    const fullDiscountPresent = await this.connection
      .getRepository(ctx, FullDiscountPresent)
      .createQueryBuilder('fullDiscountPresent')
      .leftJoin('fullDiscountPresent.channels', 'channels')
      .leftJoinAndSelect('fullDiscountPresent.promotion', 'promotion')
      .where('promotion.id = :promotionId', {promotionId})
      .andWhere('channels.id = :channelId', {channelId: ctx.channelId})
      .take(1)
      .getOne();
    return fullDiscountPresent;
  }

  // async freeGiftByOrder(ctx: RequestContext, orderId: ID) {
  //   const order = await this.connection.getRepository(ctx, Order).findOne(orderId);
  //   if (!order) {
  //     throw new Error(`Order with id ${orderId} not found`);
  //   }
  //   const fullDiscountPresentIds = await this.getUsableFullDiscountPresent(ctx, order);
  //   if (fullDiscountPresentIds.length === 0) {
  //     return;
  //   }
  //   const qb = this.listQueryBuilder.build(FullDiscountPresent, undefined, {
  //     relations: ['promotion'],
  //     channelId: ctx.channelId,
  //   });
  //   qb.andWhere(`${qb.alias}.id in (:...ids)`, {ids: fullDiscountPresentIds});
  //   const fullDiscountPresents = await qb.getMany();
  //   const freeGiftIds: ID[] = [];
  //   for (const item of fullDiscountPresents) {
  //     const applicableProductId = item.applicableProduct.productIds as ID[];
  //     const applicableType = item.applicableProduct.applicableType;
  //     let index = 0;
  //     if (item.ruleType === RuleType.Ladder) {
  //       const totalQuantity = await this.checkerIsRestricted.getTotalQuantity(
  //         ctx,
  //         order.id,
  //         applicableProductId,
  //         applicableType,
  //         item.promotion,
  //       );
  //       //循环判断满足阶梯的优惠
  //       for (let i = 0; i < item.ruleValues.length; i++) {
  //         const ruleValue = item.ruleValues[i] as RuleValue;
  //         if (totalQuantity >= ruleValue.minimum) {
  //           index = i;
  //         }
  //       }
  //     }
  //     const ruleValue = item.ruleValues[index] as RuleValue;
  //     if (ruleValue) {
  //       const freeGiftValues = ruleValue.freeGiftValues;
  //       if (freeGiftValues) {
  //         for (const freeGiftValue of freeGiftValues) {
  //           if (freeGiftValue?.freeGiftId) {
  //             freeGiftIds.push(freeGiftValue?.freeGiftId);
  //           }
  //         }
  //       }
  //     }
  //   }
  //   const freeGifts = await this.freeGiftService.findFreeGifts(ctx, freeGiftIds);
  //   return freeGifts;
  // }

  // async getUsableFullDiscountPresent(ctx: RequestContext, order: Order) {
  //   const fullDiscountPresent = await this.getActiveFullDiscountPresent(ctx);
  //   const fullDiscountPresentIds = [];
  //   for (const item of fullDiscountPresent) {
  //     const promotion = item.promotion;
  //     const availableState = await this.couponService.verifyTheOfferIsAvailable(ctx, promotion, order);
  //     if (availableState) {
  //       fullDiscountPresentIds.push(item.id);
  //     }
  //   }
  //   return fullDiscountPresentIds;
  // }

  // async getActiveFullDiscountPresent(
  //   ctx: RequestContext,
  //   options?: ListQueryOptions<FullDiscountPresent>,
  //   relations?: RelationPaths<FullDiscountPresent>,
  // ) {
  //   const qb = this.listQueryBuilder.build(FullDiscountPresent, options, {
  //     relations: relations ?? ['promotion'],
  //     channelId: ctx.channelId,
  //   });
  //   qb.andWhere(`${qb.alias}.status = :status`, {status: ActivityStatus.Normal});
  //   qb.andWhere(`${qb.alias}.deletedAt is null`);
  //   return qb.getMany();
  // }

  // async addGiftToOrder(ctx: RequestContext, orderId: ID, productVariantId: ID, quantity = 1) {
  //   const productVariant = await this.productVariantService.findOne(ctx, productVariantId);
  //   if (!productVariant) {
  //     throw new EntityNotFoundError('ProductVariant', productVariantId);
  //   }
  //   if (!(productVariant.product.customFields as ProductCustomFields).freeGift) {
  //     throw new Error(`ProductVariant with id ${productVariantId} is not a free gift`);
  //   }
  //   const freeGifts = await this.freeGiftByOrder(ctx, orderId);
  //   if (!freeGifts) {
  //     throw new Error('该订单不可选择赠品');
  //   }
  //   const freeGiftProductIds = freeGifts.map(freeGift => freeGift.product.id);
  //   if (!freeGiftProductIds.includes(productVariant.productId)) {
  //     throw new Error('该订单可选择的赠品不包括该赠品');
  //   }
  //   await this.removeGiftFromOrder(ctx, orderId);
  //   await this.orderService.addItemToOrder(ctx, orderId, productVariantId, quantity);
  //   const order = await this.orderService.findOne(ctx, orderId, [
  //     'lines',
  //     'promotions',
  //     'customer',
  //     'lines.productVariant',
  //   ]);
  //   if (!order) {
  //     throw new Error('order not exist');
  //   }
  //   for (const line of order.lines) {
  //     if (line.productVariant.id === productVariantId) {
  //       await this.orderService.adjustOrderLine(ctx, order.id, line.id, line.quantity, {
  //         purchasePattern: PurchasePattern.Gift,
  //       });
  //       break;
  //     }
  //   }
  //   return this.orderService.findOne(ctx, orderId);
  // }

  async removeGiftFromOrder(ctx: RequestContext, orderId: ID) {
    throw new Error('not implemented');
    // const order = await this.orderService.findOne(ctx, orderId, ['lines']);
    // if (!order) {
    //   throw new Error('order not exist');
    // }
    // const orderLines = order.lines;
    // for (const line of orderLines) {
    //   if ((line.customFields as OrderLineCustomFields).purchasePattern === PurchasePattern.Gift) {
    //     await this.orderService.adjustOrderLine(ctx, order.id, line.id, line.quantity - 1, {
    //       purchasePattern: PurchasePattern.Ordinary,
    //     });
    //   }
    // }
    // return this.orderService.findOne(ctx, orderId);
  }

  activitySynopsis(ctx: RequestContext, fullDiscountPresent: FullDiscountPresent) {
    let synopsisStr = ``;
    const synopsisTags: string[] = [];
    const ruleValues = fullDiscountPresent.ruleValues;
    const ruleType = fullDiscountPresent.ruleType;
    const fullDiscountPresentType = fullDiscountPresent.type;
    if (ruleType === RuleType.Ladder) {
      const maxDiscount = ruleValues[ruleValues.length - 1];
      if (maxDiscount.discountValue?.discountType === DiscountType.FixedAmount && maxDiscount.discountValue.discount) {
        synopsisStr += `最高减${maxDiscount.discountValue?.discount / 100}元`;
      } else if (maxDiscount.discountValue?.discountType === DiscountType.FixedPercent) {
        synopsisStr += `最高打${maxDiscount.discountValue?.discount / 10}折`;
      }
      if (maxDiscount.freeGiftValues && maxDiscount.freeGiftValues.length > 0) {
        if (synopsisStr !== ``) {
          synopsisStr += `，送${maxDiscount.maximumOffer}种赠品`;
        } else {
          synopsisStr += `最高送${maxDiscount.maximumOffer}种赠品`;
        }
      }
      ruleValues.forEach(ruleValue => {
        let commonStr = '';
        if (fullDiscountPresentType === FullDiscountPresentType.QuantityFullReduction) {
          commonStr = `满${ruleValue.minimum}件`;
        } else if (
          fullDiscountPresentType === FullDiscountPresentType.AmountFullReduction ||
          fullDiscountPresentType === FullDiscountPresentType.AmountFullPresent
        ) {
          commonStr = `满${ruleValue.minimum / 100}元`;
        }
        if (ruleValue.discountValue?.discountType === DiscountType.FixedAmount && ruleValue.discountValue.discount) {
          synopsisTags.push(`${commonStr}减${ruleValue.discountValue?.discount / 100}元`);
        } else if (ruleValue.discountValue?.discountType === DiscountType.FixedPercent) {
          synopsisTags.push(`${commonStr}打${ruleValue.discountValue?.discount / 10}折`);
        }
      });
    } else if (ruleType === RuleType.Cycle) {
      const discount = ruleValues[0];
      let commonStr = '';
      if (fullDiscountPresentType === FullDiscountPresentType.QuantityFullReduction) {
        commonStr = `满${discount.minimum}件`;
      } else if (
        fullDiscountPresentType === FullDiscountPresentType.AmountFullReduction ||
        fullDiscountPresentType === FullDiscountPresentType.AmountFullPresent
      ) {
        commonStr = `满${discount.minimum / 100}元`;
      }
      if (discount.discountValue && discount.freeGiftValues && discount.freeGiftValues.length > 0) {
        synopsisStr += `每${commonStr} 减${discount.discountValue?.discount / 100}元,送${discount.maximumOffer}种赠品`;
      } else if (discount.discountValue) {
        synopsisStr += `每${commonStr} 减${discount.discountValue?.discount / 100}元`;
      } else if (discount.freeGiftValues && discount.freeGiftValues.length > 0) {
        synopsisStr += `${commonStr} 送${discount.maximumOffer}种赠品`;
      } else {
        synopsisStr += `满减送活动`;
      }
    }
    return {synopsisStr, synopsisTags};
  }

  activityContent(ctx: RequestContext, fullDiscountPresent: FullDiscountPresent) {
    const fullDiscountPresentType = fullDiscountPresent.type;
    const ruleValues = fullDiscountPresent.ruleValues;
    const ruleType = fullDiscountPresent.ruleType;
    if (ruleType === RuleType.Ladder) {
      return this.ladderRuleValues(ctx, ruleValues, fullDiscountPresentType);
    } else if (ruleType === RuleType.Cycle) {
      return this.cycleRuleValues(ctx, ruleValues, fullDiscountPresentType);
    }
  }
  async cycleRuleValues(
    ctx: RequestContext,
    ruleValues: RuleValue[],
    fullDiscountPresentType: FullDiscountPresentType,
  ) {
    const discount = ruleValues[0]?.discountValue?.discount || 0;
    const discountType = ruleValues[0]?.discountValue?.discountType;
    let fullMinus = '';
    let fullPresent = '';
    let common = '';
    if (fullDiscountPresentType === FullDiscountPresentType.QuantityFullReduction) {
      common = `每满${ruleValues[0].minimum}件`;
    } else if (fullDiscountPresentType === FullDiscountPresentType.AmountFullReduction) {
      common = `每满${ruleValues[0].minimum / 100}元`;
    } else if (fullDiscountPresentType === FullDiscountPresentType.AmountFullPresent) {
      common = `实付满${ruleValues[0].minimum / 100}元`;
    }
    if (discountType === DiscountType.FixedAmount) {
      fullMinus = `${common}减${discount / 100}元`;
    } else if (discountType === DiscountType.FixedPercent) {
      fullMinus += `${common}打${discount / 10}折`;
    }
    let fullPresentArray: string[] = [];
    if (ruleValues[0].freeGiftValues && ruleValues[0].freeGiftValues.length > 0) {
      fullPresent = `${common},送${ruleValues[0]?.maximumOffer}种赠品`;
      const freeGiftValues = ruleValues[0].freeGiftValues as FreeGiftValue[];
      const giveaway = await this.getGiftsStr(ctx, freeGiftValues);
      if (giveaway?.length > 0) {
        fullPresent += `【${giveaway.join('、')}】`;
      } else {
        fullPresent += ` 暂无赠品`;
      }
      fullPresentArray = [fullPresent];
    }

    return {fullMinus: [fullMinus], fullPresent: fullPresentArray};
  }
  async ladderRuleValues(
    ctx: RequestContext,
    ruleValues: RuleValue[],
    fullDiscountPresentType: FullDiscountPresentType,
  ) {
    const fullDiscount = [];
    const fullMinus = [];
    const fullPresent = [];
    for (const ruleValue of ruleValues) {
      let commonStr = '';
      if (fullDiscountPresentType === FullDiscountPresentType.QuantityFullReduction) {
        commonStr = `满${ruleValue.minimum}件`;
      } else if (fullDiscountPresentType === FullDiscountPresentType.AmountFullReduction) {
        commonStr = `满${ruleValue.minimum / 100}元`;
      } else if (fullDiscountPresentType === FullDiscountPresentType.AmountFullPresent) {
        commonStr = `实付满${ruleValue.minimum / 100}元`;
      }

      if (ruleValue.discountValue?.discountType === DiscountType.FixedAmount) {
        const fullMinusStr = `${commonStr}减${ruleValue.discountValue?.discount / 100}元;`;
        fullMinus.push(fullMinusStr);
      } else if (ruleValue.discountValue?.discountType === DiscountType.FixedPercent) {
        const fullDiscountStr = `${commonStr}打${ruleValue.discountValue?.discount / 10}折;`;
        fullDiscount.push(fullDiscountStr);
      }
      if (ruleValue.freeGiftValues && ruleValue.freeGiftValues.length > 0) {
        const fullPresentStr = `${commonStr},送${ruleValue.maximumOffer}种赠品`;
        const freeGiftValues = ruleValue.freeGiftValues as FreeGiftValue[];
        const giveaway = await this.getGiftsStr(ctx, freeGiftValues);
        if (giveaway?.length > 0) {
          fullPresent.push(`${fullPresentStr}【${giveaway.join('、')}】`);
        } else {
          fullPresent.push(`${fullPresentStr} 暂无赠品`);
        }
      }
    }
    return {fullDiscount, fullMinus, fullPresent};
  }

  async getGiftsStr(ctx: RequestContext, freeGiftValues: FreeGiftValue[]) {
    const giveaway = [];
    for (const freeGiftValue of freeGiftValues) {
      if (freeGiftValue?.freeGiftId) {
        const productId = freeGiftValue?.freeGiftProductId;
        if (productId) {
          const filterGiftProduct = await this.filterGiftProduct(ctx, productId);
          if (filterGiftProduct.isFiltered) {
            continue;
          }
        }
        const freeGift = await this.freeGiftService.findOne(ctx, freeGiftValue?.freeGiftId);
        if (freeGift) {
          giveaway.push(`${freeGiftValue.maximumOffer}件【${freeGift.name}】`);
        }
      }
    }
    return giveaway;
  }

  async filterGiftProduct(ctx: RequestContext, productId: ID): Promise<{isFiltered: boolean}> {
    const variantList = await this.customerProductVariantService.getVariantsByProductId(ctx, productId);
    // 若无变体数据，直接过滤
    if (!variantList || variantList.items?.length <= 0) {
      return {isFiltered: true};
    }
    const product = variantList.items[0].product;
    // 若商品已删除或被禁用，直接过滤
    if (!product?.enabled || product.deletedAt) {
      return {isFiltered: true};
    }

    const productVariants = variantList.items;
    let isFiltered = true;

    // 检查特定 SKU 是否有库存
    const checkStock = async (variant: ProductVariant): Promise<boolean> => {
      // SKU 被禁用或者已删除时直接过滤
      if (!variant.enabled || variant.deletedAt) {
        return false;
      }
      const {stockOnHand, stockAllocated} = await this.customerProductVariantService.getAvailableStock(ctx, variant);
      return stockOnHand - stockAllocated - variant.outOfStockThreshold > 0;
    };
    if (isFiltered) {
      // 当前 SKU 无库存，检查其他变体是否有库存
      for (const variant of productVariants) {
        if (await checkStock(variant)) {
          isFiltered = false;
          break;
        }
      }
    }
    return {isFiltered};
  }

  async activityGifts(ctx: RequestContext, fullDiscountPresent: FullDiscountPresent) {
    const ruleValues = fullDiscountPresent.ruleValues;
    const freeGiftItems: FreeGiftValue[] = [];
    for (const ruleValue of ruleValues) {
      const freeGiftValues = ruleValue.freeGiftValues;
      if (freeGiftValues) {
        for (const freeGiftValue of freeGiftValues) {
          if (freeGiftValue) {
            // productIds.push(freeGiftValue?.freeGiftProductId);
            freeGiftItems.push(freeGiftValue);
          }
        }
      }
    }
    const productIds = freeGiftItems.map(item => item.freeGiftProductId) as ID[];
    if (productIds.length === 0) {
      return [];
    }
    let products = await this.productService.findByIds(ctx, productIds);
    products = products.sort((a, b) => {
      const itemA = freeGiftItems.find(item => idsAreEqual(item.freeGiftProductId as ID, a.id));
      const itemB = freeGiftItems.find(item => idsAreEqual(item.freeGiftProductId as ID, b.id));
      const priorityA = itemA?.priority ?? 0;
      const priorityB = itemB?.priority ?? 0;
      return priorityA - priorityB;
    });
    return products;
  }

  async fullReductionAndFullGiftAll() {
    const ctxs = await this.productCustomService.getAllCtxs();
    for (const ctx of ctxs) {
      await this.fullReductionAndFullGift(ctx);
    }
  }

  async fullReductionAndFullGift(ctx: RequestContext) {
    const notYetStarted = await this.connection
      .getRepository(ctx, FullDiscountPresent)
      .createQueryBuilder('fullDiscountPresent')
      .leftJoinAndSelect('fullDiscountPresent.channels', 'channels')
      .leftJoinAndSelect('fullDiscountPresent.promotion', 'promotion')
      .andWhere('fullDiscountPresent.status = :status', {status: ActivityStatus.NotStarted})
      .andWhere('fullDiscountPresent.startTime <= :startTime', {startTime: new Date()})
      .andWhere('fullDiscountPresent.endTime > :endTime', {endTime: new Date()})
      .getMany();
    const notYetStartedIds = notYetStarted.map(item => item.id);
    const promotionIds = notYetStarted.map(item => item.promotion.id);
    if (notYetStartedIds && notYetStartedIds.length > 0) {
      for (const item of notYetStarted) {
        const channels = item?.channels;
        const channelIds = this.commonService.filterDefaultChannel(channels);
        await this.commonService.activeOrderTime(ctx, channelIds);
      }
      await this.connection
        .getRepository(ctx, FullDiscountPresent)
        .update({id: In(notYetStartedIds)}, {status: ActivityStatus.Normal});
      const notYetStartedKeys = notYetStartedIds.map(id =>
        CacheKeyManagerService.fullDiscountPresent(id, ctx.channelId),
      );
      await this.cacheService.removeCache(notYetStartedKeys);
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      notYetStarted.forEach(async item => {
        await this.productPromotionActiveService.activeProductCacheClear(ctx, item.applicableProduct);
      });
    }
    if (promotionIds && promotionIds.length > 0) {
      await this.connection.getRepository(ctx, Promotion).update({id: In(promotionIds)}, {enabled: true});
      const promotionKeys = [
        ...promotionIds.map(id => CacheKeyManagerService.promotion(id, ctx.channelId)),
        ...promotionIds.map(id => CacheKeyManagerService.promotionIncludeFailure(id, ctx.channelId)),
      ];
      await this.cacheService.removeCache(promotionKeys);
    }
    const haveEnded = await this.connection
      .getRepository(ctx, FullDiscountPresent)
      .createQueryBuilder('fullDiscountPresent')
      .leftJoinAndSelect('fullDiscountPresent.channels', 'channels')
      .leftJoinAndSelect('fullDiscountPresent.promotion', 'promotion')
      .andWhere('fullDiscountPresent.status = :status', {status: ActivityStatus.Normal})
      .andWhere('fullDiscountPresent.endTime <= :endTime', {endTime: new Date()})
      .getMany();
    const haveEndedIds = haveEnded.map(item => item.id);
    const haveEndedPromotionIds = haveEnded.map(item => item.promotion.id);
    if (haveEndedIds && haveEndedIds.length > 0) {
      for (const item of haveEnded) {
        const channels = item?.channels;
        const channelIds = this.commonService.filterDefaultChannel(channels);
        await this.commonService.activeOrderTime(ctx, channelIds);
      }
      await this.connection
        .getRepository(ctx, FullDiscountPresent)
        .update({id: In(haveEndedIds)}, {status: ActivityStatus.HaveEnded});
      const haveEndedKeys = haveEndedIds.map(id => CacheKeyManagerService.fullDiscountPresent(id, ctx.channelId));
      await this.cacheService.removeCache(haveEndedKeys);
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      haveEnded.forEach(async item => {
        await this.productPromotionActiveService.activeProductCacheClear(ctx, item.applicableProduct);
      });
    }
    if (haveEndedPromotionIds && haveEndedPromotionIds.length > 0) {
      await this.connection.getRepository(ctx, Promotion).update({id: In(haveEndedPromotionIds)}, {enabled: false});
      const promotionKeys = [
        ...haveEndedPromotionIds.map(id => CacheKeyManagerService.promotion(id, ctx.channelId)),
        ...haveEndedPromotionIds.map(id => CacheKeyManagerService.promotionIncludeFailure(id, ctx.channelId)),
      ];
      await this.cacheService.removeCache(promotionKeys);
    }
  }
}
