import {Injectable} from '@nestjs/common';
import {
  ID,
  ListQueryBuilder,
  ListQueryOptions,
  RelationPaths,
  RequestContext,
  TransactionalConnection,
  UnauthorizedError,
  Transaction,
  Logger,
  Customer,
  Order,
  PaymentMethodService,
  OrderEvent,
  EventBus,
} from '@vendure/core';
import {
  DistributorOrder,
  SubscriptionMessageRecord,
  TemplateConfig,
  WishBoxActivity,
  WishBoxActivityBuy,
  WishBoxActivityOpenStrategy,
  WishBoxActivityPick,
  WishBoxActivityPrize,
  WishBoxActivityRecord,
  WishBoxActivityRecordItem,
} from '../entities';
import {
  WishBoxActivityPeriodUnit,
  WishBoxActivityInput,
  WishBoxActivityOpenStrategyStatus,
  WishBoxActivityPrizeStatus,
  WishBoxActivityStatus,
  WishBoxType,
  WishBoxActivityRecordStatus,
  WishBoxActivityPrizeValidityMode,
  WishBoxActivityBuyPayStatus,
  WishBoxActivityBuyStatus,
} from '../generated-admin-types';
import {CustomerService} from '@vendure/core';
import {EntityNotFoundError, FindOptionsWhere, In, IsNull, MoreThan} from 'typeorm';
import moment from 'moment';
import {Provably} from '@provair/core';
import {KvsService} from '@scmally/kvs';
import {RedLockService} from '@scmally/red-lock';
import {
  OrderCustomFields,
  PaymentInput,
  PaymentOrderType,
  TemplateType,
  WeChatPaymentType,
  PromotionType,
} from '../generated-shop-types';
import {SendMessageService} from './send-message.service';
import {ProductCustomService} from './product-custom.service';
import {InterfaceWishBoxActivity, WeChatPaymentService} from '@scmally/wechat';
import {CommonService} from './common.service';
import {OrderCustomCommonService} from './custom-order-common.service';

moment.updateLocale('en', {week: {dow: 1}});

@Injectable()
export class WishBoxService implements InterfaceWishBoxActivity {
  protected provably: Provably;

  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private customerService: CustomerService,
    private kvsService: KvsService,
    private redLockService: RedLockService,
    private sendMessageService: SendMessageService,
    private productCustomService: ProductCustomService,
    private weChatPaymentService: WeChatPaymentService,
    private commonService: CommonService,
    private orderCustomCommonService: OrderCustomCommonService,
    private paymentMethodService: PaymentMethodService,
    private eventBus: EventBus,
  ) {
    this.provably = Provably.create(this.kvsService.store);
    this.weChatPaymentService.registerWishBoxActivity(this);
  }

  async upsertWishBoxActivity(ctx: RequestContext, input: WishBoxActivityInput) {
    let activity: WishBoxActivity | null;
    if (input.id) {
      activity = await this.connection.getRepository(ctx, WishBoxActivity).findOneBy({
        id: input.id,
      });
      if (!activity) {
        throw new Error('心愿盒子活动不存在');
      }
    } else {
      const required = ['name', 'rules'];
      const missing = required.filter(item => !input[item as keyof WishBoxActivityInput]);
      if (missing.length > 0) {
        throw new Error(`心愿盒子活动缺少必填字段：${missing.join('、')}`);
      }
      activity = new WishBoxActivity({channelId: ctx.channelId});
    }
    activity = {
      ...activity,
      ...{
        name: input.name ?? activity.name,
        remarks: input.remarks ?? activity.remarks ?? '',
        startAt: input.startAt ? new Date(input.startAt) : activity.startAt ?? new Date(),
        endAt: input.endAt ? new Date(input.endAt) : activity.endAt ?? new Date(),
        period: input.period ?? activity.period ?? 0,
        periodUnit: input.periodUnit ?? activity.periodUnit ?? WishBoxActivityPeriodUnit.Day,
        periodPerLimit: input.periodPerLimit ?? activity.periodPerLimit ?? 0,
        freeBoxOpen: input.freeBoxOpen ?? activity.freeBoxOpen ?? false,
        freeBoxPeriod: input.freeBoxPeriod ?? activity.freeBoxPeriod ?? 0,
        freeBoxPeriodUnit: input.freeBoxPeriodUnit ?? activity.freeBoxPeriodUnit ?? WishBoxActivityPeriodUnit.Day,
        freeBoxPeriodPerLimit: input.freeBoxPeriodPerLimit ?? activity.freeBoxPeriodPerLimit ?? 0,
        freeBoxPerWinLimit: input.freeBoxPerWinLimit ?? activity.freeBoxPerWinLimit ?? 0,
        freeBoxPrizeValidityMode:
          input.freeBoxPrizeValidityMode ??
          activity.freeBoxPrizeValidityMode ??
          WishBoxActivityPrizeValidityMode.Relative,
        freeBoxPrizeValidityDay: input.freeBoxPrizeValidityDay ?? activity.freeBoxPrizeValidityDay ?? 0,
        freeBoxPrizeValidityStartAt: input.freeBoxPrizeValidityStartAt
          ? new Date(input.freeBoxPrizeValidityStartAt)
          : activity.freeBoxPrizeValidityStartAt,
        freeBoxPrizeValidityExpireAt: input.freeBoxPrizeValidityExpireAt
          ? new Date(input.freeBoxPrizeValidityExpireAt)
          : activity.freeBoxPrizeValidityExpireAt,
        rules: input.rules ?? activity.rules,
        status: input.status ?? activity.status ?? WishBoxActivityStatus.Draft,
        titleImage: input.titleImage ?? activity.titleImage ?? '',
        shareCover: input.shareCover ?? activity.shareCover ?? '',
      },
    };
    if (activity.startAt > activity.endAt) {
      throw new Error('心愿盒子活动开始时间不能大于结束时间');
    }
    activity = await this.connection.getRepository(ctx, WishBoxActivity).save(activity);

    // create strategies
    const strategies: WishBoxActivityOpenStrategy[] = [];
    for (const item of input.wishBoxActivityOpenStrategies ?? []) {
      let strategy: WishBoxActivityOpenStrategy | null;
      if (item.id) {
        strategy = await this.connection.getRepository(ctx, WishBoxActivityOpenStrategy).findOneBy({
          id: item.id,
          activityId: activity.id,
        });
        if (!strategy) {
          continue;
        }
      } else {
        strategy = new WishBoxActivityOpenStrategy({activityId: activity.id, channelId: ctx.channelId});
      }
      strategy = {
        ...strategy,
        ...{
          count: item.count ?? strategy.count ?? 0,
          price: item.price ?? strategy.price ?? 0,
          wishProbability: item.wishProbability ?? strategy.wishProbability ?? 0,
          baseProbability: item.baseProbability ?? strategy.baseProbability ?? 0,
          status: item.status ?? strategy.status ?? WishBoxActivityOpenStrategyStatus.Normal,
          sort: item.sort ?? strategy.sort ?? 0,
        },
      };
      if (Number((strategy.wishProbability + strategy.baseProbability).toFixed(2)) !== 100) {
        throw new Error('心愿盒子活动抽取概率总和必须为100%');
      }
      strategies.push(strategy);
    }
    if (strategies.length) {
      await this.connection.getRepository(ctx, WishBoxActivityOpenStrategy).save(strategies);
    }

    // create prizes
    const prizes: WishBoxActivityPrize[] = [];
    for (const item of input.wishBoxActivityPrizes ?? []) {
      let prize: WishBoxActivityPrize | null;
      if (item.id) {
        prize = await this.connection.getRepository(ctx, WishBoxActivityPrize).findOneBy({
          id: item.id,
          activityId: activity.id,
        });
        if (!prize) {
          continue;
        }
      } else {
        prize = new WishBoxActivityPrize({activityId: activity.id, channelId: ctx.channelId});
      }
      prize = {
        ...prize,
        ...{
          boxType: item.boxType ?? prize.boxType ?? WishBoxType.Normal,
          targetId: item.targetId ?? prize.targetId,
          probability: item.probability ?? prize.probability,
          status: item.status ?? prize.status ?? WishBoxActivityPrizeStatus.Normal,
        },
      };
      prizes.push(prize);
    }
    if (prizes.length) {
      await this.connection.getRepository(ctx, WishBoxActivityPrize).save(prizes);
    }

    return this.findOne(ctx, activity.id);
  }

  async findOne(
    ctx: RequestContext,
    id: ID,
    options?: ListQueryOptions<WishBoxActivity>,
    relations?: RelationPaths<WishBoxActivity>,
  ) {
    const qb = this.listQueryBuilder.build(WishBoxActivity, options, {
      ctx,
      relations: relations,
    });
    qb.leftJoinAndSelect(
      `${qb.alias}.wishBoxActivityOpenStrategies`,
      'wishBoxActivityOpenStrategies',
      'wishBoxActivityOpenStrategies.status = :strategyStatus',
      {strategyStatus: WishBoxActivityOpenStrategyStatus.Normal},
    );
    qb.leftJoinAndSelect(
      `${qb.alias}.wishBoxActivityPrizes`,
      'wishBoxActivityPrizes',
      'wishBoxActivityPrizes.status = :prizeStatus',
      {prizeStatus: WishBoxActivityPrizeStatus.Normal},
    );
    qb.leftJoinAndSelect(`wishBoxActivityPrizes.productVariant`, 'productVariant');
    qb.andWhere(`${qb.alias}.id =:id`, {id});
    qb.andWhere(`${qb.alias}.channelId = :channelId`, {channelId: ctx.channelId});
    return qb.take(1).getOne();
  }

  async findAll(
    ctx: RequestContext,
    options: ListQueryOptions<WishBoxActivity>,
    relations: RelationPaths<WishBoxActivity>,
  ) {
    const qb = this.listQueryBuilder.build(WishBoxActivity, options, {
      ctx,
      relations: relations,
    });
    qb.andWhere(`${qb.alias}.channelId = :channelId`, {channelId: ctx.channelId});
    const [items, totalItems] = await qb.getManyAndCount();
    return {items, totalItems};
  }

  async wishBoxActivityFreeQualification(ctx: RequestContext, wishBoxActivityId: ID) {
    const customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId ?? '');
    if (!customer) {
      return false;
    }
    const activity = await this.connection.getRepository(ctx, WishBoxActivity).findOneBy({
      id: wishBoxActivityId,
      channelId: ctx.channelId,
    });
    // 活动未开启、活动已结束、活动未开启免费盒子
    if (
      !activity ||
      activity.status !== WishBoxActivityStatus.Opened ||
      activity.startAt > new Date() ||
      activity.endAt < new Date() ||
      !activity.freeBoxOpen
    ) {
      return false;
    }

    // 免费盒子中奖次数已达到限制
    const recordCountWhere: FindOptionsWhere<WishBoxActivityRecord> = {
      customerId: customer.id,
      activityId: wishBoxActivityId,
      boxType: WishBoxType.Free,
    };
    const winRecordCount = await this.connection.getRepository(ctx, WishBoxActivityRecord).countBy({
      ...recordCountWhere,
      isEmpty: false,
    });
    if (activity.freeBoxPerWinLimit >= 0 && winRecordCount >= activity.freeBoxPerWinLimit) {
      return false;
    }

    // 有未使用的免费奖品
    const pendingDeliveryRecordCount = await this.connection.getRepository(ctx, WishBoxActivityRecord).count({
      where: [
        {
          ...recordCountWhere,
          expireAt: MoreThan(new Date()),
          status: WishBoxActivityRecordStatus.PendingDelivery,
        },
        {
          ...recordCountWhere,
          expireAt: IsNull(),
          status: WishBoxActivityRecordStatus.PendingDelivery,
        },
      ],
    });
    if (pendingDeliveryRecordCount > 0) {
      return false;
    }

    // 免费盒子周期内抽取次数已达到限制
    if (activity.freeBoxPeriodUnit !== WishBoxActivityPeriodUnit.Forever) {
      recordCountWhere.createdAt = MoreThan(
        moment()
          .startOf(activity.freeBoxPeriodUnit)
          .subtract(activity.freeBoxPeriod - 1, activity.freeBoxPeriodUnit)
          .toDate(),
      );
    }
    const recordCount = await this.connection.getRepository(ctx, WishBoxActivityRecord).countBy(recordCountWhere);
    if (recordCount >= activity.freeBoxPeriodPerLimit) {
      return false;
    }

    return true;
  }

  async drawPrize(
    wishBoxActivityPrizes: WishBoxActivityPrize[],
    uid: string,
    clientSeed: string,
    count: number,
  ): Promise<WishBoxActivityPrize[]> {
    // 空时均分剩余概率，不满100%会出空奖，超出100%按比例计算抽奖概率
    const prizes: WishBoxActivityPrize[] = [];
    let boxPrizes = wishBoxActivityPrizes.map(item => ({...item}));
    const sumProbability = boxPrizes.reduce((acc, item) => acc + (item.probability ?? 0), 0);
    if (sumProbability < 100) {
      let remainingProbability = 100 - sumProbability;
      const remainingPrizes = boxPrizes.filter(item => item.probability === null);
      const remainingProbabilityPerPrize = Number((remainingProbability / remainingPrizes.length).toFixed(2));
      // 精度问题导致不满100，补齐概率到最后一个
      for (let i = 0; i < remainingPrizes.length; i++) {
        if (i === remainingPrizes.length - 1) {
          remainingPrizes[i].probability = remainingProbability;
        } else {
          remainingPrizes[i].probability = remainingProbabilityPerPrize;
          remainingProbability -= remainingProbabilityPerPrize;
        }
      }
    } else if (sumProbability > 100) {
      boxPrizes = boxPrizes.filter(item => item.probability !== null);
      const scaleFactor = 100 / sumProbability;
      let remainingProbability = 100;
      for (let i = 0; i < boxPrizes.length; i++) {
        if (i === boxPrizes.length - 1) {
          boxPrizes[i].probability = Number(remainingProbability.toFixed(2));
        } else {
          boxPrizes[i].probability = Number((boxPrizes[i].probability! * scaleFactor).toFixed(2));
          remainingProbability -= boxPrizes[i].probability!;
        }
      }
    }
    // 抽奖
    const serverSeedResult = await this.provably.createServerSeed({uid});
    const calculates = await this.provably.calculate({
      uid,
      serverSeedHash: serverSeedResult.serverSeedHash,
      clientSeed,
      count,
    });
    let start = 0;
    for (const item of boxPrizes) {
      const end = start + item.probability!;
      for (const calculate of calculates) {
        if (start < calculate.result && calculate.result <= end) {
          prizes.push(item);
        }
      }
      start = end;
    }
    return prizes;
  }

  async wishBoxActivityFreeOpen(ctx: RequestContext, wishBoxActivityId: ID) {
    const customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId ?? '');
    if (!customer) {
      throw new UnauthorizedError();
    }

    return this.wishBoxActivityOpen(ctx, customer, wishBoxActivityId, WishBoxType.Free);
  }

  async wishBoxActivityNormalOpen(
    ctx: RequestContext,
    customer: Customer,
    wishBoxActivityId: ID,
    strategyId: ID,
    activityBuyId: ID,
  ) {
    return this.wishBoxActivityOpen(ctx, customer, wishBoxActivityId, WishBoxType.Normal, strategyId, activityBuyId);
  }

  @Transaction()
  async wishBoxActivityOpen(
    ctx: RequestContext,
    customer: Customer,
    wishBoxActivityId: ID,
    type: WishBoxType,
    strategyId?: ID,
    activityBuyId?: ID,
  ) {
    ctx = (await this.commonService.getCtxByCustomerAndChannels([ctx.channel], customer)) ?? ctx;
    const lock = await this.redLockService.lockResource(`wishBoxActivityOpen:${customer.id}:${wishBoxActivityId}`);
    try {
      const activity = await this.findOne(ctx, wishBoxActivityId);
      if (!activity) {
        throw new EntityNotFoundError('WishBoxActivity', wishBoxActivityId);
      }

      const prizes: WishBoxActivityPrize[] = [];
      let startAt: Date | undefined;
      let expireAt: Date | undefined;
      const clientSeed = Math.random().toString(36).slice(-8);

      if (type === WishBoxType.Free) {
        // 开启免费盒子
        if (!(await this.wishBoxActivityFreeQualification(ctx, wishBoxActivityId))) {
          throw new Error('free box qualification not satisfied');
        }
        const freeBoxPrizes = activity.wishBoxActivityPrizes.filter(
          item => item.boxType === WishBoxType.Free && item.status === WishBoxActivityPrizeStatus.Normal,
        );
        if (freeBoxPrizes.length > 0) {
          if (activity.freeBoxPrizeValidityMode === WishBoxActivityPrizeValidityMode.Relative) {
            startAt = new Date();
            expireAt = moment(startAt).add(activity.freeBoxPrizeValidityDay, 'days').toDate();
          } else {
            startAt = activity.freeBoxPrizeValidityStartAt;
            expireAt = activity.freeBoxPrizeValidityExpireAt;
          }
          prizes.push(...(await this.drawPrize(freeBoxPrizes, customer.id.toString(), clientSeed, 1)));
        }
      } else {
        // 开启付费盒子
        let wishPrizes = await this.wishBoxActivityWishProductFind(ctx, wishBoxActivityId);
        const wishPrizesIds = wishPrizes.map(item => item.id);
        const normalPrizes = activity.wishBoxActivityPrizes.filter(
          item =>
            item.boxType === WishBoxType.Normal &&
            item.status === WishBoxActivityPrizeStatus.Normal &&
            !wishPrizesIds.includes(item.id),
        );
        const openStrategy = activity.wishBoxActivityOpenStrategies.find(item => item.id === strategyId);
        if (wishPrizes.length <= 0) {
          prizes.push(
            ...(await this.drawPrize(normalPrizes, customer.id.toString(), clientSeed, openStrategy?.count ?? 1)),
          );
        } else {
          const serverSeedResult = await this.provably.createServerSeed({uid: customer.id.toString()});
          const calculates = await this.provably.calculate({
            uid: customer.id.toString(),
            serverSeedHash: serverSeedResult.serverSeedHash,
            clientSeed,
            count: openStrategy?.count ?? 1,
          });
          let wishCount = 0;
          let baseCount = 0;
          for (const calculate of calculates) {
            if (calculate.result <= (openStrategy?.wishProbability ?? 0)) {
              wishCount++;
            } else {
              baseCount++;
            }
          }
          // 心愿奖励
          if (wishCount > wishPrizes.length) {
            baseCount += wishCount - wishPrizes.length;
            wishCount = wishPrizes.length;
          }
          if (wishCount === wishPrizes.length) {
            prizes.push(...wishPrizes);
          } else {
            for (let i = 0; i < wishCount; i++) {
              const wishWinPrizes = await this.drawPrize(wishPrizes, customer.id.toString(), clientSeed, 1);
              prizes.push(...wishWinPrizes);
              wishPrizes = wishPrizes.filter(wishPrize => !wishWinPrizes.map(item => item.id).includes(wishPrize.id));
            }
          }
          // 基础奖励
          if (baseCount > 0) {
            prizes.push(...(await this.drawPrize(normalPrizes, customer.id.toString(), clientSeed, baseCount)));
          }
        }
      }

      const record = await this.connection.getRepository(ctx, WishBoxActivityRecord).save(
        new WishBoxActivityRecord({
          customer: {id: customer.id},
          activity: {id: activity.id},
          activityBuy: activityBuyId ? {id: activityBuyId} : null,
          boxType: type,
          isEmpty: prizes.length === 0,
          code: `B${moment().format('YYYYMMDDHHmmss')}${customer.id}${Math.floor(Math.random() * 100000000)}`.slice(
            0,
            24,
          ),
          startAt: startAt ?? new Date(),
          expireAt: expireAt ?? moment().add(100, 'years').toDate(),
          status:
            prizes.length !== 0 ? WishBoxActivityRecordStatus.PendingDelivery : WishBoxActivityRecordStatus.Delivered,
          channelId: ctx.channelId,
        }),
      );
      if (prizes.length > 0) {
        await this.connection.getRepository(ctx, WishBoxActivityRecordItem).save(
          prizes.map(
            item =>
              new WishBoxActivityRecordItem({
                recordId: record.id,
                activityPrizeId: item.id,
                type: item.type,
                targetId: item.targetId,
                channelId: ctx.channelId,
              }),
          ),
        );
      }
      return await this.wishBoxActivityRecord(ctx, record.id);
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  async wishBoxActivityRecord(
    ctx: RequestContext,
    id: ID,
    options?: ListQueryOptions<WishBoxActivityRecord>,
    relations?: RelationPaths<WishBoxActivityRecord>,
  ) {
    const qb = this.listQueryBuilder.build(WishBoxActivityRecord, options, {
      ctx,
      relations: relations,
    });
    qb.leftJoinAndSelect(`${qb.alias}.wishBoxActivityRecordItems`, 'wishBoxActivityRecordItems');
    qb.leftJoinAndSelect(`wishBoxActivityRecordItems.productVariant`, 'productVariant');
    qb.andWhere(`${qb.alias}.id = :id`, {id});
    qb.andWhere(`${qb.alias}.channelId = :channelId`, {channelId: ctx.channelId});
    return qb.take(1).getOne();
  }

  async wishBoxActivityRecords(
    ctx: RequestContext,
    options: ListQueryOptions<WishBoxActivityRecord>,
    relations: RelationPaths<WishBoxActivityRecord>,
    isCustomer?: boolean,
  ) {
    const qb = this.listQueryBuilder.build(WishBoxActivityRecord, options, {
      ctx,
      relations: relations,
    });
    if (isCustomer) {
      const customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId ?? '');
      if (!customer) {
        throw new UnauthorizedError();
      }
      qb.andWhere(`${qb.alias}.customerId = :customerId`, {customerId: customer.id});
    }
    qb.andWhere(`${qb.alias}.channelId = :channelId`, {channelId: ctx.channelId});
    const [items, totalItems] = await qb.getManyAndCount();
    return {items, totalItems};
  }

  @Transaction()
  async wishBoxActivityWishProductPick(ctx: RequestContext, wishBoxActivityId: ID, wishBoxActivityPrizeIdList: ID[]) {
    const customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId ?? '');
    if (!customer) {
      throw new UnauthorizedError();
    }
    const prizes = await this.connection.getRepository(ctx, WishBoxActivityPrize).find({
      where: {
        id: In(wishBoxActivityPrizeIdList),
        activityId: wishBoxActivityId,
        boxType: WishBoxType.Normal,
        status: WishBoxActivityPrizeStatus.Normal,
      },
    });
    await this.connection.getRepository(ctx, WishBoxActivityPick).delete({
      activityId: wishBoxActivityId,
      customerId: customer.id,
    });
    await this.connection.getRepository(ctx, WishBoxActivityPick).save(
      prizes.map(
        item =>
          new WishBoxActivityPick({
            activityId: wishBoxActivityId,
            customerId: customer.id,
            activityPrizeId: item.id,
            channelId: ctx.channelId,
          }),
      ),
    );
    return this.wishBoxActivityWishProductFind(ctx, wishBoxActivityId);
  }

  async wishBoxActivityWishProductFind(
    ctx: RequestContext,
    wishBoxActivityId: ID,
    options?: ListQueryOptions<WishBoxActivityPrize>,
    relations?: RelationPaths<WishBoxActivityPrize>,
  ) {
    const customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId ?? '');
    if (!customer) {
      throw new UnauthorizedError();
    }
    const picks = await this.connection.getRepository(ctx, WishBoxActivityPick).find({
      where: {
        activityId: wishBoxActivityId,
        customerId: customer.id,
      },
    });
    if (picks.length <= 0) {
      return [];
    }
    const qb = this.listQueryBuilder.build(WishBoxActivityPrize, options, {
      ctx,
      relations: relations,
    });
    qb.leftJoinAndSelect(`${qb.alias}.productVariant`, 'productVariant');
    qb.andWhere(`${qb.alias}.id IN (:...ids)`, {ids: picks.map(item => item.activityPrizeId)});
    qb.andWhere(`${qb.alias}.activityId = :activityId`, {activityId: wishBoxActivityId});
    qb.andWhere(`${qb.alias}.status = :status`, {status: WishBoxActivityPrizeStatus.Normal});
    return qb.getMany();
  }

  async wishBoxActivityBooking(ctx: RequestContext, wishBoxActivityId: ID, isCheck = false) {
    const customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId ?? '');
    if (!customer) {
      throw new UnauthorizedError();
    }
    const lock = await this.redLockService.lockResource(`wishBoxActivityBooking:${customer.id}:${wishBoxActivityId}`);
    try {
      const isBooking = await this.connection.getRepository(ctx, SubscriptionMessageRecord).findOneBy({
        targetType: 'WishBoxActivity',
        targetId: wishBoxActivityId.toString(),
        customer: {id: customer.id},
        sended: false,
      });
      if (isCheck) {
        return !!isBooking;
      }
      if (isBooking) {
        throw new Error('您已经预约过了');
      }
      const wishBoxActivity = await this.findOne(ctx, wishBoxActivityId);
      if (!wishBoxActivity || wishBoxActivity?.startAt < new Date()) {
        throw new Error('活动不在预约时间内');
      }

      const booking = new SubscriptionMessageRecord({
        targetType: 'WishBoxActivity',
        targetId: wishBoxActivityId.toString(),
        customer: {id: customer.id},
        sended: false,
        channelId: ctx.channelId,
      });
      await this.connection.getRepository(ctx, SubscriptionMessageRecord).save(booking);
      return true;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  async wishBoxActivityRemindAll() {
    const ctxs = await this.productCustomService.getAllCtxs();
    for (const ctx of ctxs) {
      await this.wishBoxActivityRemind(ctx);
    }
  }

  async wishBoxActivityRemind(ctx: RequestContext) {
    const templateConfig = await this.connection.getRepository(ctx, TemplateConfig).findOne({
      where: {
        channelId: ctx.channelId,
        templateType: TemplateType.BlindBoxActivityBooking, // 复用盲盒活动模板
      },
    });
    if (!templateConfig) {
      return;
    }

    const laterTime = moment().add(10, 'minutes').toDate();
    const wishBoxActivities = await this.connection
      .getRepository(ctx, WishBoxActivity)
      .createQueryBuilder('wishBoxActivity')
      .andWhere(`wishBoxActivity.channelId = :channelId`, {channelId: ctx.channelId})
      .andWhere(`wishBoxActivity.startAt between :startAt and :laterTime`, {
        startAt: moment().toDate(),
        laterTime,
      })
      .andWhere(`wishBoxActivity.status = :status`, {status: WishBoxActivityStatus.Opened})
      .getMany();
    if (wishBoxActivities.length === 0) {
      return;
    }
    for (const wishBoxActivity of wishBoxActivities) {
      let hasMore = true;
      const pageSize = 100; // 每页记录数
      while (hasMore) {
        const bookings = await this.connection
          .getRepository(ctx, SubscriptionMessageRecord)
          .createQueryBuilder('subscriptionMessageRecord')
          .leftJoinAndSelect('subscriptionMessageRecord.customer', 'customer')
          .andWhere('subscriptionMessageRecord.targetType = :targetType', {targetType: 'WishBoxActivity'})
          .andWhere('subscriptionMessageRecord.targetId = :targetId', {targetId: wishBoxActivity.id.toString()})
          .andWhere('subscriptionMessageRecord.sended = false')
          .take(pageSize)
          .getMany();
        hasMore = bookings.length === pageSize;
        if (bookings.length === 0) continue;

        for (const booking of bookings) {
          try {
            // 发送提醒消息
            const result = await this.sendMessageService.wishBoxActivityRemind(
              ctx,
              booking.customer.id,
              wishBoxActivity.name,
              wishBoxActivity.startAt,
              templateConfig,
              booking.targetId,
            );
            if (result?.data?.errcode === 0) {
              booking.sended = true;
            }
          } catch (error) {
            Logger.error(
              `Failed to send reminder for bookingId ${booking.id} in wish box activity ${wishBoxActivity.id}`,
              error,
            );
            continue;
          }
        }
        await this.connection.getRepository(ctx, SubscriptionMessageRecord).save(bookings);
      }
    }
  }

  async wishBoxActivityBuyCheck(ctx: RequestContext, wishBoxActivityId: ID) {
    const customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId ?? '');
    if (!customer) {
      throw new UnauthorizedError();
    }
    const wishBoxActivity = await this.findOne(ctx, wishBoxActivityId);
    if (
      !wishBoxActivity ||
      wishBoxActivity.status !== WishBoxActivityStatus.Opened ||
      wishBoxActivity.startAt > new Date() ||
      wishBoxActivity.endAt < new Date()
    ) {
      return 0;
    }

    const buyCountWhere: FindOptionsWhere<WishBoxActivityBuy> = {
      customerId: customer.id,
      activityId: wishBoxActivityId,
      payStatus: WishBoxActivityBuyPayStatus.Paid,
    };
    if (wishBoxActivity.periodUnit !== WishBoxActivityPeriodUnit.Forever) {
      buyCountWhere.createdAt = MoreThan(
        moment()
          .startOf(wishBoxActivity.periodUnit)
          .subtract(wishBoxActivity.period - 1, wishBoxActivity.periodUnit)
          .toDate(),
      );
    }
    const buyCount = await this.connection.getRepository(ctx, WishBoxActivityBuy).countBy(buyCountWhere);
    const remainCount = wishBoxActivity.periodPerLimit - buyCount;
    return remainCount < 0 ? 0 : remainCount;
  }

  async wishBoxActivityBuy(
    ctx: RequestContext,
    wishBoxActivityId: ID,
    activityOpenStrategyId: ID,
    paymentType: WeChatPaymentType,
  ) {
    const customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId ?? '');
    if (!customer) {
      throw new UnauthorizedError();
    }
    const wishBoxActivity = await this.findOne(ctx, wishBoxActivityId);
    if (!wishBoxActivity || wishBoxActivity.status !== WishBoxActivityStatus.Opened) {
      throw new Error('心愿盒子活动已失效');
    }
    if (wishBoxActivity.startAt > new Date()) {
      throw new Error('心愿盒子活动未开始');
    }
    if (wishBoxActivity.endAt < new Date()) {
      throw new Error('心愿盒子活动已结束');
    }
    const activityOpenStrategy = wishBoxActivity.wishBoxActivityOpenStrategies.find(
      item => item.id === activityOpenStrategyId && item.status === WishBoxActivityOpenStrategyStatus.Normal,
    );
    if (!activityOpenStrategy) {
      throw new Error('开启策略不存在');
    }
    const lock = await this.redLockService.lockResource(`wishBoxActivityBuy:${customer.id}:${wishBoxActivityId}`);
    try {
      // 盒子购买次数已达到限制
      const remainCount = await this.wishBoxActivityBuyCheck(ctx, wishBoxActivityId);
      if (remainCount <= 0) {
        throw new Error('心愿盒子活动参与次数已达上限');
      }

      const wishBoxActivityBuy = await this.connection.getRepository(ctx, WishBoxActivityBuy).save(
        new WishBoxActivityBuy({
          activityId: wishBoxActivityId,
          customerId: customer.id,
          activityOpenStrategyId,
          price: activityOpenStrategy.price,
          code: this.weChatPaymentService.generateDateString('B'),
          channelId: ctx.channelId,
        }),
      );

      return await this.weChatPaymentService.createAPurchasePaymentOrder(
        ctx,
        paymentType,
        wishBoxActivityBuy.id,
        PaymentOrderType.WishBoxActivityBuyOrder,
        wishBoxActivityBuy.code,
      );
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  async getWishBoxActivityBuyByCode(ctx: RequestContext, code: string) {
    const wishBoxActivityBuy = await this.connection.getRepository(ctx, WishBoxActivityBuy).findOneBy({code});
    if (!wishBoxActivityBuy) {
      throw new Error('心愿盒子活动购买记录不存在');
    }
    return {
      id: wishBoxActivityBuy.id,
      code: wishBoxActivityBuy.code,
      price: wishBoxActivityBuy.price,
      activityId: wishBoxActivityBuy.activityId,
      activityOpenStrategyId: wishBoxActivityBuy.activityOpenStrategyId,
      customer: (await this.customerService.findOne(ctx, wishBoxActivityBuy.customerId, ['user'])) as Customer,
    };
  }

  async getWishBoxActivityBuyByOrderId(ctx: RequestContext, orderId: ID) {
    const wishBoxActivityBuy = await this.connection.getRepository(ctx, WishBoxActivityBuy).findOneBy({id: orderId});
    if (!wishBoxActivityBuy) {
      throw new Error('心愿盒子活动购买记录不存在');
    }
    return {
      id: wishBoxActivityBuy.id,
      code: wishBoxActivityBuy.code,
      price: wishBoxActivityBuy.price,
      activityId: wishBoxActivityBuy.activityId,
      activityOpenStrategyId: wishBoxActivityBuy.activityOpenStrategyId,
      customer: (await this.customerService.findOne(ctx, wishBoxActivityBuy.customerId, ['user'])) as Customer,
    };
  }

  async getWishBoxActivityBuy(
    ctx: RequestContext,
    options: ListQueryOptions<WishBoxActivityBuy>,
    relations: RelationPaths<WishBoxActivityBuy>,
    wishBoxActivityBuyId?: ID,
    code?: string,
  ) {
    const customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId ?? '');
    if (!customer) {
      throw new UnauthorizedError();
    }
    const qb = this.listQueryBuilder.build(WishBoxActivityBuy, options, {ctx, relations});
    qb.leftJoinAndSelect(`${qb.alias}.activity`, 'activity');
    qb.leftJoinAndSelect(`${qb.alias}.activityOpenStrategy`, 'activityOpenStrategy');
    qb.leftJoinAndSelect(`${qb.alias}.customer`, 'customer');
    qb.leftJoinAndSelect(`${qb.alias}.wishBoxActivityRecord`, 'wishBoxActivityRecord');
    qb.leftJoinAndSelect(`wishBoxActivityRecord.wishBoxActivityRecordItems`, 'wishBoxActivityRecordItems');
    qb.leftJoinAndSelect(`wishBoxActivityRecordItems.productVariant`, 'productVariant');
    qb.andWhere(`${qb.alias}.customerId = :customerId`, {customerId: customer.id});
    qb.andWhere(`${qb.alias}.channelId = :channelId`, {channelId: ctx.channelId});
    if (wishBoxActivityBuyId) {
      qb.andWhere(`${qb.alias}.id = :id`, {id: wishBoxActivityBuyId});
    }
    if (code) {
      qb.andWhere(`${qb.alias}.code = :code`, {code});
    }
    return qb.getOne();
  }

  async getWishBoxActivityBuys(
    ctx: RequestContext,
    options: ListQueryOptions<WishBoxActivityBuy>,
    relations: RelationPaths<WishBoxActivityBuy>,
    isCustomer?: boolean,
    recordCode?: string,
  ) {
    const qb = this.listQueryBuilder.build(WishBoxActivityBuy, options, {ctx, relations});
    if (isCustomer) {
      const customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId ?? '');
      if (!customer) {
        throw new UnauthorizedError();
      }
      qb.andWhere(`${qb.alias}.customerId = :customerId`, {customerId: customer.id});
    }
    qb.leftJoinAndSelect(`${qb.alias}.wishBoxActivityRecord`, 'wishBoxActivityRecord');
    qb.leftJoinAndSelect(`wishBoxActivityRecord.order`, 'order');
    qb.leftJoinAndMapOne(
      'order.distributorOrder',
      DistributorOrder,
      'distributorOrder',
      'distributorOrder.orderId = order.id',
    )
      .leftJoinAndSelect('distributorOrder.distributor', 'distributor')
      .leftJoinAndSelect('distributorOrder.distributorGroup', 'distributorGroup');
    if (recordCode) {
      qb.andWhere(`wishBoxActivityRecord.code = :recordCode`, {recordCode});
    }
    qb.andWhere(`${qb.alias}.channelId = :channelId`, {channelId: ctx.channelId});
    const [items, totalItems] = await qb.getManyAndCount();
    // Backfill distributorOrder in case leftJoinAndMapOne is not hydrated in getManyAndCount
    const orderIds = Array.from(
      new Set(items.map(i => i.wishBoxActivityRecord?.order?.id).filter((id): id is ID => Boolean(id)) as ID[]),
    );
    if (orderIds.length > 0) {
      const distributorOrders = await this.connection.getRepository(ctx, DistributorOrder).find({
        where: {order: {id: In(orderIds)}},
        relations: ['order', 'distributor', 'distributorGroup'],
      });
      const orderIdToDistributorOrder = new Map<ID, DistributorOrder>();
      for (const d of distributorOrders) {
        const oid = d.order?.id;
        if (oid) {
          orderIdToDistributorOrder.set(oid, d);
        }
      }
      type MutableOrder = {id?: ID; distributorOrder?: DistributorOrder};
      for (const item of items) {
        const mOrder = item.wishBoxActivityRecord?.order as unknown as MutableOrder | undefined;
        if (mOrder?.id && !mOrder.distributorOrder) {
          const dist = orderIdToDistributorOrder.get(mOrder.id);
          if (dist) {
            mOrder.distributorOrder = dist;
          }
        }
      }
    }
    return {items, totalItems};
  }

  async wishBoxActivityBuyById(
    ctx: RequestContext,
    options: ListQueryOptions<WishBoxActivityBuy>,
    relations: RelationPaths<WishBoxActivityBuy>,
    wishBoxActivityBuyId: ID,
  ) {
    const qb = this.listQueryBuilder.build(WishBoxActivityBuy, options, {ctx, relations});
    qb.leftJoinAndSelect(`${qb.alias}.activity`, 'activity');
    qb.leftJoinAndSelect(`${qb.alias}.activityOpenStrategy`, 'activityOpenStrategy');
    qb.leftJoinAndSelect(`${qb.alias}.customer`, 'customer');
    qb.leftJoinAndSelect(`${qb.alias}.wishBoxActivityRecord`, 'wishBoxActivityRecord');
    qb.leftJoinAndSelect(`wishBoxActivityRecord.wishBoxActivityRecordItems`, 'wishBoxActivityRecordItems');
    qb.leftJoinAndSelect(`wishBoxActivityRecordItems.productVariant`, 'productVariant');
    qb.leftJoinAndSelect(`wishBoxActivityRecord.order`, 'order');
    qb.leftJoinAndMapOne(
      'order.distributorOrder',
      DistributorOrder,
      'distributorOrder',
      'distributorOrder.orderId = order.id',
    )
      .leftJoinAndSelect('distributorOrder.distributor', 'distributor')
      .leftJoinAndSelect('distributorOrder.distributorGroup', 'distributorGroup');
    qb.andWhere(`${qb.alias}.channelId = :channelId`, {channelId: ctx.channelId});
    qb.andWhere(`${qb.alias}.id = :id`, {id: wishBoxActivityBuyId});
    return qb.getOne();
  }

  /**
   * 支付成功后，开启心愿盒子
   * @param ctx 请求上下文
   * @param wishBoxActivityBuyId 心愿盒子活动购买记录ID
   * @param payment 支付信息
   * @returns 是否成功
   */
  async addPaymentToActivityBuys(
    ctx: RequestContext,
    wishBoxActivityBuyId: ID,
    payment: PaymentInput,
  ): Promise<boolean> {
    let wishBoxActivityBuy = await this.connection.getRepository(ctx, WishBoxActivityBuy).findOne({
      where: {
        id: wishBoxActivityBuyId,
        payStatus: WishBoxActivityBuyPayStatus.PendingPay,
      },
      relations: ['customer'],
    });
    if (!wishBoxActivityBuy) {
      throw new Error('心愿盒子活动购买记录不存在');
    }
    wishBoxActivityBuy.paymentMethod = payment.method;
    wishBoxActivityBuy.payStatus = WishBoxActivityBuyPayStatus.Paid;
    wishBoxActivityBuy.paymentAt = new Date();
    wishBoxActivityBuy.paymentMetadata = payment.metadata;
    wishBoxActivityBuy = await this.connection.getRepository(ctx, WishBoxActivityBuy).save(wishBoxActivityBuy);

    await this.wishBoxActivityNormalOpen(
      ctx,
      wishBoxActivityBuy.customer,
      wishBoxActivityBuy.activityId,
      wishBoxActivityBuy.activityOpenStrategyId,
      wishBoxActivityBuy.id,
    );
    wishBoxActivityBuy.status = WishBoxActivityBuyStatus.Opened;
    await this.connection.getRepository(ctx, WishBoxActivityBuy).save(wishBoxActivityBuy);
    return true;
  }

  @Transaction()
  async wishBoxActivityRecordPickUp(ctx: RequestContext, wishBoxActivityRecordIds: ID[]) {
    if (wishBoxActivityRecordIds.length === 0) {
      return [];
    }
    const customer = await this.customerService.findOneByUserId(ctx, ctx.activeUserId ?? '');
    if (!customer) {
      throw new UnauthorizedError();
    }
    await this.connection
      .getRepository(ctx, WishBoxActivityRecord)
      .update({customerId: customer.id, inShoppingCart: true, channelId: ctx.channelId}, {inShoppingCart: false});

    const wishBoxActivityRecords = await this.connection.getRepository(ctx, WishBoxActivityRecord).find({
      where: {
        id: In(wishBoxActivityRecordIds),
        customerId: customer.id,
        channelId: ctx.channelId,
        status: WishBoxActivityRecordStatus.PendingDelivery,
      },
      relations: ['wishBoxActivityRecordItems', 'wishBoxActivityRecordItems.productVariant'],
    });
    if (wishBoxActivityRecords.length === 0) {
      return [];
    }
    if (!wishBoxActivityRecords.find(item => item.boxType === WishBoxType.Normal)) {
      throw new Error('仅随单商品不可单独提货');
    }
    for (const wishBoxActivityRecord of wishBoxActivityRecords) {
      if (wishBoxActivityRecord.startAt && wishBoxActivityRecord.startAt > new Date()) {
        throw new Error('商品未到提货时间');
      }
      if (wishBoxActivityRecord.expireAt && wishBoxActivityRecord.expireAt < new Date()) {
        throw new Error('商品已过提货时间');
      }
      wishBoxActivityRecord.inShoppingCart = true;
    }
    await this.connection.getRepository(ctx, WishBoxActivityRecord).save(wishBoxActivityRecords);
    return wishBoxActivityRecords;
  }

  async getWishBoxActivityShoppingCartRecords(ctx: RequestContext, customerId: ID): Promise<WishBoxActivityRecord[]> {
    const wishBoxActivityRecords = await this.connection.getRepository(ctx, WishBoxActivityRecord).find({
      where: {
        customerId,
        channelId: ctx.channelId,
        inShoppingCart: true,
        status: WishBoxActivityRecordStatus.PendingDelivery,
      },
      relations: ['wishBoxActivityRecordItems', 'wishBoxActivityRecordItems.productVariant'],
    });
    return wishBoxActivityRecords;
  }

  /**
   * 核销购物车商品
   * @param ctx 请求上下文
   * @param order 订单
   * @returns 是否成功
   */
  async verifyWishBoxActivityShoppingCartRecords(ctx: RequestContext, order: Order) {
    const lock = await this.redLockService.lockResource(`verifyWishBoxActivityShoppingCartRecords:${order.id}`);
    try {
      const promLineResults = (order.customFields as OrderCustomFields).orderPromotionResult?.promResult
        ?.promLineResults;
      if (!promLineResults) {
        return true;
      }
      const wishBoxActivityPromResult = promLineResults.find(prom => prom?.type === PromotionType.WishBoxActivity);
      if (!wishBoxActivityPromResult?.wishBoxActivityRecordIds) {
        return true;
      }
      const wishBoxActivityRecordIds = wishBoxActivityPromResult.wishBoxActivityRecordIds;
      if (wishBoxActivityRecordIds.length === 0) {
        return true;
      }
      const wishBoxActivityRecords = await this.connection.getRepository(ctx, WishBoxActivityRecord).find({
        where: {
          id: In(wishBoxActivityRecordIds),
          channelId: ctx.channelId,
          status: WishBoxActivityRecordStatus.PendingDelivery,
        },
      });
      wishBoxActivityRecords.forEach(wishBoxActivityRecord => {
        if (wishBoxActivityRecord.expireAt && wishBoxActivityRecord.expireAt < new Date()) {
          throw new Error('商品已过提货时间');
        }
        wishBoxActivityRecord.inShoppingCart = false;
        wishBoxActivityRecord.status = WishBoxActivityRecordStatus.Delivered;
        wishBoxActivityRecord.orderId = order.id;
      });
      await this.connection.getRepository(ctx, WishBoxActivityRecord).save(wishBoxActivityRecords);

      // 将心愿盒子提货关联的订单行设置为不可售后
      const orderLineIds: ID[] = order.lines.map(l => l.id);
      if (orderLineIds.length > 0) {
        await this.orderCustomCommonService.closeOrderLineAfterSale(ctx, order, orderLineIds);
      }
      return true;
    } catch (error) {
      Logger.error(`verifyWishBoxActivityShoppingCartRecords error: ${error}`);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  async cancelWishBoxActivityOrder(ctx: RequestContext, order: Order) {
    const lock = await this.redLockService.lockResource(`cancelOrder:${order.id}`);
    try {
      const wishBoxActivityRecords = await this.connection.getRepository(ctx, WishBoxActivityRecord).find({
        where: {orderId: order.id},
      });
      wishBoxActivityRecords.forEach(wishBoxActivityRecord => {
        wishBoxActivityRecord.status = WishBoxActivityRecordStatus.PendingDelivery;
      });
      await this.connection.getRepository(ctx, WishBoxActivityRecord).save(wishBoxActivityRecords);
    } catch (error) {
      Logger.error(`cancelWishBoxActivityOrder error: ${error}`);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  async refundWishBoxActivityBuy(ctx: RequestContext, wishBoxActivityBuyId: ID, refundReason: string) {
    const lock = await this.redLockService.lockResource(`refundWishBoxActivityBuy:${wishBoxActivityBuyId}`);
    try {
      const wishBoxActivityBuy = await this.connection
        .getRepository(ctx, WishBoxActivityBuy)
        .findOneBy({id: wishBoxActivityBuyId});

      if (!wishBoxActivityBuy) {
        throw new Error('心愿盒子活动购买记录不存在');
      }
      if (wishBoxActivityBuy.payStatus !== WishBoxActivityBuyPayStatus.Paid || !wishBoxActivityBuy.paymentMethod) {
        throw new Error('心愿盒子活动购买记录状态不支持退款');
      }
      const methodAndHandler = await this.paymentMethodService.getMethodAndOperations(
        ctx,
        wishBoxActivityBuy.paymentMethod,
      );
      if (!methodAndHandler?.paymentMethod || !methodAndHandler?.handler) {
        throw new Error('支付方式不存在');
      }
      await methodAndHandler.handler.createRefund(
        ctx,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        undefined as any,
        wishBoxActivityBuy.price,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        wishBoxActivityBuy as any,
        {
          method: wishBoxActivityBuy.paymentMethod,
          metadata: wishBoxActivityBuy.paymentMetadata,
          amount: wishBoxActivityBuy.price,
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } as any,
        methodAndHandler.paymentMethod.handler.args,
        methodAndHandler.paymentMethod,
      );

      const wishBoxActivityRecord = await this.connection.getRepository(ctx, WishBoxActivityRecord).findOneBy({
        activityBuyId: wishBoxActivityBuy.id,
      });
      if (wishBoxActivityRecord) {
        wishBoxActivityRecord.status = WishBoxActivityRecordStatus.Refunded;
        await this.connection.getRepository(ctx, WishBoxActivityRecord).save(wishBoxActivityRecord);
        if (wishBoxActivityRecord.orderId) {
          const order = await this.connection.getRepository(ctx, Order).findOne({
            where: {id: wishBoxActivityRecord.orderId},
            relations: ['lines'],
          });
          if (order) {
            order.state = 'Cancelled';
            await this.connection.getRepository(ctx, Order).save(order);
            // 同步状态到聚水潭
            this.eventBus.publish(new OrderEvent(ctx, order, 'updated'));
          }
          await this.connection
            .getRepository(ctx, WishBoxActivityRecord)
            .update(
              {orderId: wishBoxActivityRecord.orderId, boxType: WishBoxType.Free},
              {status: WishBoxActivityRecordStatus.Refunded},
            );
        }
      }

      wishBoxActivityBuy.payStatus = WishBoxActivityBuyPayStatus.Refunded;
      wishBoxActivityBuy.refundReason = refundReason;
      await this.connection.getRepository(ctx, WishBoxActivityBuy).save(wishBoxActivityBuy);
      return wishBoxActivityBuy;
    } catch (error) {
      Logger.error(`refundWishBoxActivityBuy error: ${error}`);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }
}
