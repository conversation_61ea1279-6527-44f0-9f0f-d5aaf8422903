import {DeepPartial, ID, Customer, VendureEntity, PaymentMetadata} from '@vendure/core';
import {Column, Entity, Index, JoinColumn, ManyToOne, OneToOne} from 'typeorm';
import {WishBoxActivity} from './wish-box-activity.entity';
import {WishBoxActivityOpenStrategy} from './wish-box-activity-open-strategy.entity';
import {WishBoxActivityBuyStatus, WishBoxActivityBuyPayStatus} from '../../generated-admin-types';
import {WishBoxActivityRecord} from './wish-box-activity-record.entity';

@Entity()
export class WishBoxActivityBuy extends VendureEntity {
  constructor(input?: DeepPartial<WishBoxActivityBuy>) {
    super(input);
  }

  @Index()
  @Column({type: 'int', comment: '心愿盒子活动id'})
  activityId: ID;

  @ManyToOne(() => WishBoxActivity)
  @JoinColumn({name: 'activityId'})
  activity: WishBoxActivity;

  @Index()
  @Column({type: 'int', comment: '用户id'})
  customerId: ID;

  @ManyToOne(() => Customer)
  @JoinColumn({name: 'customerId'})
  customer: Customer;

  @Index()
  @Column({type: 'int', comment: '开启策略id'})
  activityOpenStrategyId: ID;

  @ManyToOne(() => WishBoxActivityOpenStrategy)
  @JoinColumn({name: 'activityOpenStrategyId'})
  activityOpenStrategy: WishBoxActivityOpenStrategy;

  @OneToOne(() => WishBoxActivityRecord, item => item.activityBuy)
  wishBoxActivityRecord: WishBoxActivityRecord;

  @Column({type: 'int', comment: '价格，单位分'})
  price: number;

  @Index()
  @Column({type: 'varchar', length: 64, comment: '订单号'})
  code: string;

  @Column({type: 'simple-json', nullable: true, comment: '支付数据'})
  paymentMetadata?: PaymentMetadata;

  @Column({type: 'varchar', nullable: true, comment: '支付方式'})
  paymentMethod?: string;

  @Index()
  @Column({type: 'datetime', nullable: true, comment: '支付时间'})
  paymentAt?: Date;

  @Column({
    type: 'varchar',
    length: 32,
    comment: '状态： pendingOpen-等待开启 opened-已开启',
    default: WishBoxActivityBuyStatus.PendingOpen,
  })
  status: WishBoxActivityBuyStatus;

  @Column({
    type: 'varchar',
    length: 32,
    comment: '支付状态：pendingPay - 待支付 paid-已支付 refunded-已退款',
    default: WishBoxActivityBuyPayStatus.PendingPay,
  })
  payStatus: WishBoxActivityBuyPayStatus;

  @Column({type: 'int', comment: '渠道id'})
  channelId: ID;

  @Column({type: 'varchar', nullable: true, comment: '退款原因'})
  refundReason?: string;
}
