import {DeepPartial, ID, Customer, VendureEntity} from '@vendure/core';
import {Column, Entity, Index, JoinColumn, ManyToOne} from 'typeorm';
import {WishBoxActivity} from './wish-box-activity.entity';
import {WishBoxActivityPrize} from './wish-box-activity-prize.entity';

@Entity()
export class WishBoxActivityPick extends VendureEntity {
  constructor(input?: DeepPartial<WishBoxActivityPick>) {
    super(input);
  }

  @Index()
  @Column({type: 'int', comment: '心愿盒子活动id'})
  activityId: ID;

  @ManyToOne(() => WishBoxActivity)
  @JoinColumn({name: 'activityId'})
  activity: WishBoxActivity;

  @Index()
  @Column({type: 'int', comment: '用户id'})
  customerId: ID;

  @ManyToOne(() => Customer)
  @JoinColumn({name: 'customerId'})
  customer: Customer;

  @Index()
  @Column({type: 'int', comment: '心愿盒子活动奖品id'})
  activityPrizeId: ID;

  @ManyToOne(() => WishBoxActivityPrize)
  @JoinColumn({name: 'activityPrizeId'})
  activityPrize: WishBoxActivityPrize;

  @Column({type: 'int', comment: '渠道id'})
  channelId: ID;
}
