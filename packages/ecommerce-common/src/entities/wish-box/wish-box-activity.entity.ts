import {DeepPartial, ID, VendureEntity} from '@vendure/core';
import {Column, Entity, Index, OneToMany} from 'typeorm';
import {
  WishBoxActivityPeriodUnit,
  WishBoxActivityPrizeValidityMode,
  WishBoxActivityStatus,
} from '../../generated-admin-types';
import {WishBoxActivityOpenStrategy} from './wish-box-activity-open-strategy.entity';
import {WishBoxActivityPrize} from './wish-box-activity-prize.entity';

@Entity()
export class WishBoxActivity extends VendureEntity {
  constructor(input?: DeepPartial<WishBoxActivity>) {
    super(input);
  }

  @Column({type: 'varchar', comment: '名称'})
  name: string;

  @Column({type: 'varchar', comment: '备注'})
  remarks: string;

  @Index()
  @Column({type: 'datetime', comment: '开始时间'})
  startAt: Date;

  @Index()
  @Column({type: 'datetime', comment: '结束时间'})
  endAt: Date;

  @Column({type: 'int', comment: '普通盒子循环周期', default: 0})
  period: number;

  @Column({type: 'varchar', comment: '普通盒子循环周期单位', default: WishBoxActivityPeriodUnit.Day})
  periodUnit: WishBoxActivityPeriodUnit;

  @Column({type: 'int', comment: '普通盒子周期内用户次数限制', default: 0})
  periodPerLimit: number;

  @Column({type: 'boolean', comment: '启动免费盒子', default: false})
  freeBoxOpen: boolean;

  @Column({type: 'int', comment: '免费盒子循环周期', default: 0})
  freeBoxPeriod: number;

  @Column({type: 'varchar', comment: '免费盒子循环周期单位', default: WishBoxActivityPeriodUnit.Day})
  freeBoxPeriodUnit: WishBoxActivityPeriodUnit;

  @Column({type: 'int', comment: '免费盒子周期内用户抽取次数限制', default: 0})
  freeBoxPeriodPerLimit: number;

  @Column({type: 'int', comment: '免费盒子用户中奖次数限制，-1为不限制', default: 0})
  freeBoxPerWinLimit: number;

  @Column({type: 'varchar', comment: '免费盒子奖品有效模式', default: WishBoxActivityPrizeValidityMode.Relative})
  freeBoxPrizeValidityMode: WishBoxActivityPrizeValidityMode;

  @Column({type: 'int', comment: '免费盒子奖品有效天数', default: 0})
  freeBoxPrizeValidityDay: number;

  @Column({type: 'datetime', comment: '免费盒子奖品有效开始时间', nullable: true})
  freeBoxPrizeValidityStartAt?: Date;

  @Column({type: 'datetime', comment: '免费盒子奖品有效结束时间', nullable: true})
  freeBoxPrizeValidityExpireAt?: Date;

  @Column({type: 'text', comment: '规则'})
  rules: string;

  @Column({type: 'varchar', comment: '活动状态', default: WishBoxActivityStatus.Draft})
  status: WishBoxActivityStatus;

  @Column({type: 'int', comment: '渠道id'})
  channelId: ID;

  @OneToMany(() => WishBoxActivityOpenStrategy, strategy => strategy.activity)
  wishBoxActivityOpenStrategies: WishBoxActivityOpenStrategy[];

  @OneToMany(() => WishBoxActivityPrize, prize => prize.activity)
  wishBoxActivityPrizes: WishBoxActivityPrize[];

  @Column({nullable: true, type: 'varchar', comment: '盲盒标题图片'})
  titleImage: string;

  @Column({nullable: true, type: 'varchar', comment: '盲盒分享封面图片'})
  shareCover: string;
}
