import {DeepPartial, ID, ProductVariant, VendureEntity} from '@vendure/core';
import {Column, Entity, Index, JoinColumn, ManyToOne} from 'typeorm';
import {WishBoxActivityRecord} from './wish-box-activity-record.entity';
import {WishBoxActivityPrize} from './wish-box-activity-prize.entity';

@Entity()
export class WishBoxActivityRecordItem extends VendureEntity {
  constructor(input?: DeepPartial<WishBoxActivityRecordItem>) {
    super(input);
  }

  @Index()
  @Column({type: 'int', comment: '心愿盒子活动记录id'})
  recordId: ID;

  @ManyToOne(() => WishBoxActivityRecord)
  @JoinColumn({name: 'recordId'})
  record: WishBoxActivityRecord;

  @Index()
  @Column({type: 'int', comment: '心愿盒子活动奖品id'})
  activityPrizeId: ID;

  @ManyToOne(() => WishBoxActivityPrize)
  @JoinColumn({name: 'activityPrizeId'})
  activityPrize: WishBoxActivityPrize;

  @Column({type: 'varchar', length: 32, comment: '奖品类型：product-产品'})
  type: string;

  @ManyToOne(() => ProductVariant)
  @JoinColumn({name: 'targetId'})
  productVariant: ProductVariant;

  @Column({type: 'int', comment: '奖品id', nullable: true})
  targetId?: ID;

  @Column({type: 'int', comment: '渠道id'})
  channelId: ID;
}
