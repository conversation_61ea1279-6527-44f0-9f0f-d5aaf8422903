import {DeepPartial, ID, Customer, VendureEntity, Order} from '@vendure/core';
import {Column, Entity, Index, JoinColumn, ManyToOne, OneToMany} from 'typeorm';
import {WishBoxActivity} from './wish-box-activity.entity';
import {WishBoxActivityBuy} from './wish-box-activity-buy.entity';
import {WishBoxActivityRecordStatus, WishBoxType} from '../../generated-admin-types';
import {WishBoxActivityRecordItem} from './wish-box-activity-record-item.entity';

@Entity()
export class WishBoxActivityRecord extends VendureEntity {
  constructor(input?: DeepPartial<WishBoxActivityRecord>) {
    super(input);
  }

  @Index()
  @Column({type: 'int', comment: '心愿盒子活动id'})
  activityId: ID;

  @ManyToOne(() => WishBoxActivity)
  @JoinColumn({name: 'activityId'})
  activity: WishBoxActivity;

  @Index()
  @Column({type: 'int', comment: '用户id'})
  customerId: ID;

  @ManyToOne(() => Customer)
  @JoinColumn({name: 'customerId'})
  customer: Customer;

  @Index()
  @Column({type: 'int', comment: '购买记录关联id', nullable: true})
  activityBuyId?: ID;

  @ManyToOne(() => WishBoxActivityBuy, {nullable: true})
  @JoinColumn({name: 'activityBuyId'})
  activityBuy?: WishBoxActivityBuy;

  @Column({type: 'int', comment: '订单id', nullable: true})
  orderId?: ID;

  @ManyToOne(() => Order, {nullable: true})
  @JoinColumn({name: 'orderId'})
  order?: Order;

  @Column({type: 'varchar', length: 16, comment: '盒子类型：free-免费 normal-普通'})
  boxType: WishBoxType;

  @Column({type: 'boolean', comment: '是否空奖'})
  isEmpty: boolean;

  @Index()
  @Column({type: 'varchar', length: 64, comment: '开盒子编号'})
  code: string;

  @Index()
  @Column({type: 'datetime', nullable: true, comment: '开始时间，空为不限制'})
  startAt?: Date;

  @Index()
  @Column({type: 'datetime', nullable: true, comment: '过期时间，空为不限制'})
  expireAt?: Date;

  @Column({
    type: 'varchar',
    length: 32,
    comment: '状态：pendingDelivery - 待提货  delivered-已提货 refunded-已退货',
    default: WishBoxActivityRecordStatus.PendingDelivery,
  })
  status: WishBoxActivityRecordStatus;

  @Column({type: 'boolean', comment: '是否在购物车中', default: false})
  inShoppingCart: boolean;

  @Column({type: 'int', comment: '渠道id'})
  channelId: ID;

  @OneToMany(() => WishBoxActivityRecordItem, item => item.record)
  wishBoxActivityRecordItems: WishBoxActivityRecordItem[];
}
