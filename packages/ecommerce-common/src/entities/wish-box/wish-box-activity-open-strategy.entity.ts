import {DeepPartial, ID, VendureEntity} from '@vendure/core';
import {Column, Entity, Index, JoinColumn, ManyToOne} from 'typeorm';
import {WishBoxActivity} from './wish-box-activity.entity';
import {WishBoxActivityOpenStrategyStatus} from '../../generated-admin-types';

@Entity()
export class WishBoxActivityOpenStrategy extends VendureEntity {
  constructor(input?: DeepPartial<WishBoxActivityOpenStrategy>) {
    super(input);
  }

  @Index()
  @Column({type: 'int', comment: '心愿盒子活动id'})
  activityId: ID;

  @ManyToOne(() => WishBoxActivity)
  @JoinColumn({name: 'activityId'})
  activity: WishBoxActivity;

  @Column({type: 'int', comment: '抽取次数'})
  count: number;

  @Column({type: 'int', comment: '价格，分'})
  price: number;

  @Column({type: 'float', comment: '心愿商品概率（总概率需为100%）'})
  wishProbability: number;

  @Column({type: 'float', comment: '基础商品概率（总概率需为100%）'})
  baseProbability: number;

  @Column({
    type: 'varchar',
    length: 16,
    default: WishBoxActivityOpenStrategyStatus.Normal,
    comment: '状态：normal-正常',
  })
  status: WishBoxActivityOpenStrategyStatus;

  @Column({type: 'int', comment: '排序号'})
  sort: number;

  @Column({type: 'int', comment: '渠道id'})
  channelId: ID;
}
