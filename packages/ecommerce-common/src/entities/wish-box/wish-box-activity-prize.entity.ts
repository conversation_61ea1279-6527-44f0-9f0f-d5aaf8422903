import {DeepPartial, ID, ProductVariant, VendureEntity} from '@vendure/core';
import {Column, Entity, Index, JoinColumn, ManyToOne} from 'typeorm';
import {WishBoxType, WishBoxPrizeType, WishBoxActivityPrizeStatus} from '../../generated-admin-types';
import {WishBoxActivity} from './wish-box-activity.entity';

@Entity()
export class WishBoxActivityPrize extends VendureEntity {
  constructor(input?: DeepPartial<WishBoxActivityPrize>) {
    super(input);
  }

  @Index()
  @Column({type: 'int', comment: '心愿盒子活动id'})
  activityId: ID;

  @ManyToOne(() => WishBoxActivity)
  @JoinColumn({name: 'activityId'})
  activity: WishBoxActivity;

  @Column({
    type: 'varchar',
    length: 16,
    comment: '盒子类型：free-免费 normal-普通',
  })
  boxType: WishBoxType;

  @Column({
    type: 'float',
    comment: '抽中概率：同类型盒子叠加，空时均分剩余概率，不满100%会出空奖，超出100按比例计算',
    nullable: true,
  })
  probability?: number;

  @Column({
    type: 'varchar',
    length: 16,
    default: WishBoxPrizeType.Product,
    comment: '奖品类型：product-产品',
  })
  type: WishBoxPrizeType;

  @ManyToOne(() => ProductVariant)
  @JoinColumn({name: 'targetId'})
  productVariant: ProductVariant;

  @Column({type: 'int', comment: '奖品id', nullable: true})
  targetId?: ID;

  @Column({
    type: 'varchar',
    length: 16,
    default: WishBoxActivityPrizeStatus.Normal,
  })
  status: WishBoxActivityPrizeStatus;

  @Column({type: 'int', comment: '渠道id'})
  channelId: ID;
}
