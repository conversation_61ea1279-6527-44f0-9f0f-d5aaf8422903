import {Customer, DeepPartial, ID, VendureEntity} from '@vendure/core';
import {Column, Entity, Index, JoinColumn, ManyToOne, Unique} from 'typeorm';

@Entity()
@Unique('UNIQUE_USER_PER_ORG', ['targetType', 'targetId', 'customerId'])
export class SubscriptionMessageRecord extends VendureEntity {
  constructor(input?: DeepPartial<SubscriptionMessageRecord>) {
    super(input);
  }

  @Index()
  @Column({type: 'int', nullable: true, comment: '用户id'})
  customerId: ID;

  @ManyToOne(() => Customer)
  @JoinColumn({name: 'customerId'})
  customer: Customer;

  @Index()
  @Column({type: 'varchar', length: 50, comment: '关联类型'})
  targetType: string;

  @Index()
  @Column({type: 'varchar', length: 50, comment: '关联ID'})
  targetId: string;

  @Column({type: 'boolean', default: false, comment: '是否发送，0表示未发送，1表示已发送'})
  sended: boolean;

  @Column({type: 'varchar', nullable: true, comment: '渠道ID'})
  channelId: ID;
}
