import {ID} from '@vendure/core';
import {BaseCondition, BuyType, PromInstance, PromOrder, PromOrderLine, PromResult, SuperimposeType} from 'prom-engine';
import {ApplicableType, FullDiscountPresentType, PromotionType} from '../../generated-shop-types';
import {customerGroupList} from '../conditions/customer.group.list.conditions';
import {productQuantityContain} from '../conditions/product.quantity.conditions';
import {ProductType} from '../type';
import {ConditionService, GroupType} from './common-conditions';

export class FullMinusCondition extends BaseCondition {
  private conditionService = new ConditionService();

  superimposeCheck(
    order: PromOrder,
    prom: PromInstance,
    result: PromResult,
  ): {result: boolean; lines: PromOrderLine[]} {
    const lines = order.lines.filter(l => {
      const orderLinePromResult = result.orderLinePromResults.find(ol => ol.orderLineId === l.lineId);
      const findSuperimposeConflict = orderLinePromResult?.discountDetails.find(d => {
        if (
          !(
            (d.superimposeType === SuperimposeType.all || d.superimposeTypes?.includes(prom.type)) &&
            (prom.superimposeType === SuperimposeType.all || prom.superimposeTypes?.includes(d.type))
          ) ||
          (d.type === prom.type && d.type !== PromotionType.ActuallyPaid)
        ) {
          return true;
        }
      });
      return !findSuperimposeConflict;
    });
    return {result: !!lines, lines: lines};
  }

  async testAndFindOrderLines(
    order: PromOrder,
    prom: PromInstance,
    result: PromResult,
  ): Promise<{result: boolean; lines: PromOrderLine[]}> {
    const baseTestResult = this.superimposeCheck(order, prom, result);
    const conditions = prom.rules.conditions;
    const membershipPlanId = prom.rules.membershipPlanId;
    //可执行的商品id
    let productId: ID[] = [];
    //可执行的商品类型
    let applicableType = '';
    //可执行的用户组
    let customerGroupIds: ID[] = [];
    let groupType: GroupType = GroupType.all;
    let isOpen = false;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    conditions.map((condition: any) => {
      if (condition.code === productQuantityContain.code) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        condition.args.map((arg: any) => {
          if (arg.name === 'productIds') {
            productId = arg.value;
          }
          if (arg.name === 'type') {
            applicableType = arg.value;
          }
        });
      } else if (condition.code === customerGroupList.code) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        condition.args.map((arg: any) => {
          if (arg.name === 'customerGroupIds') {
            customerGroupIds = arg.value;
          } else if (arg.name === 'groupType') {
            groupType = arg.value;
          } else if (arg.name === 'isOpen') {
            isOpen = arg.value;
          }
        });
      }
    });
    const isLimitCheck = await this.conditionService.checkLimit(
      {
        groupIds: customerGroupIds,
        groupType: groupType,
        isOpen: isOpen,
      },
      membershipPlanId,
    );
    if (!isLimitCheck) {
      baseTestResult.result = false;
      baseTestResult.lines = [];
    }
    baseTestResult.lines = baseTestResult.lines.filter(l => {
      if (applicableType === ApplicableType.All) {
        return true;
      } else if (applicableType === ApplicableType.AvailableGoods) {
        return productId.includes(l.productId);
      } else if (applicableType === ApplicableType.UnusableGoods) {
        return !productId.includes(l.productId);
      }
    });
    const actionsArgs = prom.rules.actions[0].args;
    let fullDiscountPresentType: FullDiscountPresentType = FullDiscountPresentType.QuantityFullReduction;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    actionsArgs.map((arg: any, index: number) => {
      if (arg.name === 'fullDiscountPresentType' && arg.value) {
        fullDiscountPresentType = arg.value;
      }
    });
    if ((fullDiscountPresentType as FullDiscountPresentType) !== FullDiscountPresentType.AmountFullPresent) {
      baseTestResult.lines = baseTestResult.lines.filter(l => l.buyType === BuyType.common);
    }
    // 只有普通商品才可以参与活动
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    baseTestResult.lines = baseTestResult.lines.filter((l: any) => l.productType === ProductType.Normal);
    baseTestResult.result = baseTestResult.lines.length > 0;
    return baseTestResult;
  }
}
