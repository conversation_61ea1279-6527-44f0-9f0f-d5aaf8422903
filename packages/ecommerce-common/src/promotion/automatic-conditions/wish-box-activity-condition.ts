import {BaseCondition, BuyType, PromInstance, PromOrder, PromOrderLine, PromResult} from 'prom-engine';
export class WishBoxActivityCondition extends BaseCondition {
  async testAndFindOrderLines(
    order: PromOrder,
    prom: PromInstance,
    result: PromResult,
  ): Promise<{result: boolean; lines: PromOrderLine[]}> {
    const testResult = this.superimposeCheck(order, prom, result);
    testResult.lines = testResult.lines.filter(l => l.buyType === BuyType.common);
    testResult.result = testResult.lines.length > 0;
    return testResult;
  }
}
