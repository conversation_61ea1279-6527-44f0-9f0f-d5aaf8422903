import {BaseAction, DiscountType, PromInstance, PromOrder, PromOrderLine} from 'prom-engine';
import {PromResult, PromLineResult} from 'prom-engine';
import {WishBoxService} from '../../service';
import {Injectable} from '@nestjs/common';
import {ID, RequestContext} from '@vendure/core';
import {WishBoxType} from '../../generated-admin-types';

@Injectable()
export class WishBoxActivityAction extends BaseAction {
  constructor(private wishBoxService: WishBoxService) {
    super();
  }

  async execute(
    order: PromOrder,
    orderLines: PromOrderLine[],
    prom: PromInstance,
    result: PromResult,
    preResult: PromResult | null,
    emptyForTips: boolean,
  ): Promise<PromLineResult> {
    const promLineResult: PromLineResult = {
      promInstanceId: prom.id,
      orderLines: [],
      discountType: DiscountType.noDiscount,
      discountAmount: 0,
      discountCount: 0,
      superimposeType: prom.superimposeType,
      superimposeTypes: prom.superimposeTypes,
    };

    const customerId = prom.rules.customerId as ID;
    const ctx = prom.rules.ctx as RequestContext;
    const wishBoxActivityRecords = await this.wishBoxService.getWishBoxActivityShoppingCartRecords(ctx, customerId);
    if (!wishBoxActivityRecords?.length) {
      return promLineResult;
    }
    // 只有完全匹配（产品 + 数量）的订单才触发心愿盒子活动的提货
    const skuIdMap = new Map<ID, number>();
    orderLines.forEach(l => {
      const count = skuIdMap.get(l.skuId) || 0;
      skuIdMap.set(l.skuId, count + l.count);
    });
    const skuIDToRecordType = new Map<ID, WishBoxType>();
    for (const wishBoxActivityRecord of wishBoxActivityRecords) {
      const wishBoxActivityRecordItems = wishBoxActivityRecord.wishBoxActivityRecordItems;
      for (const wishBoxActivityRecordItem of wishBoxActivityRecordItems) {
        const productVariant = wishBoxActivityRecordItem.productVariant;
        skuIDToRecordType.set(productVariant.id, wishBoxActivityRecord.boxType);
        const count = skuIdMap.get(productVariant.id);
        if (!count) {
          return promLineResult;
        } else {
          if (count === 1) {
            skuIdMap.delete(productVariant.id);
          } else {
            skuIdMap.set(productVariant.id, count - 1);
          }
        }
      }
    }
    // 如果订单行完全匹配，则触发心愿盒子活动的提货
    if (skuIdMap.size === 0) {
      const discountOrderLines: PromLineResult['orderLines'] = [];
      let lineDiscountAmount = 0;
      let lineDiscountCount = 0;
      orderLines.forEach(l => {
        lineDiscountAmount += l.price;
        lineDiscountCount += l.count;
        discountOrderLines.push({
          orderLineId: l.lineId,
          skuId: l.skuId,
          discountCount: l.count,
          discountAmount: l.price,
          wishBoxActivityBoxType: skuIDToRecordType.get(l.skuId),
        } as PromLineResult['orderLines'][number]);
      });
      promLineResult.orderLines = discountOrderLines;
      promLineResult.discountType = DiscountType.amount;
      promLineResult.discountAmount = lineDiscountAmount;
      promLineResult.discountCount = lineDiscountCount;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (promLineResult as any).wishBoxActivityRecordIds = wishBoxActivityRecords.map(w => w.id);
    }

    return promLineResult;
  }
}
