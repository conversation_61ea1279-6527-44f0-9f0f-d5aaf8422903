import {
  createTestEnvironment,
  registerInitializer,
  SimpleGraphQLClient,
  SqljsInitializer,
  testConfig,
} from '@vendure/testing';

import {initialData} from '../fixtures/initial-data';
import {
  ChannelService,
  DefaultLogger,
  EntityIdStrategy,
  LogLevel,
  mergeConfig,
  RequestContextService,
} from '@vendure/core';
import {TestServer} from '@vendure/testing/lib/test-server';
import {CommonPlugin} from '../../ecommerce-common.plugin';
import {FullDiscountPresentService} from '../../service/full-discount-present.service';
import {PromotionResultDetailService} from '../../service/promotion-result-detail.service';
import {
  ActivityStatus,
  ApplicableType,
  DiscountType,
  FullDiscountPresentType,
  RuleType,
} from '../../generated-admin-types';
import {PromotionType} from '../../generated-shop-types';
import gql from 'graphql-tag';

require('dotenv').config();
jest.setTimeout(30000);

// GraphQL 查询和变更定义
const FULL_DISCOUNT_PRESENT_FRAGMENT = gql`
  fragment FullDiscountPresent on FullDiscountPresent {
    id
    name
    displayName
    type
    status
    startTime
    endTime
    ruleType
    ruleValues {
      minimum
      discountValue {
        discountType
        discount
      }
    }
    applicableProduct {
      applicableType
      productIds
    }
    statisticsData {
      totalPayment
      totalOrders
      customerCount
      averageOrderValue
    }
  }
`;

const UPSERT_FULL_DISCOUNT_PRESENT = gql`
  mutation upsertFullDiscountPresent($input: FullDiscountPresentInput!) {
    upsertFullDiscountPresent(input: $input) {
      ...FullDiscountPresent
    }
  }
  ${FULL_DISCOUNT_PRESENT_FRAGMENT}
`;

const GET_FULL_DISCOUNT_PRESENTS = gql`
  query fullDiscountPresents($options: FullDiscountPresentListOptions!) {
    fullDiscountPresents(options: $options) {
      totalItems
      items {
        ...FullDiscountPresent
      }
    }
  }
  ${FULL_DISCOUNT_PRESENT_FRAGMENT}
`;

describe('FullDiscountPresent Statistics', () => {
  let server: TestServer;
  let adminClient: SimpleGraphQLClient;
  let shopClient: SimpleGraphQLClient;
  let serverStarted = false;
  let channelService: ChannelService;
  let requestContextService: RequestContextService;
  let fullDiscountPresentService: FullDiscountPresentService;
  let promotionResultDetailService: PromotionResultDetailService;

  beforeAll(async () => {
    registerInitializer('sqljs', new SqljsInitializer('__data__'));
    const config = mergeConfig(testConfig, {
      logger: new DefaultLogger({level: LogLevel.Debug}),
      plugins: [CommonPlugin],
      entityIdStrategy: new EntityIdStrategy(),
    });

    ({server, adminClient, shopClient} = createTestEnvironment(config));
    await server.init({
      initialData,
      productsCsvPath: '../fixtures/e2e-products-minimal.csv',
      customerCount: 1,
    });

    channelService = server.app.get(ChannelService);
    requestContextService = server.app.get(RequestContextService);
    fullDiscountPresentService = server.app.get(FullDiscountPresentService);
    promotionResultDetailService = server.app.get(PromotionResultDetailService);
    serverStarted = true;
  }, 60000);

  afterAll(async () => {
    await server.destroy();
  });

  it('Should start successfully', async () => {
    expect(serverStarted).toBe(true);
  });

  describe('活动统计数据查询', () => {
    let testActivityId: string;
    let testPromotionId: string;
    let ctx: any;

    beforeAll(async () => {
      ctx = await requestContextService.create({
        apiType: 'admin',
        languageCode: 'zh',
        channelOrToken: await channelService.getDefaultChannel(),
      });

      // 创建测试活动
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      const input = {
        name: '统计数据测试活动',
        displayName: '统计数据测试',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '用于统计数据测试',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '统计数据测试活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 10000,
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 1000,
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input});
      testActivityId = upsertFullDiscountPresent.id;

      // 获取关联的促销活动ID
      const activity = await fullDiscountPresentService.findOne(ctx, testActivityId);
      testPromotionId = activity?.promotion?.id || activity?.promotionId;
    });

    it('查询活动列表时应该包含统计数据', async () => {
      const {fullDiscountPresents} = await adminClient.query(GET_FULL_DISCOUNT_PRESENTS, {
        options: {
          take: 10,
          skip: 0,
          sort: {
            updatedAt: 'DESC',
          },
        },
      });

      expect(fullDiscountPresents).toBeDefined();
      expect(fullDiscountPresents.items).toBeInstanceOf(Array);

      // 检查统计数据结构
      if (fullDiscountPresents.items.length > 0) {
        const firstItem = fullDiscountPresents.items[0];
        expect(firstItem.statisticsData).toBeDefined();
        expect(firstItem.statisticsData).toHaveProperty('totalPayment');
        expect(firstItem.statisticsData).toHaveProperty('totalOrders');
        expect(firstItem.statisticsData).toHaveProperty('customerCount');
        expect(firstItem.statisticsData).toHaveProperty('averageOrderValue');
      }
    });

    it('应该能够获取满减活动的统计数据', async () => {
      if (!testPromotionId) {
        console.warn('No promotion ID available for statistics test');
        return;
      }

      const statistics = await promotionResultDetailService.statisticsPromotion(
        ctx,
        testPromotionId,
        PromotionType.FullDiscountPresent,
      );

      expect(statistics).toBeDefined();
      expect(statistics).toHaveProperty('totalPayment');
      expect(statistics).toHaveProperty('totalOrders');
      expect(statistics).toHaveProperty('customerCount');
      expect(statistics).toHaveProperty('averageOrderValue');

      // 验证数据类型
      expect(typeof statistics.totalPayment).toBe('number');
      expect(typeof statistics.totalOrders).toBe('number');
      expect(typeof statistics.customerCount).toBe('number');
      expect(typeof statistics.averageOrderValue).toBe('number');

      // 验证数据合理性
      expect(statistics.totalPayment).toBeGreaterThanOrEqual(0);
      expect(statistics.totalOrders).toBeGreaterThanOrEqual(0);
      expect(statistics.customerCount).toBeGreaterThanOrEqual(0);
      expect(statistics.averageOrderValue).toBeGreaterThanOrEqual(0);
    });

    it('应该能够获取实付满赠活动的统计数据', async () => {
      // 创建实付满赠活动
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      const input = {
        name: '实付满赠统计测试',
        displayName: '实付满赠统计',
        type: FullDiscountPresentType.AmountFullPresent,
        remarks: '实付满赠统计测试',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '实付满赠统计测试活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 20000,
            discountValue: {
              discountType: DiscountType.NoDiscount,
              discount: 0,
            },
            freeGiftValues: [
              {
                freeGiftId: '1',
                freeGiftName: '统计测试赠品',
                freeGiftPrice: 0,
                freeGiftProductId: '1',
                maximumOffer: 1,
                priority: 1,
              },
            ],
            maximumOffer: 1,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: true,
        stackingPromotionTypes: ['actuallyPaid'],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input});
      const actuallyPaidActivity = await fullDiscountPresentService.findOne(ctx, upsertFullDiscountPresent.id);
      const actuallyPaidPromotionId = actuallyPaidActivity?.promotion?.id || actuallyPaidActivity?.promotionId;

      if (actuallyPaidPromotionId) {
        const statistics = await promotionResultDetailService.statisticsPromotion(
          ctx,
          actuallyPaidPromotionId,
          PromotionType.ActuallyPaid,
        );

        expect(statistics).toBeDefined();
        expect(statistics).toHaveProperty('totalPayment');
        expect(statistics).toHaveProperty('totalOrders');
        expect(statistics).toHaveProperty('customerCount');
        expect(statistics).toHaveProperty('averageOrderValue');
      }
    });
  });

  describe('统计数据计算逻辑', () => {
    let ctx: any;

    beforeAll(async () => {
      ctx = await requestContextService.create({
        apiType: 'admin',
        languageCode: 'zh',
        channelOrToken: await channelService.getDefaultChannel(),
      });
    });

    it('应该能够正确计算平均订单价值', async () => {
      // 模拟统计数据
      const mockStatistics = {
        totalPayment: 10000, // 100元
        totalOrders: 5,
        customerCount: 3,
        averageOrderValue: 0,
      };

      // 计算平均订单价值
      const expectedAverageOrderValue = mockStatistics.totalPayment / mockStatistics.totalOrders;
      mockStatistics.averageOrderValue = expectedAverageOrderValue;

      expect(mockStatistics.averageOrderValue).toBe(2000); // 20元
    });

    it('应该能够处理零订单的情况', async () => {
      const mockStatistics = {
        totalPayment: 0,
        totalOrders: 0,
        customerCount: 0,
        averageOrderValue: 0,
      };

      // 当订单数为0时，平均订单价值应该为0
      const expectedAverageOrderValue = mockStatistics.totalOrders > 0 
        ? mockStatistics.totalPayment / mockStatistics.totalOrders 
        : 0;

      expect(expectedAverageOrderValue).toBe(0);
    });

    it('应该能够验证统计数据的一致性', async () => {
      const mockStatistics = {
        totalPayment: 15000, // 150元
        totalOrders: 3,
        customerCount: 2,
        averageOrderValue: 5000, // 50元
      };

      // 验证平均订单价值计算是否正确
      const calculatedAverage = mockStatistics.totalPayment / mockStatistics.totalOrders;
      expect(calculatedAverage).toBe(mockStatistics.averageOrderValue);

      // 验证客户数量不应该超过订单数量
      expect(mockStatistics.customerCount).toBeLessThanOrEqual(mockStatistics.totalOrders);
    });
  });

  describe('管理后台统计数据展示', () => {
    it('管理后台查询活动列表时应该包含统计数据', async () => {
      const ctx = await requestContextService.create({
        apiType: 'admin',
        languageCode: 'zh',
        channelOrToken: await channelService.getDefaultChannel(),
      });

      const result = await fullDiscountPresentService.findAll(
        ctx,
        {take: 10, skip: 0},
        [],
        true, // isAdmin = true
      );

      expect(result).toBeDefined();
      expect(result.items).toBeInstanceOf(Array);

      // 检查是否为管理后台添加了统计数据
      if (result.items.length > 0) {
        const firstItem = result.items[0] as any;
        expect(firstItem.statisticsData).toBeDefined();
      }
    });

    it('非管理后台查询时不应该包含统计数据', async () => {
      const ctx = await requestContextService.create({
        apiType: 'shop',
        languageCode: 'zh',
        channelOrToken: await channelService.getDefaultChannel(),
      });

      const result = await fullDiscountPresentService.findAll(
        ctx,
        {take: 10, skip: 0},
        [],
        false, // isAdmin = false
      );

      expect(result).toBeDefined();
      expect(result.items).toBeInstanceOf(Array);

      // 检查非管理后台是否不包含统计数据
      if (result.items.length > 0) {
        const firstItem = result.items[0] as any;
        expect(firstItem.statisticsData).toBeUndefined();
      }
    });
  });
});
