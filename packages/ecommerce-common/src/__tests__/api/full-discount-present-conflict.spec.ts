import {
  createTestEnvironment,
  registerInitializer,
  SimpleGraphQLClient,
  SqljsInitializer,
  testConfig,
} from '@vendure/testing';

import {initialData} from '../fixtures/initial-data';
import {
  ChannelService,
  DefaultLogger,
  EntityIdStrategy,
  LogLevel,
  mergeConfig,
  RequestContextService,
} from '@vendure/core';
import {TestServer} from '@vendure/testing/lib/test-server';
import {CommonPlugin} from '../../ecommerce-common.plugin';
import {
  ActivityStatus,
  ApplicableType,
  DiscountType,
  FullDiscountPresentType,
  RuleType,
} from '../../generated-admin-types';
import gql from 'graphql-tag';

require('dotenv').config();
jest.setTimeout(30000);

// GraphQL 查询和变更定义
const FULL_DISCOUNT_PRESENT_FRAGMENT = gql`
  fragment FullDiscountPresent on FullDiscountPresent {
    id
    name
    displayName
    type
    status
    startTime
    endTime
    ruleType
    ruleValues {
      minimum
      discountValue {
        discountType
        discount
      }
    }
    applicableProduct {
      applicableType
      productIds
    }
    stackingDiscountSwitch
    stackingPromotionTypes
  }
`;

const UPSERT_FULL_DISCOUNT_PRESENT = gql`
  mutation upsertFullDiscountPresent($input: FullDiscountPresentInput!) {
    upsertFullDiscountPresent(input: $input) {
      ...FullDiscountPresent
    }
  }
  ${FULL_DISCOUNT_PRESENT_FRAGMENT}
`;

const DISCOUNT_ACTIVITY_FRAGMENT = gql`
  fragment DiscountActivity on DiscountActivity {
    id
    displayName
    name
    status
    startTime
    endTime
    minimum
    discount
    productIds
    stackingDiscountSwitch
    stackingPromotionTypes
  }
`;

const UPSERT_DISCOUNT_ACTIVITY = gql`
  mutation upsertDiscountActivity($input: DiscountActivityInput!) {
    upsertDiscountActivity(input: $input) {
      ...DiscountActivity
    }
  }
  ${DISCOUNT_ACTIVITY_FRAGMENT}
`;

const PACKAGE_DISCOUNT_FRAGMENT = gql`
  fragment PackageDiscount on PackageDiscount {
    id
    name
    displayName
    status
    startTime
    endTime
    price
    selectCount
    productIds
    stackingDiscountSwitch
    stackingPromotionTypes
  }
`;

const UPSERT_PACKAGE_DISCOUNT = gql`
  mutation upsertPackageDiscount($input: PackageDiscountInput!) {
    upsertPackageDiscount(input: $input) {
      ...PackageDiscount
    }
  }
  ${PACKAGE_DISCOUNT_FRAGMENT}
`;

describe('FullDiscountPresent Conflict Detection', () => {
  let server: TestServer;
  let adminClient: SimpleGraphQLClient;
  let shopClient: SimpleGraphQLClient;
  let serverStarted = false;
  let channelService: ChannelService;
  let requestContextService: RequestContextService;

  beforeAll(async () => {
    registerInitializer('sqljs', new SqljsInitializer('__data__'));
    const config = mergeConfig(testConfig, {
      logger: new DefaultLogger({level: LogLevel.Debug}),
      plugins: [CommonPlugin],
      entityIdStrategy: new EntityIdStrategy(),
    });

    ({server, adminClient, shopClient} = createTestEnvironment(config));
    await server.init({
      initialData,
      productsCsvPath: '../fixtures/e2e-products-minimal.csv',
      customerCount: 1,
    });

    channelService = server.app.get(ChannelService);
    requestContextService = server.app.get(RequestContextService);
    serverStarted = true;
  }, 60000);

  afterAll(async () => {
    await server.destroy();
  });

  it('Should start successfully', async () => {
    expect(serverStarted).toBe(true);
  });

  describe('满减优惠活动与满减优惠活动冲突检测', () => {
    it('创建时间重叠的满减活动应该失败', async () => {
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      // 创建第一个满减活动
      const firstActivity = {
        name: '第一个满减活动',
        displayName: '第一个满减',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '第一个活动',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '第一个满减活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 10000,
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 1000,
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent: firstResult} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {
        input: firstActivity,
      });

      expect(firstResult).toBeDefined();

      // 尝试创建时间重叠的第二个满减活动，应该失败
      const secondActivity = {
        ...firstActivity,
        name: '第二个满减活动',
        displayName: '第二个满减',
        remarks: '第二个活动',
      };

      await expect(adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input: secondActivity})).rejects.toThrow();
    });

    it('创建时间不重叠的满减活动应该成功', async () => {
      const firstStartTime = new Date();
      const firstEndTime = new Date();
      firstEndTime.setDate(firstEndTime.getDate() + 3);

      const secondStartTime = new Date();
      secondStartTime.setDate(secondStartTime.getDate() + 5);
      const secondEndTime = new Date();
      secondEndTime.setDate(secondEndTime.getDate() + 10);

      // 创建第一个满减活动
      const firstActivity = {
        name: '时间不重叠活动1',
        displayName: '不重叠1',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '时间不重叠测试1',
        startTime: firstStartTime.toISOString(),
        endTime: firstEndTime.toISOString(),
        introduce: '时间不重叠活动1',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 10000,
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 1000,
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const secondActivity = {
        ...firstActivity,
        name: '时间不重叠活动2',
        displayName: '不重叠2',
        remarks: '时间不重叠测试2',
        startTime: secondStartTime.toISOString(),
        endTime: secondEndTime.toISOString(),
      };

      const {upsertFullDiscountPresent: firstResult} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {
        input: firstActivity,
      });
      const {upsertFullDiscountPresent: secondResult} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {
        input: secondActivity,
      });

      expect(firstResult).toBeDefined();
      expect(secondResult).toBeDefined();
      expect(firstResult.id).not.toBe(secondResult.id);
    });
  });

  describe('实付满赠活动叠加功能测试', () => {
    it('创建叠加的实付满赠活动应该成功', async () => {
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      // 创建第一个实付满赠活动（不叠加）
      const firstActivity = {
        name: '实付满赠不叠加',
        displayName: '实付不叠加',
        type: FullDiscountPresentType.AmountFullPresent,
        remarks: '实付满赠不叠加测试',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '实付满赠不叠加活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 20000,
            discountValue: {
              discountType: DiscountType.NoDiscount,
              discount: 0,
            },
            freeGiftValues: [
              {
                freeGiftId: '1',
                freeGiftName: '赠品1',
                freeGiftPrice: 0,
                freeGiftProductId: '1',
                maximumOffer: 1,
                priority: 1,
              },
            ],
            maximumOffer: 1,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      // 创建第二个实付满赠活动（叠加）
      const secondActivity = {
        ...firstActivity,
        name: '实付满赠叠加',
        displayName: '实付叠加',
        remarks: '实付满赠叠加测试',
        stackingDiscountSwitch: true,
        stackingPromotionTypes: ['actuallyPaid'],
      };

      const {upsertFullDiscountPresent: firstResult} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {
        input: firstActivity,
      });
      const {upsertFullDiscountPresent: secondResult} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {
        input: secondActivity,
      });

      expect(firstResult).toBeDefined();
      expect(secondResult).toBeDefined();
      expect(firstResult.stackingDiscountSwitch).toBe(false);
      expect(secondResult.stackingDiscountSwitch).toBe(true);
      expect(secondResult.stackingPromotionTypes).toContain('actuallyPaid');
    });
  });
});
