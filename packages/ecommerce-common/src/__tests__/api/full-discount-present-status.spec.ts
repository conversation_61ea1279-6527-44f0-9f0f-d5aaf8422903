import {
  createTestEnvironment,
  registerInitializer,
  SimpleGraphQLClient,
  SqljsInitializer,
  testConfig,
} from '@vendure/testing';

import {initialData} from '../fixtures/initial-data';
import {
  ChannelService,
  DefaultLogger,
  EntityIdStrategy,
  LogLevel,
  mergeConfig,
  RequestContextService,
} from '@vendure/core';
import {TestServer} from '@vendure/testing/lib/test-server';
import {CommonPlugin} from '../../ecommerce-common.plugin';
import {FullDiscountPresentService} from '../../service/full-discount-present.service';
import {
  ActivityStatus,
  ApplicableType,
  DiscountType,
  FullDiscountPresentType,
  RuleType,
} from '../../generated-admin-types';
import gql from 'graphql-tag';

require('dotenv').config();
jest.setTimeout(30000);

// GraphQL 查询和变更定义
const FULL_DISCOUNT_PRESENT_FRAGMENT = gql`
  fragment FullDiscountPresent on FullDiscountPresent {
    id
    name
    displayName
    type
    status
    startTime
    endTime
    ruleType
    ruleValues {
      minimum
      discountValue {
        discountType
        discount
      }
    }
    applicableProduct {
      applicableType
      productIds
    }
  }
`;

const UPSERT_FULL_DISCOUNT_PRESENT = gql`
  mutation upsertFullDiscountPresent($input: FullDiscountPresentInput!) {
    upsertFullDiscountPresent(input: $input) {
      ...FullDiscountPresent
    }
  }
  ${FULL_DISCOUNT_PRESENT_FRAGMENT}
`;

const GET_FULL_DISCOUNT_PRESENT = gql`
  query fullDiscountPresent($id: ID!, $options: FullDiscountPresentListOptions) {
    fullDiscountPresent(id: $id, options: $options) {
      ...FullDiscountPresent
    }
  }
  ${FULL_DISCOUNT_PRESENT_FRAGMENT}
`;

const FAILURE_FULL_DISCOUNT_PRESENT = gql`
  mutation failureFullDiscountPresent($id: ID!) {
    failureFullDiscountPresent(id: $id) {
      ...FullDiscountPresent
    }
  }
  ${FULL_DISCOUNT_PRESENT_FRAGMENT}
`;

describe('FullDiscountPresent Status Management', () => {
  let server: TestServer;
  let adminClient: SimpleGraphQLClient;
  let shopClient: SimpleGraphQLClient;
  let serverStarted = false;
  let channelService: ChannelService;
  let requestContextService: RequestContextService;
  let fullDiscountPresentService: FullDiscountPresentService;

  beforeAll(async () => {
    registerInitializer('sqljs', new SqljsInitializer('__data__'));
    const config = mergeConfig(testConfig, {
      logger: new DefaultLogger({level: LogLevel.Debug}),
      plugins: [CommonPlugin],
      entityIdStrategy: new EntityIdStrategy(),
    });

    ({server, adminClient, shopClient} = createTestEnvironment(config));
    await server.init({
      initialData,
      productsCsvPath: '../fixtures/e2e-products-minimal.csv',
      customerCount: 1,
    });

    channelService = server.app.get(ChannelService);
    requestContextService = server.app.get(RequestContextService);
    fullDiscountPresentService = server.app.get(FullDiscountPresentService);
    serverStarted = true;
  }, 60000);

  afterAll(async () => {
    await server.destroy();
  });

  it('Should start successfully', async () => {
    expect(serverStarted).toBe(true);
  });

  describe('活动状态自动管理', () => {
    it('创建未来开始的活动状态应为未开始', async () => {
      const startTime = new Date();
      startTime.setDate(startTime.getDate() + 1); // 明天开始
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7); // 一周后结束

      const input = {
        name: '未来活动',
        displayName: '未来活动',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '测试未来活动',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '未来活动测试',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 10000,
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 1000,
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input});

      expect(upsertFullDiscountPresent).toBeDefined();
      expect(upsertFullDiscountPresent.status).toBe(ActivityStatus.NotStarted);
    });

    it('创建当前进行中的活动状态应为正常', async () => {
      const startTime = new Date();
      startTime.setDate(startTime.getDate() - 1); // 昨天开始
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7); // 一周后结束

      const input = {
        name: '进行中活动',
        displayName: '进行中活动',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '测试进行中活动',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '进行中活动测试',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 10000,
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 1000,
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input});

      expect(upsertFullDiscountPresent).toBeDefined();
      expect(upsertFullDiscountPresent.status).toBe(ActivityStatus.Normal);
    });

    it('创建已结束的活动状态应为已结束', async () => {
      const startTime = new Date();
      startTime.setDate(startTime.getDate() - 7); // 一周前开始
      const endTime = new Date();
      endTime.setDate(endTime.getDate() - 1); // 昨天结束

      const input = {
        name: '已结束活动',
        displayName: '已结束活动',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '测试已结束活动',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '已结束活动测试',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 10000,
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 1000,
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input});

      expect(upsertFullDiscountPresent).toBeDefined();
      expect(upsertFullDiscountPresent.status).toBe(ActivityStatus.HaveEnded);
    });
  });

  describe('活动状态手动管理', () => {
    let testActivityId: string;

    beforeAll(async () => {
      // 创建一个测试活动用于状态管理测试
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      const input = {
        name: '状态管理测试活动',
        displayName: '状态管理测试',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '用于状态管理测试',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '状态管理测试活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 10000,
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 1000,
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input});
      testActivityId = upsertFullDiscountPresent.id;
    });

    it('手动失效活动应该成功', async () => {
      // 验证活动初始状态
      const {fullDiscountPresent: beforeFailure} = await adminClient.query(GET_FULL_DISCOUNT_PRESENT, {
        id: testActivityId,
        options: {},
      });

      expect(beforeFailure.status).toBe(ActivityStatus.Normal);

      // 执行失效操作
      const {failureFullDiscountPresent} = await adminClient.query(FAILURE_FULL_DISCOUNT_PRESENT, {
        id: testActivityId,
      });

      expect(failureFullDiscountPresent).toBeDefined();
      expect(failureFullDiscountPresent.id).toBe(testActivityId);
      expect(failureFullDiscountPresent.status).toBe(ActivityStatus.Failure);

      // 验证活动状态已更新
      const {fullDiscountPresent: afterFailure} = await adminClient.query(GET_FULL_DISCOUNT_PRESENT, {
        id: testActivityId,
        options: {},
      });

      expect(afterFailure.status).toBe(ActivityStatus.Failure);
    });
  });

  describe('活动状态自动切换定时任务', () => {
    it('应该能够执行状态切换定时任务', async () => {
      const ctx = await requestContextService.create({
        apiType: 'admin',
        languageCode: 'zh',
        channelOrToken: await channelService.getDefaultChannel(),
      });

      // 调用状态切换方法
      await expect(fullDiscountPresentService.fullReductionAndFullGift(ctx)).resolves.not.toThrow();
    });

    it('应该能够执行全部渠道的状态切换', async () => {
      // 调用全部渠道状态切换方法
      await expect(fullDiscountPresentService.fullReductionAndFullGiftAll()).resolves.not.toThrow();
    });
  });

  describe('活动状态验证', () => {
    it('失效的活动不应该出现在冲突检测中', async () => {
      const ctx = await requestContextService.create({
        apiType: 'admin',
        languageCode: 'zh',
        channelOrToken: await channelService.getDefaultChannel(),
      });

      // 获取未失效的活动
      const notFailureActivities = await fullDiscountPresentService.getNotFailureFullDiscountPresent(ctx);

      // 验证返回的活动都不是失效状态
      notFailureActivities.forEach(activity => {
        expect(activity.status).not.toBe(ActivityStatus.Failure);
        expect(activity.status).not.toBe(ActivityStatus.HaveEnded);
      });
    });

    it('已删除的活动不应该出现在查询结果中', async () => {
      const ctx = await requestContextService.create({
        apiType: 'admin',
        languageCode: 'zh',
        channelOrToken: await channelService.getDefaultChannel(),
      });

      // 获取活动列表
      const {items} = await fullDiscountPresentService.findAll(ctx, {take: 100, skip: 0}, []);

      // 验证返回的活动都没有被删除
      items.forEach(activity => {
        expect(activity.deletedAt).toBeNull();
      });
    });
  });
});
