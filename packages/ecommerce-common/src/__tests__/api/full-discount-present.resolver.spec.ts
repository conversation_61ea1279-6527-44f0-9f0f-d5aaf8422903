import {
  createTestEnvironment,
  registerInitializer,
  SimpleGraphQLClient,
  SqljsInitializer,
  testConfig,
} from '@vendure/testing';

import {ChannelService, DefaultLogger, LogLevel, mergeConfig, RequestContextService} from '@vendure/core';
import {TestServer} from '@vendure/testing/lib/test-server';
import gql from 'graphql-tag';
import {CommonPlugin} from '../../ecommerce-common.plugin';
import {
  ActivityStatus,
  ApplicableType,
  DiscountType,
  FullDiscountPresentType,
  RuleType,
} from '../../generated-admin-types';
import {initialData} from '../fixtures/initial-data';

require('dotenv').config();
jest.setTimeout(20000);

// GraphQL 查询和变更定义
const FULL_DISCOUNT_PRESENT_FRAGMENT = gql`
  fragment FullDiscountPresent on FullDiscountPresent {
    id
    name
    displayName
    type
    remarks
    status
    startTime
    endTime
    introduce
    ruleType
    whetherRestrictUsers
    groupType
    stackingDiscountSwitch
    memberPlanIds
    stackingPromotionTypes
    ruleValues {
      minimum
      discountValue {
        discountType
        discount
      }
      freeGiftValues {
        freeGiftId
        freeGiftName
        freeGiftPrice
        freeGiftProductId
        maximumOffer
        priority
      }
      maximumOffer
    }
    applicableProduct {
      applicableType
      productIds
    }
    statisticsData {
      totalPayment
      totalOrders
      customerCount
      averageOrderValue
    }
    showLabelInCommodity
  }
`;

const UPSERT_FULL_DISCOUNT_PRESENT = gql`
  mutation upsertFullDiscountPresent($input: FullDiscountPresentInput!) {
    upsertFullDiscountPresent(input: $input) {
      ...FullDiscountPresent
    }
  }
  ${FULL_DISCOUNT_PRESENT_FRAGMENT}
`;

const GET_FULL_DISCOUNT_PRESENT = gql`
  query fullDiscountPresent($id: ID!, $options: FullDiscountPresentListOptions) {
    fullDiscountPresent(id: $id, options: $options) {
      ...FullDiscountPresent
    }
  }
  ${FULL_DISCOUNT_PRESENT_FRAGMENT}
`;

const GET_FULL_DISCOUNT_PRESENTS = gql`
  query fullDiscountPresents($options: FullDiscountPresentListOptions!) {
    fullDiscountPresents(options: $options) {
      totalItems
      items {
        ...FullDiscountPresent
      }
    }
  }
  ${FULL_DISCOUNT_PRESENT_FRAGMENT}
`;

const FAILURE_FULL_DISCOUNT_PRESENT = gql`
  mutation failureFullDiscountPresent($id: ID!) {
    failureFullDiscountPresent(id: $id) {
      ...FullDiscountPresent
    }
  }
  ${FULL_DISCOUNT_PRESENT_FRAGMENT}
`;

const DELETE_FULL_DISCOUNT_PRESENT = gql`
  mutation softDeleteFullDiscountPresent($id: ID!) {
    softDeleteFullDiscountPresent(id: $id) {
      message
      result
    }
  }
`;

describe('FullDiscountPresent Resolver', () => {
  let server: TestServer;
  let adminClient: SimpleGraphQLClient;
  let shopClient: SimpleGraphQLClient;
  let serverStarted = false;
  let channelService: ChannelService;
  let requestContextService: RequestContextService;

  beforeAll(async () => {
    registerInitializer('sqljs', new SqljsInitializer('__data__'));
    const config = mergeConfig(testConfig, {
      logger: new DefaultLogger({level: LogLevel.Debug}),
      plugins: [CommonPlugin],
      entityIdStrategy: new EntityIdStrategy(),
    });

    ({server, adminClient, shopClient} = createTestEnvironment(config));
    await server.init({
      initialData,
      productsCsvPath: '../fixtures/e2e-products-minimal.csv',
      customerCount: 1,
    });

    channelService = server.app.get(ChannelService);
    requestContextService = server.app.get(RequestContextService);
    serverStarted = true;
  }, 60000);

  afterAll(async () => {
    await server.destroy();
  });

  it('Should start successfully', async () => {
    expect(serverStarted).toBe(true);
  });

  describe('创建满减优惠活动', () => {
    it('创建满额减活动成功', async () => {
      const startTime = new Date();
      startTime.setDate(startTime.getDate() + 1); // 明天开始
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7); // 一周后结束

      const input = {
        name: '满100减10活动',
        displayName: '满100减10',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '测试满额减活动',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '满100元减10元优惠活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 10000, // 100元，以分为单位
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 1000, // 10元，以分为单位
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input});

      expect(upsertFullDiscountPresent).toBeDefined();
      expect(upsertFullDiscountPresent.name).toBe(input.name);
      expect(upsertFullDiscountPresent.type).toBe(input.type);
      expect(upsertFullDiscountPresent.status).toBe(ActivityStatus.NotStarted);
      expect(upsertFullDiscountPresent.ruleValues).toHaveLength(1);
      expect(upsertFullDiscountPresent.ruleValues[0].minimum).toBe(10000);
    });

    it('创建满件减活动成功', async () => {
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      const input = {
        name: '满3件减5元活动',
        displayName: '满3件减5元',
        type: FullDiscountPresentType.QuantityFullReduction,
        remarks: '测试满件减活动',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '满3件减5元优惠活动',
        ruleType: RuleType.Cycle,
        ruleValues: [
          {
            minimum: 3, // 3件
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 500, // 5元，以分为单位
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input});

      expect(upsertFullDiscountPresent).toBeDefined();
      expect(upsertFullDiscountPresent.name).toBe(input.name);
      expect(upsertFullDiscountPresent.type).toBe(input.type);
      expect(upsertFullDiscountPresent.ruleType).toBe(RuleType.Cycle);
      expect(upsertFullDiscountPresent.status).toBe(ActivityStatus.Normal);
    });

    it('创建实付满赠活动成功', async () => {
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      const input = {
        name: '实付满200送赠品',
        displayName: '实付满200送赠品',
        type: FullDiscountPresentType.AmountFullPresent,
        remarks: '测试实付满赠活动',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '实付满200元送赠品活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 20000, // 200元，以分为单位
            discountValue: {
              discountType: DiscountType.NoDiscount,
              discount: 0,
            },
            freeGiftValues: [
              {
                freeGiftId: '1',
                freeGiftName: '测试赠品',
                freeGiftPrice: 0,
                freeGiftProductId: '1',
                maximumOffer: 1,
                priority: 1,
              },
            ],
            maximumOffer: 1,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: true,
        stackingPromotionTypes: ['actuallyPaid'],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input});

      expect(upsertFullDiscountPresent).toBeDefined();
      expect(upsertFullDiscountPresent.name).toBe(input.name);
      expect(upsertFullDiscountPresent.type).toBe(input.type);
      expect(upsertFullDiscountPresent.stackingDiscountSwitch).toBe(true);
      expect(upsertFullDiscountPresent.ruleValues[0].freeGiftValues).toHaveLength(1);
    });
  });

  describe('查询满减优惠活动', () => {
    let createdActivityId: string;

    beforeAll(async () => {
      // 创建一个测试活动用于查询
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      const input = {
        name: '查询测试活动',
        displayName: '查询测试',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '用于查询测试的活动',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '查询测试活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 5000,
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 500,
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input});
      createdActivityId = upsertFullDiscountPresent.id;
    });

    it('根据ID查询单个活动成功', async () => {
      const {fullDiscountPresent} = await adminClient.query(GET_FULL_DISCOUNT_PRESENT, {
        id: createdActivityId,
        options: {},
      });

      expect(fullDiscountPresent).toBeDefined();
      expect(fullDiscountPresent.id).toBe(createdActivityId);
      expect(fullDiscountPresent.name).toBe('查询测试活动');
      expect(fullDiscountPresent.type).toBe(FullDiscountPresentType.AmountFullReduction);
    });

    it('查询活动列表成功', async () => {
      const {fullDiscountPresents} = await adminClient.query(GET_FULL_DISCOUNT_PRESENTS, {
        options: {
          take: 10,
          skip: 0,
          sort: {
            updatedAt: 'DESC',
          },
        },
      });

      expect(fullDiscountPresents).toBeDefined();
      expect(fullDiscountPresents.totalItems).toBeGreaterThan(0);
      expect(fullDiscountPresents.items).toBeInstanceOf(Array);
      expect(fullDiscountPresents.items.length).toBeGreaterThan(0);
    });

    it('根据活动类型筛选查询成功', async () => {
      const {fullDiscountPresents} = await adminClient.query(GET_FULL_DISCOUNT_PRESENTS, {
        options: {
          take: 10,
          skip: 0,
          filter: {
            type: {
              eq: FullDiscountPresentType.AmountFullReduction,
            },
          },
        },
      });

      expect(fullDiscountPresents).toBeDefined();
      expect(fullDiscountPresents.items.every(item => item.type === FullDiscountPresentType.AmountFullReduction)).toBe(
        true,
      );
    });

    it('根据活动名称筛选查询成功', async () => {
      const {fullDiscountPresents} = await adminClient.query(GET_FULL_DISCOUNT_PRESENTS, {
        options: {
          take: 10,
          skip: 0,
          filter: {
            name: {
              contains: '查询测试',
            },
          },
        },
      });

      expect(fullDiscountPresents).toBeDefined();
      expect(fullDiscountPresents.items.some(item => item.name.includes('查询测试'))).toBe(true);
    });
  });

  describe('更新满减优惠活动', () => {
    let activityToUpdate: any;

    beforeAll(async () => {
      // 创建一个测试活动用于更新
      const startTime = new Date();
      startTime.setDate(startTime.getDate() + 1);
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      const input = {
        name: '待更新活动',
        displayName: '待更新',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '用于更新测试的活动',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '待更新活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 5000,
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 500,
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input});
      activityToUpdate = upsertFullDiscountPresent;
    });

    it('更新活动基本信息成功', async () => {
      const updateInput = {
        id: activityToUpdate.id,
        name: '已更新活动',
        displayName: '已更新',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '已更新的活动备注',
        startTime: activityToUpdate.startTime,
        endTime: activityToUpdate.endTime,
        introduce: '已更新的活动介绍',
        ruleType: RuleType.Ladder,
        ruleValues: activityToUpdate.ruleValues,
        applicableProduct: activityToUpdate.applicableProduct,
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input: updateInput});

      expect(upsertFullDiscountPresent).toBeDefined();
      expect(upsertFullDiscountPresent.id).toBe(activityToUpdate.id);
      expect(upsertFullDiscountPresent.name).toBe('已更新活动');
      expect(upsertFullDiscountPresent.displayName).toBe('已更新');
      expect(upsertFullDiscountPresent.remarks).toBe('已更新的活动备注');
    });

    it('更新活动规则成功', async () => {
      const updateInput = {
        id: activityToUpdate.id,
        name: activityToUpdate.name,
        displayName: activityToUpdate.displayName,
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: activityToUpdate.remarks,
        startTime: activityToUpdate.startTime,
        endTime: activityToUpdate.endTime,
        introduce: activityToUpdate.introduce,
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 10000, // 更新为100元
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 1500, // 更新为15元
            },
            maximumOffer: 0,
          },
          {
            minimum: 20000, // 新增200元档位
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 3000, // 30元
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: activityToUpdate.applicableProduct,
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input: updateInput});

      expect(upsertFullDiscountPresent).toBeDefined();
      expect(upsertFullDiscountPresent.ruleValues).toHaveLength(2);
      expect(upsertFullDiscountPresent.ruleValues[0].minimum).toBe(10000);
      expect(upsertFullDiscountPresent.ruleValues[0].discountValue.discount).toBe(1500);
      expect(upsertFullDiscountPresent.ruleValues[1].minimum).toBe(20000);
      expect(upsertFullDiscountPresent.ruleValues[1].discountValue.discount).toBe(3000);
    });
  });

  describe('删除和失效满减优惠活动', () => {
    let activityToDelete: any;
    let activityToFail: any;

    beforeAll(async () => {
      // 创建两个测试活动用于删除和失效
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      const deleteInput = {
        name: '待删除活动',
        displayName: '待删除',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '用于删除测试的活动',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '待删除活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 5000,
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 500,
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const failInput = {...deleteInput, name: '待失效活动', displayName: '待失效', remarks: '用于失效测试的活动'};

      const {upsertFullDiscountPresent: deleteActivity} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {
        input: deleteInput,
      });
      const {upsertFullDiscountPresent: failActivity} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {
        input: failInput,
      });

      activityToDelete = deleteActivity;
      activityToFail = failActivity;
    });

    it('软删除活动成功', async () => {
      const {softDeleteFullDiscountPresent} = await adminClient.query(DELETE_FULL_DISCOUNT_PRESENT, {
        id: activityToDelete.id,
      });

      expect(softDeleteFullDiscountPresent).toBeDefined();
      expect(softDeleteFullDiscountPresent.result).toBe('DELETED');
      expect(softDeleteFullDiscountPresent.message).toBe('delete success');

      // 验证活动已被软删除，查询时应该找不到
      const {fullDiscountPresent} = await adminClient.query(GET_FULL_DISCOUNT_PRESENT, {
        id: activityToDelete.id,
        options: {},
      });

      expect(fullDiscountPresent).toBeNull();
    });

    it('失效活动成功', async () => {
      const {failureFullDiscountPresent} = await adminClient.query(FAILURE_FULL_DISCOUNT_PRESENT, {
        id: activityToFail.id,
      });

      expect(failureFullDiscountPresent).toBeDefined();
      expect(failureFullDiscountPresent.id).toBe(activityToFail.id);
      expect(failureFullDiscountPresent.status).toBe(ActivityStatus.Failure);

      // 验证活动状态已更新
      const {fullDiscountPresent} = await adminClient.query(GET_FULL_DISCOUNT_PRESENT, {
        id: activityToFail.id,
        options: {},
      });

      expect(fullDiscountPresent.status).toBe(ActivityStatus.Failure);
    });
  });

  describe('活动验证规则', () => {
    it('创建活动时验证开始时间不能大于结束时间', async () => {
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() - 1); // 结束时间早于开始时间

      const input = {
        name: '时间错误活动',
        displayName: '时间错误',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '时间验证测试',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '时间错误活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 5000,
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 500,
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      await expect(adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input})).rejects.toThrow();
    });

    it('创建循环规则活动时验证只能有一个规则', async () => {
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      const input = {
        name: '循环规则错误活动',
        displayName: '循环规则错误',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '循环规则验证测试',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '循环规则错误活动',
        ruleType: RuleType.Cycle,
        ruleValues: [
          {
            minimum: 5000,
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 500,
            },
            maximumOffer: 0,
          },
          {
            minimum: 10000,
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 1000,
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      await expect(adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input})).rejects.toThrow();
    });

    it('创建实付满赠活动时验证优惠类型只能为不优惠', async () => {
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      const input = {
        name: '实付满赠错误活动',
        displayName: '实付满赠错误',
        type: FullDiscountPresentType.AmountFullPresent,
        remarks: '实付满赠验证测试',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '实付满赠错误活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 10000,
            discountValue: {
              discountType: DiscountType.FixedAmount, // 实付满赠不能有折扣
              discount: 1000,
            },
            maximumOffer: 0,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      await expect(adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input})).rejects.toThrow();
    });
  });
});
