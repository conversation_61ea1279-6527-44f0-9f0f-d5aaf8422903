import {
  createTestEnvironment,
  registerInitializer,
  SimpleGraphQLClient,
  SqljsInitializer,
  testConfig,
} from '@vendure/testing';

import {initialData} from '../fixtures/initial-data';
import {
  ChannelService,
  DefaultLogger,
  EntityIdStrategy,
  LogLevel,
  mergeConfig,
  RequestContextService,
  ProductService,
} from '@vendure/core';
import {TestServer} from '@vendure/testing/lib/test-server';
import {CommonPlugin} from '../../ecommerce-common.plugin';
import {FullDiscountPresentService} from '../../service/full-discount-present.service';
import {FreeGiftService} from '../../service/free-gift.service';
import {
  ActivityStatus,
  ApplicableType,
  DiscountType,
  FullDiscountPresentType,
  RuleType,
} from '../../generated-admin-types';
import {FreeGiftValue} from '../../generated-shop-types';
import gql from 'graphql-tag';

require('dotenv').config();
jest.setTimeout(30000);

// GraphQL 查询和变更定义
const FULL_DISCOUNT_PRESENT_FRAGMENT = gql`
  fragment FullDiscountPresent on FullDiscountPresent {
    id
    name
    displayName
    type
    status
    startTime
    endTime
    ruleType
    ruleValues {
      minimum
      discountValue {
        discountType
        discount
      }
      freeGiftValues {
        freeGiftId
        freeGiftName
        freeGiftPrice
        freeGiftProductId
        maximumOffer
        priority
      }
      maximumOffer
    }
    applicableProduct {
      applicableType
      productIds
    }
  }
`;

const UPSERT_FULL_DISCOUNT_PRESENT = gql`
  mutation upsertFullDiscountPresent($input: FullDiscountPresentInput!) {
    upsertFullDiscountPresent(input: $input) {
      ...FullDiscountPresent
    }
  }
  ${FULL_DISCOUNT_PRESENT_FRAGMENT}
`;

const GET_FULL_DISCOUNT_PRESENT = gql`
  query fullDiscountPresent($id: ID!, $options: FullDiscountPresentListOptions) {
    fullDiscountPresent(id: $id, options: $options) {
      ...FullDiscountPresent
    }
  }
  ${FULL_DISCOUNT_PRESENT_FRAGMENT}
`;

describe('FullDiscountPresent Gift Management', () => {
  let server: TestServer;
  let adminClient: SimpleGraphQLClient;
  let shopClient: SimpleGraphQLClient;
  let serverStarted = false;
  let channelService: ChannelService;
  let requestContextService: RequestContextService;
  let fullDiscountPresentService: FullDiscountPresentService;
  let freeGiftService: FreeGiftService;
  let productService: ProductService;

  beforeAll(async () => {
    registerInitializer('sqljs', new SqljsInitializer('__data__'));
    const config = mergeConfig(testConfig, {
      logger: new DefaultLogger({level: LogLevel.Debug}),
      plugins: [CommonPlugin],
      entityIdStrategy: new EntityIdStrategy(),
    });

    ({server, adminClient, shopClient} = createTestEnvironment(config));
    await server.init({
      initialData,
      productsCsvPath: '../fixtures/e2e-products-minimal.csv',
      customerCount: 1,
    });

    channelService = server.app.get(ChannelService);
    requestContextService = server.app.get(RequestContextService);
    fullDiscountPresentService = server.app.get(FullDiscountPresentService);
    freeGiftService = server.app.get(FreeGiftService);
    productService = server.app.get(ProductService);
    serverStarted = true;
  }, 60000);

  afterAll(async () => {
    await server.destroy();
  });

  it('Should start successfully', async () => {
    expect(serverStarted).toBe(true);
  });

  describe('赠品配置管理', () => {
    it('创建带赠品的满减活动应该成功', async () => {
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      const input = {
        name: '满减送赠品活动',
        displayName: '满减送赠品',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '测试赠品配置',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '满减送赠品活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 10000, // 满100元
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 1000, // 减10元
            },
            freeGiftValues: [
              {
                freeGiftId: '1',
                freeGiftName: '测试赠品1',
                freeGiftPrice: 0,
                freeGiftProductId: '1',
                maximumOffer: 1,
                priority: 1,
              },
              {
                freeGiftId: '2',
                freeGiftName: '测试赠品2',
                freeGiftPrice: 0,
                freeGiftProductId: '2',
                maximumOffer: 1,
                priority: 2,
              },
            ],
            maximumOffer: 2,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input});

      expect(upsertFullDiscountPresent).toBeDefined();
      expect(upsertFullDiscountPresent.ruleValues[0].freeGiftValues).toHaveLength(2);
      expect(upsertFullDiscountPresent.ruleValues[0].maximumOffer).toBe(2);
    });

    it('创建实付满赠活动应该成功', async () => {
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      const input = {
        name: '实付满赠活动',
        displayName: '实付满赠',
        type: FullDiscountPresentType.AmountFullPresent,
        remarks: '测试实付满赠',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '实付满赠活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 20000, // 实付满200元
            discountValue: {
              discountType: DiscountType.NoDiscount,
              discount: 0,
            },
            freeGiftValues: [
              {
                freeGiftId: '3',
                freeGiftName: '实付赠品',
                freeGiftPrice: 0,
                freeGiftProductId: '3',
                maximumOffer: 1,
                priority: 1,
              },
            ],
            maximumOffer: 1,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: true,
        stackingPromotionTypes: ['actuallyPaid'],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input});

      expect(upsertFullDiscountPresent).toBeDefined();
      expect(upsertFullDiscountPresent.type).toBe(FullDiscountPresentType.AmountFullPresent);
      expect(upsertFullDiscountPresent.ruleValues[0].freeGiftValues).toHaveLength(1);
      expect(upsertFullDiscountPresent.ruleValues[0].discountValue.discountType).toBe(DiscountType.NoDiscount);
    });

    it('阶梯规则多档赠品配置应该成功', async () => {
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      const input = {
        name: '阶梯赠品活动',
        displayName: '阶梯赠品',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '测试阶梯赠品',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '阶梯赠品活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 10000, // 满100元
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 1000,
            },
            freeGiftValues: [
              {
                freeGiftId: '4',
                freeGiftName: '初级赠品',
                freeGiftPrice: 0,
                freeGiftProductId: '4',
                maximumOffer: 1,
                priority: 1,
              },
            ],
            maximumOffer: 1,
          },
          {
            minimum: 20000, // 满200元
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 2500,
            },
            freeGiftValues: [
              {
                freeGiftId: '5',
                freeGiftName: '高级赠品',
                freeGiftPrice: 0,
                freeGiftProductId: '5',
                maximumOffer: 1,
                priority: 1,
              },
              {
                freeGiftId: '6',
                freeGiftName: '额外赠品',
                freeGiftPrice: 0,
                freeGiftProductId: '6',
                maximumOffer: 1,
                priority: 2,
              },
            ],
            maximumOffer: 2,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input});

      expect(upsertFullDiscountPresent).toBeDefined();
      expect(upsertFullDiscountPresent.ruleValues).toHaveLength(2);
      expect(upsertFullDiscountPresent.ruleValues[0].freeGiftValues).toHaveLength(1);
      expect(upsertFullDiscountPresent.ruleValues[1].freeGiftValues).toHaveLength(2);
    });
  });

  describe('赠品业务逻辑测试', () => {
    let testActivityId: string;
    let ctx: any;

    beforeAll(async () => {
      ctx = await requestContextService.create({
        apiType: 'admin',
        languageCode: 'zh',
        channelOrToken: await channelService.getDefaultChannel(),
      });

      // 创建测试活动
      const startTime = new Date();
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + 7);

      const input = {
        name: '赠品业务测试活动',
        displayName: '赠品业务测试',
        type: FullDiscountPresentType.AmountFullReduction,
        remarks: '赠品业务逻辑测试',
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        introduce: '赠品业务测试活动',
        ruleType: RuleType.Ladder,
        ruleValues: [
          {
            minimum: 10000,
            discountValue: {
              discountType: DiscountType.FixedAmount,
              discount: 1000,
            },
            freeGiftValues: [
              {
                freeGiftId: '7',
                freeGiftName: '业务测试赠品',
                freeGiftPrice: 0,
                freeGiftProductId: '1', // 使用实际存在的产品ID
                maximumOffer: 1,
                priority: 1,
              },
            ],
            maximumOffer: 1,
          },
        ],
        applicableProduct: {
          applicableType: ApplicableType.All,
          productIds: [],
        },
        stackingDiscountSwitch: false,
        stackingPromotionTypes: [],
        whetherRestrictUsers: false,
        memberPlanIds: [],
      };

      const {upsertFullDiscountPresent} = await adminClient.query(UPSERT_FULL_DISCOUNT_PRESENT, {input});
      testActivityId = upsertFullDiscountPresent.id;
    });

    it('应该能够获取活动赠品列表', async () => {
      const activity = await fullDiscountPresentService.findOne(ctx, testActivityId);
      expect(activity).toBeDefined();

      const gifts = await fullDiscountPresentService.activityGifts(ctx, activity!);
      expect(gifts).toBeDefined();
      expect(Array.isArray(gifts)).toBe(true);
    });

    it('应该能够生成赠品描述文本', async () => {
      const activity = await fullDiscountPresentService.findOne(ctx, testActivityId);
      expect(activity).toBeDefined();

      const freeGiftValues: FreeGiftValue[] = [
        {
          freeGiftId: '7',
          freeGiftName: '业务测试赠品',
          freeGiftPrice: 0,
          freeGiftProductId: '1',
          maximumOffer: 1,
          priority: 1,
        },
      ];

      const giftsStr = await fullDiscountPresentService.getGiftsStr(ctx, freeGiftValues);
      expect(giftsStr).toBeDefined();
      expect(Array.isArray(giftsStr)).toBe(true);
    });

    it('应该能够过滤无效的赠品商品', async () => {
      // 测试商品过滤逻辑
      const filterResult = await fullDiscountPresentService.filterGiftProduct(ctx, '999999'); // 不存在的商品ID
      expect(filterResult.isFiltered).toBe(true);

      // 测试有效商品
      const validFilterResult = await fullDiscountPresentService.filterGiftProduct(ctx, '1'); // 存在的商品ID
      expect(validFilterResult.isFiltered).toBe(false);
    });

    it('应该能够生成活动内容描述', async () => {
      const activity = await fullDiscountPresentService.findOne(ctx, testActivityId);
      expect(activity).toBeDefined();

      const content = await fullDiscountPresentService.activityContent(ctx, activity!);
      expect(content).toBeDefined();

      if (activity!.ruleType === RuleType.Ladder) {
        expect(content).toHaveProperty('fullDiscount');
        expect(content).toHaveProperty('fullMinus');
        expect(content).toHaveProperty('fullPresent');
      } else if (activity!.ruleType === RuleType.Cycle) {
        expect(content).toHaveProperty('fullMinus');
        expect(content).toHaveProperty('fullPresent');
      }
    });

    it('应该能够生成活动概要描述', async () => {
      const activity = await fullDiscountPresentService.findOne(ctx, testActivityId);
      expect(activity).toBeDefined();

      const synopsis = fullDiscountPresentService.activitySynopsis(ctx, activity!);
      expect(synopsis).toBeDefined();
      expect(synopsis).toHaveProperty('synopsisStr');
      expect(synopsis).toHaveProperty('synopsisTags');
      expect(typeof synopsis.synopsisStr).toBe('string');
      expect(Array.isArray(synopsis.synopsisTags)).toBe(true);
    });
  });
});
