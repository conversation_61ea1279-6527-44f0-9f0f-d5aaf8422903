import {CustomFieldConfig, LanguageCode} from '@vendure/core';
import {Distributor, OrderPromotionResult} from './entities';
import {CloseReasonType, LimitType, OrderBuyType, ProductType, PurchasePattern} from './generated-shop-types';
export const ShippingMethodCustomFields: CustomFieldConfig[] = [
  {
    name: 'priority',
    type: 'int',
    public: true,
    nullable: true,
    defaultValue: 0,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '优先级',
      },
    ],
  },
];

export const ProductOptionGroupCustomFields: CustomFieldConfig[] = [
  {
    name: 'priority',
    type: 'int',
    public: true,
    nullable: true,
    defaultValue: 0,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '优先级',
      },
    ],
  },
];
export const ProductOptionCustomFields: CustomFieldConfig[] = [
  {
    name: 'priority',
    type: 'int',
    public: true,
    nullable: true,
    defaultValue: 0,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '优先级',
      },
    ],
  },
];
export const AssetCustomFields: CustomFieldConfig[] = [
  {
    name: 'oldSource',
    type: 'string',
    public: true,
    nullable: true,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '原始资源',
      },
    ],
  },
  {
    name: 'retries',
    type: 'int',
    public: true,
    nullable: true,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '重试次数',
      },
    ],
    defaultValue: 0,
  },
  {
    // 是否需要处理
    name: 'isNeedHandle',
    type: 'boolean',
    public: true,
    nullable: true,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否需要处理',
      },
    ],
    defaultValue: true,
  },
  {
    //是否需要预处理
    name: 'isNeedPreprocess',
    type: 'boolean',
    public: true,
    nullable: true,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否需要预处理',
      },
    ],
    defaultValue: true,
  },
];
export const OrderCustomFields: CustomFieldConfig[] = [
  {
    name: 'remark',
    type: 'string',
    public: true,
    nullable: true,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '备注',
      },
    ],
  },
  {
    name: 'orderPromotionResult',
    type: 'relation',
    entity: OrderPromotionResult,
    graphQLType: 'OrderPromotionResult',
    public: true,
    nullable: true,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '订单促销结果',
      },
    ],
    eager: true,
  },
  // {
  //   name: 'refundShippingFee',
  //   type: 'int',
  //   public: true,
  //   nullable: true,
  //   label: [
  //     {
  //       languageCode: LanguageCode.zh,
  //       value: '退款运费',
  //     },
  //   ],
  //   defaultValue: 0,
  // },
  {
    name: 'merchantRemarks',
    type: 'string',
    public: true,
    defaultValue: '',
    nullable: true,
  },
  // 订单支付类型
  {
    name: 'buyType',
    type: 'string',
    public: true,
    nullable: true,
    defaultValue: OrderBuyType.Ordinary,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '支付类型(正常支付/积分兑换)',
      },
    ],
  },
  {
    // 是否需要在订单列表隐藏
    name: 'isHidden',
    type: 'boolean',
    public: true,
    nullable: true,
    defaultValue: false,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否需要在订单列表隐藏',
      },
    ],
  },
  // 积分兑换订单是否售后退回积分
  {
    name: 'isReturnPoints',
    type: 'boolean',
    public: true,
    nullable: true,
    defaultValue: true,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '积分兑换订单是否售后退回积分',
      },
    ],
  },
  // 收货人名称
  {
    name: 'receiverName',
    type: 'string',
    public: true,
    nullable: true,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '收货人名称',
      },
    ],
    defaultValue: '',
  },
  // 收货人电话
  {
    name: 'receiverPhone',
    type: 'string',
    public: true,
    nullable: true,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '收货人电话',
      },
    ],
    defaultValue: '',
  },
  {
    // 订单关闭原因类型
    name: 'closeReasonType',
    type: 'string',
    public: true,
    nullable: true,
    defaultValue: CloseReasonType.Normal,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '订单关闭原因类型',
      },
    ],
  },
  // 邮费计算类型
  {
    name: 'shippingFeeType',
    type: 'string',
    public: true,
    nullable: true,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '邮费计算类型',
      },
    ],
    defaultValue: 'order',
  },
];

export const ProductVariantCustomFields: CustomFieldConfig[] = [
  {
    name: 'costPrice',
    type: 'int',
    public: true,
    nullable: true,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '成本价',
      },
    ],
  },
  {
    name: 'virtualTargetType',
    type: 'string',
    public: true,
    nullable: true,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '虚拟目标类型',
      },
    ],
    defaultValue: '',
  },
  {
    name: 'virtualTargetId',
    type: 'string',
    public: true,
    nullable: true,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '虚拟目标id',
      },
    ],
    defaultValue: '',
  },
  {
    // 是否展示立即购买
    name: 'isShowBuyNow',
    type: 'boolean',
    public: true,
    nullable: true,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否展示立即购买',
      },
    ],
    defaultValue: true,
  },
];
export const PromotionCustomFields: CustomFieldConfig[] = [
  {
    name: 'type',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '优惠类型',
      },
    ],
    type: 'string',
    public: true,
    nullable: true,
  },
  {
    name: 'stackingDiscountSwitch',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否优惠叠加',
      },
    ],
    type: 'boolean',
    public: true,
    nullable: false,
    defaultValue: false,
  },
  {
    name: 'stackingPromotionTypes',
    list: true,
    label: [{languageCode: LanguageCode.zh, value: '叠加活动'}],
    type: 'string',
    public: true,
    nullable: true,
  },
  {
    name: 'isAutomatic',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否自动',
      },
    ],
    type: 'boolean',
    public: true,
    nullable: false,
    defaultValue: false,
  },
  {
    name: 'activityName',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '优惠名称',
      },
    ],
    type: 'string',
    public: true,
    nullable: true,
  },
];
export const OrderLineCustomFields: CustomFieldConfig[] = [
  {
    name: 'purchasePattern',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '产品类型',
      },
    ],
    type: 'string',
    public: true,
    defaultValue: PurchasePattern.Ordinary,
    nullable: false,
  },
  {
    name: 'promInstanceIds',
    list: true,
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '促销实例id',
      },
    ],
    type: 'int',
    public: true,
    nullable: true,
  },
  {
    name: 'isReview',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否评价',
      },
    ],
    type: 'boolean',
    public: true,
    nullable: true,
    defaultValue: false,
  },
  {
    name: 'isAvailableAfterSale',
    type: 'boolean',
    public: true,
    defaultValue: true,
    nullable: true,
  },
];

export const CustomerCustomFields: CustomFieldConfig[] = [
  {
    name: 'distributor',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '分销员',
      },
    ],
    type: 'relation',
    entity: Distributor,
    graphQLType: 'Distributor',
    public: true,
    nullable: true,
    eager: true,
  },
  {
    name: 'points',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '积分',
      },
      {
        languageCode: LanguageCode.en,
        value: 'points',
      },
    ],
    type: 'int',
    public: true,
    defaultValue: 0,
    nullable: false,
  },
  {
    name: 'isModified',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否修改昵称',
      },
    ],
    type: 'boolean',
    public: true,
    defaultValue: false,
  },
];

export const ChannelCustomFields: CustomFieldConfig[] = [
  //赠送积分规则 1元=1积分
  {
    //满足的金额
    name: 'amount',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '金额(分)',
      },
    ],
    type: 'int',
    public: true,
    defaultValue: 100,
    nullable: false,
  },
  {
    //赠送的积分
    name: 'points',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '积分',
      },
    ],
    type: 'int',
    public: true,
    defaultValue: 10,
    nullable: false,
  },
  {
    name: 'isShowNavigation',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否显示导航',
      },
    ],
    type: 'boolean',
    public: true,
    defaultValue: true,
  },
  {
    name: 'isShowCategory',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否显示分类',
      },
    ],
    type: 'boolean',
    public: true,
    defaultValue: true,
  },
  {
    name: 'isShowCart',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否显示购物车',
      },
    ],
    type: 'boolean',
    public: true,
    defaultValue: true,
  },
  {
    name: 'isShowMember',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否显示会员中心',
      },
    ],
    type: 'boolean',
    public: true,
    defaultValue: true,
  },
  {
    name: 'isShowMine',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否显示我的',
      },
    ],
    type: 'boolean',
    public: true,
    defaultValue: true,
  },
  {
    //用户协议
    name: 'userAgreement',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '用户协议',
      },
    ],
    type: 'text',
    public: true,
    nullable: true,
  },
  {
    //隐私政策
    name: 'privacyPolicy',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '隐私政策',
      },
    ],
    type: 'text',
    public: true,
    nullable: true,
  },
  {
    name: 'retroactivePeriod',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '追溯期(天)',
      },
    ],
    type: 'int',
    public: true,
    defaultValue: 0,
  },
  {
    name: 'matomoSiteId',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: 'matomo站点',
      },
    ],
    type: 'string',
    public: true,
    nullable: true,
  },
];

export const ProductCustomFields: CustomFieldConfig[] = [
  {
    name: 'particulars',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '详情图片',
      },
    ],
    ui: {
      component: 'particulars-select',
    },
    list: true,
    type: 'text',
    public: true,
    nullable: true,
  },
  {
    name: 'price',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '商品价格',
      },
    ],
    type: 'int',
    public: true,
    nullable: false,
  },
  {
    name: 'markingPrice',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '划线价',
      },
    ],
    type: 'int',
    public: true,
    nullable: true,
  },
  {
    name: 'hidden',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否隐藏',
      },
    ],
    type: 'boolean',
    defaultValue: false,
    public: true,
    nullable: true,
  },
  {
    name: 'freeGift',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否赠品',
      },
    ],
    type: 'boolean',
    defaultValue: false,
    public: true,
    nullable: true,
  },
  {
    name: 'putOnSaleType',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '开售类型',
      },
    ],
    type: 'string',
    public: true,
    nullable: true,
  },
  {
    name: 'putOnSaleTime',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '开售时间',
      },
    ],
    type: 'datetime',
    public: true,
    nullable: true,
  },
  {
    name: 'timedTakedown',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '定时下架',
      },
    ],
    type: 'boolean',
    defaultValue: false,
    public: true,
    nullable: true,
  },
  {
    name: 'takedownTime',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '下架时间',
      },
    ],
    type: 'datetime',
    public: true,
    nullable: true,
  },
  {
    name: 'smallProgramQRCodeLink',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '小程序二维码链接',
      },
    ],
    type: 'string',
    unique: true,
    public: true,
    nullable: true,
  },
  {
    name: 'salesVolume',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '销量',
      },
    ],
    type: 'int',
    public: true,
    nullable: true,
  },
  {
    name: 'limitType',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '限购类型',
      },
    ],
    type: 'string',
    public: true,
    defaultValue: LimitType.Unlimited,
    nullable: true,
  },
  {
    name: 'limitCount',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '限购数量',
      },
    ],
    type: 'int',
    defaultValue: -1,
    public: true,
    nullable: true,
  },
  {
    name: 'productType',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '产品类型',
      },
    ],
    type: 'string',
    public: true,
    nullable: true,
    defaultValue: ProductType.Ordinary,
  },
  {
    // 虚拟商品详情类型
    name: 'virtualTargetType',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '虚拟商品详情类型',
      },
    ],
    type: 'string',
    public: true,
    nullable: true,
    defaultValue: '',
  },
  {
    // 是否支持售后
    name: 'isSupportAfterSale',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否支持售后',
      },
    ],
    type: 'boolean',
    public: true,
    nullable: true,
    defaultValue: true,
  },
  {
    name: 'isHiddenCart',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '是否隐藏购物车',
      },
    ],
    type: 'boolean',
    public: true,
    nullable: true,
    defaultValue: false,
  },
  {
    name: 'shareCover',
    label: [
      {
        languageCode: LanguageCode.zh,
        value: '封面图片',
      },
    ],
    ui: {
      component: 'particulars-select',
    },
    type: 'string',
    public: true,
    nullable: true,
  },
];
