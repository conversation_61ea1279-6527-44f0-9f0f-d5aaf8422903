import {ID, RequestContext, TransactionalConnection} from '@vendure/core';
import {
  CustomerLevelConditionCheckResult,
  CustomerLevelConditions,
  CustomerLevelConditionTradeAmount,
  CustomerOrderState,
} from '../../types';
import {CustomerLevelCondition} from './customer-level-condition';
import {CustomerOrder} from '../../entities';
import {DateTime} from 'luxon';
import {Injectable} from '@nestjs/common';
// import {escape} from 'lodash';

@Injectable()
export class TradeAmountCondition implements CustomerLevelCondition {
  constructor(private connection: TransactionalConnection) {}
  async checkCondition(
    ctx: RequestContext,
    customerId: ID,
    conditionTradeAmount: CustomerLevelConditions,
    conditionValue?: number,
  ): Promise<CustomerLevelConditionCheckResult> {
    let totalAmount;
    if (conditionValue === null || conditionValue === undefined) {
      const orders = await this.connection
        .getRepository(ctx, CustomerOrder)
        .createQueryBuilder('customerOrder')
        .where('customerOrder.customerId = :customerId', {customerId})
        .andWhere('customerOrder.channelId = :channelId', {channelId: ctx.channelId})
        .andWhere('customerOrder.state in (:...states)', {
          states: [
            CustomerOrderState.ConfirmReceiptOfGoods,
            CustomerOrderState.Delivered,
            CustomerOrderState.ReviewSettled,
          ],
        })
        .andWhere('customerOrder.paymentTime >= :paymentTime', {
          paymentTime: DateTime.local()
            .minus({
              [(conditionTradeAmount as CustomerLevelConditionTradeAmount).range.unit]: (
                conditionTradeAmount as CustomerLevelConditionTradeAmount
              ).range.value,
            })
            .startOf('day')
            .toJSDate(),
        })
        .getMany();
      totalAmount = orders.map(order => order.paymentAmount - order.refundAmount).reduce((a, b) => a + b, 0);
    } else {
      totalAmount = conditionValue;
    }
    const passed = totalAmount >= (conditionTradeAmount as CustomerLevelConditionTradeAmount).value;
    return {
      passed,
      condition: conditionTradeAmount,
      conditionValues: {tradeAmount: totalAmount},
    };
  }
}
