import {Injectable, Logger} from '@nestjs/common';
import {CustomerLevelservice} from './customer-level.service';
import {<PERSON>ron, CronExpression} from '@nestjs/schedule';
import {
  Channel,
  // Customer,
  LanguageCode,
  ProcessContext,
  RequestContext,
  RequestContextService,
  TransactionalConnection,
} from '@vendure/core';
import {DEFAULT_CHANNEL_CODE} from '@vendure/common/lib/shared-constants';
import {KvsService} from '@scmally/kvs';
import {Between, In, LessThan} from 'typeorm';
import {CustomerLevel, CustomerLevelConfig, CustomerOrder} from '../entities';
import {CustomerOrderService} from './customer-order.service';
import {DateTime} from 'luxon';

@Injectable()
export class CustomerLevelCrontabService {
  // 是否正在重新计算所有会员等级
  private isAsignAllLevel = false;
  private isAsyncOrder = false;
  private timeoutOrders = false;
  private isReAsignLevel = false;
  private isDisableCron = false;
  constructor(
    private connection: TransactionalConnection,
    private customerLevelService: CustomerLevelservice,
    private customerOrderService: CustomerOrderService,
    private processContext: ProcessContext,
    private requestContextService: RequestContextService,
    private kvsService: KvsService,
  ) {}
  init(isDisableCron: boolean) {
    this.isDisableCron = isDisableCron;
  }

  @Cron(CronExpression.EVERY_DAY_AT_3AM)
  async reAsignAllLevel() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isAsignAllLevel) {
        return;
      }
      this.isAsignAllLevel = true;
      try {
        Logger.debug(`定时器执行:给所有人计算会员等级`);
        const ctxs = await this.getAllCtxs();
        for (const ctx of ctxs) {
          const levelConfig = await this.customerLevelService.getCustomerLevelConfig(ctx);
          if (!levelConfig?.enabled) {
            Logger.debug(`定时器执行:会员等级未启用或不需要重新计算,channelId:${ctx.channel.code}`);
            continue;
          }
          if (!levelConfig.lastCalTime) {
            levelConfig.lastCalTime = DateTime.now().minus({years: 10}).toJSDate();
            continue;
          }
          const now = new Date();
          Logger.debug(`定时器执行:会员配置发生变化，重新计算会员等级,channelId:${ctx.channel.code}`);
          let hasMore = true;
          const take = 100;
          while (hasMore) {
            const customerLevels = await this.connection.getRepository(ctx, CustomerLevel).find({
              where: {
                channelId: ctx.channel.id,
                currentLevelTime: LessThan(levelConfig.lastCalTime), // 会员等级配置更新时间之前的会员
              },
              take,
            });
            for (const customerLevel of customerLevels) {
              await this.customerLevelService.assignLevel(ctx, customerLevel.customerId);
            }
            if (customerLevels.length < take) {
              hasMore = false;
            }
          }
          levelConfig.lastCalTime = now;
          await this.connection.getRepository(ctx, CustomerLevelConfig).save(levelConfig);
          Logger.debug(`定时器执行:完成给所有人计算会员等级,channelId:${ctx.channel.code}`);
        }
        Logger.debug(`定时器执行: 完成给所有人计算会员等级`);
      } catch (error) {
        Logger.error(`定时器执行:给所有人计算会员等级错误:${error}`);
      } finally {
        this.isAsignAllLevel = false;
      }
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_4PM)
  async asignTimeoutOrders() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.timeoutOrders) {
        return;
      }
      this.timeoutOrders = true;
      try {
        const ctxs = await this.getAllCtxs();
        if (ctxs.length === 0) {
          return;
        }
        for (const ctx of ctxs) {
          const levelConfig = await this.customerLevelService.getCustomerLevelConfig(ctx);
          if (!levelConfig?.enabled) {
            continue;
          }

          try {
            let shouldContinue = true;
            while (shouldContinue) {
              const orders = await this.connection.getRepository(ctx, CustomerOrder).find({
                where: {
                  channelId: ctx.channel.id,
                  paymentTime: Between(
                    levelConfig.asignTimeoutTime,
                    DateTime.now()
                      .minus({
                        [levelConfig.conditionRange?.unit as string]: levelConfig.conditionRange?.value ?? 1,
                      })
                      .startOf('day')
                      .toJSDate(),
                  ), // 超过30天未支付的订单
                },
                take: 100,
              });
              const customerIds = orders.map(order => order.customerId);
              // 去掉重复的customerId
              const uniqueCustomerIds = [...new Set(customerIds)];
              await this.connection.getRepository(ctx, CustomerLevel).update(
                {
                  customerId: In(uniqueCustomerIds),
                  channelId: ctx.channel.id,
                },
                {
                  shouldReAsign: true,
                },
              );

              if (orders.length < 100) {
                shouldContinue = false;
              }
            }
            levelConfig.asignTimeoutTime = DateTime.now()
              .minus({
                [levelConfig.conditionRange?.unit as string]: levelConfig.conditionRange?.value ?? 1,
              })
              .startOf('day')
              .toJSDate();
            await this.connection.getRepository(ctx, CustomerLevelConfig).save(levelConfig);
          } catch (error) {
            Logger.error(`标记超时订单超时,channelId:${ctx.channel.code}`, error);
          }
        }
      } finally {
        this.timeoutOrders = false;
      }
    }
  }

  @Cron(CronExpression.EVERY_10_MINUTES)
  async asyncOrders() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isAsyncOrder) {
        return;
      }
      this.isAsyncOrder = true;
      try {
        const ctxs = await this.getAllCtxs();
        if (ctxs.length === 0) {
          Logger.debug(`没有可用的渠道，跳过订单同步`);
          return;
        }
        Logger.debug(`定时器执行:开始同步订单数据到会员等级计算`);
        for (const ctx of ctxs) {
          // if (!(await this.customerLevelService.customerLevelEnable(ctx))) {
          //   continue;
          // }

          // 同步订单数据到会员等级计算
          try {
            // 1. 同步普通订单
            let shouldContinue = true;
            while (shouldContinue) {
              /**
               * 异步同步订单数据到会员等级计算
               */
              const orders = await this.customerOrderService.asyncOrders(ctx);
              if (!orders || orders.length < 100) {
                shouldContinue = false; // 没有更多订单了
              }
              if (orders && orders.length > 0) {
                const customerIds = orders.map(order => order.customerId);
                // 去掉重复的customerId
                const uniqueCustomerIds = [...new Set(customerIds)];
                await this.connection.getRepository(ctx, CustomerLevel).update(
                  {
                    customerId: In(uniqueCustomerIds),
                    channelId: ctx.channel.id,
                  },
                  {
                    shouldReAsign: true,
                  },
                );
              }
            }

            // 2. 同步盲盒订单
            Logger.debug(`定时器执行:开始同步盲盒订单数据到会员等级计算`);
            shouldContinue = true;
            while (shouldContinue) {
              /**
               * 异步同步盲盒订单数据到会员等级计算
               */
              const blindBoxOrders = await this.customerOrderService.asyncBlindBoxOrders(ctx);
              if (!blindBoxOrders || blindBoxOrders.length < 100) {
                shouldContinue = false; // 没有更多盲盒订单了
              }
              if (blindBoxOrders && blindBoxOrders.length > 0) {
                const customerIds = blindBoxOrders.map(order => order.customerId);
                // 去掉重复的customerId
                const uniqueCustomerIds = [...new Set(customerIds)];
                await this.connection.getRepository(ctx, CustomerLevel).update(
                  {
                    customerId: In(uniqueCustomerIds),
                    channelId: ctx.channel.id,
                  },
                  {
                    shouldReAsign: true,
                  },
                );
              }
            }
            Logger.debug(`定时器执行:完成同步盲盒订单数据到会员等级计算`);
          } catch (error) {
            Logger.error(`同步订单数据到会员等级计算失败,channelId:${ctx.channel.code}`, error);
            throw error;
          }
        }
      } finally {
        this.isAsyncOrder = false;
      }
    }
  }

  @Cron(CronExpression.EVERY_30_MINUTES)
  async reAsignLevel() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isReAsignLevel) {
        return;
      }
      this.isReAsignLevel = true;
      try {
        Logger.debug(`定时器执行:给需要重新计算会员等级的人计算会员等级`);
        const ctxs = await this.getAllCtxs();
        for (const ctx of ctxs) {
          const levelConfig = await this.customerLevelService.getCustomerLevelConfig(ctx);
          if (!levelConfig?.enabled) {
            Logger.debug(`定时器执行:会员等级未启用或不需要重新计算,channelId:${ctx.channel.code}`);
            continue;
          }
          Logger.debug(`定时器执行: 给标记为需要重新计算会员等级,channelId:${ctx.channel.code}`);
          let hasMore = true;
          const take = 100;
          while (hasMore) {
            const customerLevels = await this.connection.getRepository(ctx, CustomerLevel).find({
              where: {
                channelId: ctx.channel.id,
                shouldReAsign: true,
              },
              take,
            });
            for (const customerLevel of customerLevels) {
              await this.customerLevelService.assignLevel(ctx, customerLevel.customerId);
            }

            if (customerLevels.length < take) {
              hasMore = false;
            }
          }

          Logger.debug(`定时器执行: 完成给标记为需要重新计算等级,channelId:${ctx.channel.code}`);
        }
        Logger.debug(`定时器执行: 完成给标记为需要重新计算等级`);
      } catch (error) {
        Logger.error(`定时器执行:给标记为需要重新计算等级错误:${error}`);
      } finally {
        this.isReAsignLevel = false;
      }
    }
  }

  async getAllCtxs() {
    const adminCtx = await this.requestContextService.create({
      apiType: 'admin',
    });
    const channels = await this.connection.getRepository(adminCtx, Channel).find();
    const ctxs: RequestContext[] = [];
    for (const channel of channels) {
      if (channel.code === DEFAULT_CHANNEL_CODE) {
        continue;
      }
      const ctx = new RequestContext({
        apiType: 'admin',
        isAuthorized: true,
        authorizedAsOwnerOnly: false,
        channel,
        languageCode: LanguageCode.zh_Hans,
      });
      ctxs.push(ctx);
    }
    return ctxs;
  }
}
