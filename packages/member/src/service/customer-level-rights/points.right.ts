import {RequestContext} from '@vendure/core';
import {CustomerLevel} from '../../entities';
import {
  CustomerLevelRightHanlder,
  CustomerLevelRightPoint,
  CustomerLevelRights,
  CustomerLevelRightsDistributeRecord,
} from '../../types';
import {CustomerLevelDistributeRights} from './customer-level-distribute-right';
import {Injectable} from '@nestjs/common';

@Injectable()
export class PointsRight implements CustomerLevelDistributeRights {
  rightHandler: CustomerLevelRightHanlder;
  constructor() {}

  async distributeRight(
    ctx: RequestContext,
    customerLevel: CustomerLevel,
    right: CustomerLevelRights,
  ): Promise<CustomerLevelRightsDistributeRecord> {
    const id = await this.rightHandler.point(ctx, customerLevel, right as CustomerLevelRightPoint);
    return {
      sourceId: id,
      ...right,
    };
  }

  async recysleRight(
    ctx: RequestContext,
    customerLevel: CustomerLevel,
    right: CustomerLevelRightsDistributeRecord,
  ): Promise<void> {
    await this.rightHandler.recyclePoint(ctx, customerLevel, right);
  }

  init(rightHandler: CustomerLevelRightHanlder) {
    this.rightHandler = rightHandler;
  }
}
