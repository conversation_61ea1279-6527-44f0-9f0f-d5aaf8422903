import {gql} from 'graphql-tag';
// eslint-disable-next-line
const _scalar = gql`
  scalar DateTime
`;

const sharedTypes = gql`
  enum OrderPurchaseType {
    # 购物车
    shoppingTrolley
    # 预下单
    outrightPurchase
    # 普通订单直接购买
    regularOrder
  }
  enum SalesStatus {
    # 全部
    all
    # 仓库中
    warehouse
    # 已售罄
    soldOut
    # 销售中
    selling
  }
  enum SubscriptionState {
    underway
    completion
    evaluate
    cancel
  }

  enum FrequencyUnit {
    week
    month
    day
  }

  enum RefundState {
    Pending
    Settled
    Failed
    Cancel
  }

  enum OperationSubscritionType {
    updateAddress
    updateFrequency
    updateFirstShippingDate
    unsubscribe
  }

  type OperationSubscritionValue {
    cancelExplain: String
    cancelCause: String
    cancelMoney: Float
    modification: JSON
    original: JSON
  }

  input SubscritionCancelInput {
    subscriptionId: ID!
    totalMoney: Int
    cancelExplain: String
    cancelCause: String
  }

  type SubscriptionInterval {
    unit: FrequencyUnit
    frequency: [Int]
  }

  type PeriodAndDiscount {
    expireNumberOfPeriod: Int
    discountType: DiscountType
    discountAmount: Int
    recommend: Boolean
  }

  input SubscriptionIntervalInput {
    unit: FrequencyUnit
    frequency: [Int]
  }

  input PeriodAndDiscountInput {
    expireNumberOfPeriod: Int
    discountType: DiscountType
    discountAmount: Int
    recommend: Boolean
  }

  type SubscriptionPlanList implements PaginatedList {
    items: [SubscriptionPlan!]!
    totalItems: Int!
  }

  type OrderRefund implements Node {
    id: ID!
    channels: [Channel!]
    createdAt: DateTime
    updatedAt: DateTime
    state: RefundState
    images: [String]
    cancelType: String
    cancelExplain: String
    cancelCause: String
    refundAmount: Float
    order: Order
  }

  type SubscriptionPlan implements Node {
    id: ID!
    channels: [Channel!]
    createdAt: DateTime
    updatedAt: DateTime
    name: String
    channelId: String
    payUpFront: Boolean
    fixedStartDate: DateTime
    autoRenew: Boolean
    cutOffDays: Int
    subscriptionInterval: SubscriptionInterval
    periodAndDiscount: [PeriodAndDiscount!]
  }

  type SubscriptionList implements PaginatedList {
    items: [Subscription!]!
    totalItems: Int!
  }

  type OrderRefundList implements PaginatedList {
    items: [OrderRefund!]!
    totalItems: Int!
  }
  type Subscription implements Node {
    id: ID!
    code: String
    createdAt: DateTime
    updatedAt: DateTime
    expireNumberOfPeriod: Int
    channels: [Channel!]
    currentNumberOfPeriod: Int
    cutOffDays: Int
    discountType: DiscountType
    discountAmount: Int
    frequencyUnit: FrequencyUnit
    state: SubscriptionState
    customer: Customer
    orders: [Order]
    subscriptionProducts: [SubscriptionProduct!]
    subscriptionPlan: SubscriptionPlan!
    frequency: Int
    firstShippingDate: DateTime
    nextShippingDate: DateTime
    lastShippingDate: DateTime
    totalMoney: Float
    shippingAddress: OrderAddress
  }

  input RefundOrderCustom {
    orderId: ID!
    images: [String]
    cancelType: String
    cancelExplain: String
    cancelCause: String
    refundAmount: Float
  }

  input SubscriptionInput {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    expireNumberOfPeriod: Int
    channelId: ID
    currentNumberOfPeriod: Int
    cutOffDays: Int
    discountType: DiscountType
    discountAmount: Int
    frequencyUnit: FrequencyUnit
    state: SubscriptionState
    frequency: Int
    firstShippingDate: DateTime
    nextShippingDate: DateTime
  }

  input QuerySubscriptionInput {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    expireNumberOfPeriod: Int
    channelId: ID
    currentNumberOfPeriod: Int
    cutOffDays: Int
    discountType: DiscountType
    discountAmount: Int
    frequencyUnit: FrequencyUnit
    state: SubscriptionState
    frequency: Int
    firstShippingDate: DateTime
    nextShippingDate: DateTime
  }

  input UpsertSubscriptionPlanInput {
    id: ID
    name: String!
    payUpFront: Boolean!
    fixedStartDate: DateTime
    autoRenew: Boolean
    cutOffDays: Int
    subscriptionInterval: SubscriptionIntervalInput
    periodAndDiscount: [PeriodAndDiscountInput]
  }

  type SubscriptionProduct implements Node {
    id: ID!
    channels: [Channel]
    productVariant: ProductVariant
    product: Product
    subscription: Subscription
    quantity: Int
  }

  type RefundResult {
    state: String
    transactionId: String
  }

  type SubscriptionOperation implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    operationType: String
    operationValue: OperationSubscritionValue
    subscription: Subscription
  }

  type Logistics {
    id: ID!
    logisticCode: String
    company: String
    state: LogisticState
    logisticInfo: LogisticInfo
    order: Order
    fulfillment: Fulfillment
  }

  type Traces {
    acceptStation: String
    acceptTime: DateTime
  }

  type LogisticInfo {
    logisticCode: String
    shipperCode: String
    traces: [Traces]
    state: LogisticState
    success: String
    courier: String
    courierPhone: String
    updateTime: DateTime
    takeTime: String
    name: String
    site: String
    phone: String
    logo: String
    reason: String
  }

  type ShoppingCart {
    shoppingTrolley: Order
    outrightPurchase: Order
  }

  enum LogisticState {
    codeError
    noInformation
    pickUp
    inTransit
    signFor
    problemShipment
    difficultItem
    returnSignFor
  }
  input ActiveOrderArgs {
    activeOrderInput: String
  }
  input SettlementProduct {
    productVariantId: ID!
    quantity: Int!
  }

  extend type Query {
    subscriptionPlan: [SubscriptionPlan]
    subscriptions(options: SubscriptionListOptions): SubscriptionList
    subscription(subscriptionId: String!): Subscription
    subscriptionOperations(subscriptionId: String!, operationType: OperationSubscritionType!): [SubscriptionOperation]
    subscriptionsUnderway: [Subscription]
    orderRefunds(options: OrderRefundListOptions): OrderRefundList
    orderRefund(orderId: String): OrderRefund
    logistics(orderId: ID!): Logistics
    getActiveOrderByType(
      type: OrderPurchaseType!
      isRemoveMarkUp: Boolean
      isUseMember: Boolean
      isUseShoppingCredit: Boolean
    ): Order
    newProducts(
      options: ProductListOptions
      sku: String
      collectionId: ID
      state: SalesStatus
      isFilterPreSale: Boolean
      isFilterEmptySku: Boolean
    ): ProductList!
    getPurchaseQuantity(productId: ID!, type: OrderPurchaseType, isIncludeCurrentOrder: Boolean): Int
    getExchangeableQuantity(productId: ID!, type: OrderPurchaseType, isIncludeCurrentOrder: Boolean): Int
    eligibleShippingMethodsByOrderType(type: OrderPurchaseType!): [ShippingMethodQuote!]!
    getShoppingCart(isRemoveMarkUp: Boolean, isUseMember: Boolean, isUseShoppingCredit: Boolean): ShoppingCart
    getNumber: Int
    getOrder(type: OrderPurchaseType, isRemoveMarkUp: Boolean): Order
    getOrderNull(type: OrderPurchaseType, isRemoveMarkUp: Boolean): Order
  }

  extend type Mutation {
    upsertSubscriptionPlan(input: UpsertSubscriptionPlanInput, productId: String!): SubscriptionPlan
    deleteSubscriptionPlan(productId: String!): String
    updateSubscriptionAddress(input: CreateAddressInput, subscriptionId: String!): Subscription
    updateSubscriptionFrequency(frequency: Int, subscriptionId: String!): Subscription
    updateSubscriptionFirstShippingDate(firstShippingDate: DateTime, subscriptionId: String!): Subscription
    unsubscribe(input: SubscritionCancelInput): RefundResult
    createOrderBySubscription(subscriptionId: String!): Subscription
    createOrderAfterCreatingCtx(subscriptionId: String!): Subscription
    accomplishRefundOrder(refundId: ID!, state: RefundState!): OrderRefund
    createRefund(refundOrderCustom: RefundOrderCustom): OrderRefund
    cancelRefund(refundId: ID!): OrderRefund
    updateRefund(refundId: ID!, refundOrderCustom: RefundOrderCustom!): OrderRefund
    updatePeriod: String
    createOrder: String
    deleteOrder(orderId: ID!): DeletionResponse
    updateOrderShippingAddress(orderId: ID!, input: CreateAddressInput!): Order
    setShippingAddress(orderId: ID!, input: CreateAddressInput!): Order
    setShippingMethod(orderId: ID!, shippingMethodId: ID!): Order
    confirmReceiptOfGoods(orderId: ID!): Order
    addItemToOrderByOrderType(
      productVariantId: ID!
      quantity: Int!
      type: OrderPurchaseType!
      buyType: OrderBuyType
    ): UpdateOrderItemsResult
    addItemToOrderByWishBoxActivityRecord(wishBoxActivityRecordIds: [ID!]!): Boolean
    adjustOrderLineByOrderType(orderLineId: ID!, quantity: Int!, type: OrderPurchaseType!): Order
    removeOrderLineByOrderType(orderLineIds: [ID!]!, type: OrderPurchaseType!): Order
    removeAllOrderLinesByOrderType(type: OrderPurchaseType!): Order
    shoppingCartSettlement(input: [SettlementProduct!]!): Order
    cancelledOrder(orderId: ID!): Order
    updateShoppingCartSku(orderLineId: ID!, productVariantId: ID!, quantity: Int!): Order

    addSurchargeToOrder(orderId: ID!, surcharge: Int!): Order

    newSetOrderCustomFields(input: UpdateOrderInput!, orderId: ID!): Order

    noLogisticsUpdateOrder(orderId: ID!): Order

    setShippingMethodByOrderType(type: OrderPurchaseType!, shippingMethodId: ID!): Order

    addPaymentToOrderByOrderType(type: OrderPurchaseType!, input: PaymentInputByOrderType!): Order
  }

  input PaymentInputByOrderType {
    method: String!
    metadata: JSON!
  }

  # generated by generateListOptions function
  input SubscriptionListOptions

  # generated by generateListOptions function
  input OrderRefundListOptions
`;

export const shopSchemaExtensions = gql`
  extend type Query {
    orders(options: OrderListOptions): OrderList!
  }
  ${sharedTypes}
`;

export const adminSchemaExtensions = gql`
  ${sharedTypes}
`;
