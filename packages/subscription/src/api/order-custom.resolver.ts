import {Args, Mutation, Query, Resolver} from '@nestjs/graphql';
import {
  CustomerProductService,
  OrderCustomCommonService,
  OrderPromotionResultService,
  WishBoxService,
} from '@scmally/ecommerce-common';
import {OrderBuyType} from '@scmally/ecommerce-common/dist/generated-shop-types';
import {KvsService} from '@scmally/kvs';
import {MemberService} from '@scmally/member';
import {RedLockService} from '@scmally/red-lock';
import {
  ActiveOrderResult,
  PaymentInput,
  SetOrderShippingMethodResult,
  UpdateOrderItemsResult,
} from '@vendure/common/lib/generated-shop-types';
import {CreateAddressInput, QueryOrdersArgs, ShippingMethodQuote} from '@vendure/common/lib/generated-types';
import {
  Allow,
  Ctx,
  ErrorResultUnion,
  EventBus,
  ID,
  Logger,
  NoActiveOrderError,
  Order,
  OrderService,
  PaginatedList,
  PaymentService,
  Permission,
  RelationPaths,
  Relations,
  RequestContext,
  Transaction,
} from '@vendure/core';
import {totalCoveredByPayments} from '@vendure/core/dist/service/helpers/utils/order-utils';
import {OrderRefund} from '../entities';
import {ShoppingCartUpdateEvent} from '../event';
import {
  OrderCustomFields,
  OrderPurchaseType,
  RefundOrderCustom,
  SettlementProduct,
  UpdateOrderCustomFieldsInput,
} from '../generated-shop-types';
import {OrderCustomService} from '../service';
@Resolver('OrderCustomResolver')
export class OrderCustomResolver {
  // total: number;
  // average: number;
  // cnt: number;
  // interval: Interval;
  constructor(
    private customerProductService: CustomerProductService,
    private orderCustomService: OrderCustomService,
    private orderService: OrderService,
    private orderCustomCommonService: OrderCustomCommonService,
    private orderPromotionResultService: OrderPromotionResultService,
    private paymentService: PaymentService,
    private eventBus: EventBus,
    private memberService: MemberService,
    private redLockService: RedLockService,
    private wishBoxService: WishBoxService,
    private kvsService: KvsService,
  ) {
    // this.total = 0;
    // this.average = 0;
    // this.cnt = 0;
    // this.interval = setInterval(() => {
    //   this.c();
    // }, 1000 * 20) as unknown as Interval;
  }
  // c() {
  //   Logger.info(`----------:${this.average}`);
  // }

  @Transaction()
  @Mutation()
  @Allow(Permission.Owner)
  async createRefund(
    @Ctx() ctx: RequestContext,
    @Args('refundOrderCustom') refundOrderCustom: RefundOrderCustom,
  ): Promise<OrderRefund> {
    return this.orderCustomService.createRefund(ctx, refundOrderCustom);
  }

  @Transaction()
  @Query()
  @Allow(Permission.Owner)
  async orderRefund(
    @Ctx() ctx: RequestContext,
    @Args('orderId') orderId: string,
    @Relations({entity: OrderRefund}) relations: RelationPaths<OrderRefund>,
  ): Promise<OrderRefund> {
    return this.orderCustomService.orderRefund(ctx, orderId, relations);
  }

  @Transaction()
  @Mutation()
  @Allow(Permission.Owner)
  async cancelRefund(@Ctx() ctx: RequestContext, @Args('refundId') refundId: ID) {
    return this.orderCustomService.cancelRefund(ctx, refundId);
  }

  @Transaction()
  @Mutation()
  @Allow(Permission.Owner)
  async updateRefund(
    @Ctx() ctx: RequestContext,
    @Args('refundOrderCustom') refundOrderCustom: RefundOrderCustom,
    @Args('refundId') refundId: ID,
  ) {
    return this.orderCustomService.updateRefund(ctx, refundId, refundOrderCustom);
  }
  @Query()
  @Allow(Permission.Owner)
  orders(
    @Ctx() ctx: RequestContext,
    @Args() args: QueryOrdersArgs,
    @Relations(Order) relations: RelationPaths<Order>,
  ): Promise<PaginatedList<Order>> {
    return this.orderCustomService.findOrderAll(ctx, args.options || undefined, relations);
  }

  @Mutation()
  @Allow(Permission.Owner)
  @Transaction()
  deleteOrder(@Ctx() ctx: RequestContext, @Args('orderId') orderId: ID) {
    return this.orderCustomService.softDeleteOrder(ctx, orderId);
  }

  @Mutation()
  @Allow(Permission.Owner)
  @Transaction()
  updateOrderShippingAddress(
    @Ctx() ctx: RequestContext,
    @Args('orderId') orderId: ID,
    @Args('input') input: CreateAddressInput,
  ) {
    return this.orderCustomService.updateOrderShippingAddress(ctx, orderId, input);
  }

  @Mutation()
  @Allow(Permission.Owner)
  @Transaction()
  setShippingAddress(
    @Ctx() ctx: RequestContext,
    @Args('orderId') orderId: ID,
    @Args('input') input: CreateAddressInput,
  ) {
    return this.orderCustomService.setShippingAddress(ctx, orderId, input);
  }

  @Mutation()
  @Allow(Permission.Owner)
  @Transaction()
  setShippingMethod(
    @Ctx() ctx: RequestContext,
    @Args('orderId') orderId: ID,
    @Args('shippingMethodId') shippingMethodId: ID,
  ) {
    this.eventBus.publish(new ShoppingCartUpdateEvent(ctx, orderId, 'update'));
    return this.orderPromotionResultService.setShippingMethod(ctx, orderId, [shippingMethodId]);
  }

  @Mutation()
  @Allow(Permission.Owner)
  @Transaction()
  confirmReceiptOfGoods(@Ctx() ctx: RequestContext, @Args('orderId') orderId: ID) {
    return this.orderCustomService.confirmReceiptOfGoods(ctx, orderId);
  }

  @Query()
  @Allow(Permission.Owner)
  getActiveOrderByType(
    @Ctx() ctx: RequestContext,
    @Args('type') type: OrderPurchaseType,
    @Args('isRemoveMarkUp') isRemoveMarkUp?: boolean,
    @Args('isUseMember') isUseMember?: boolean, //是否使用会员
    @Args('isUseShoppingCredit') isUseShoppingCredit?: boolean, //是否使用购物金
  ) {
    return this.orderCustomService.getActiveOrderByType(ctx, type, isRemoveMarkUp, isUseMember, isUseShoppingCredit);
  }

  @Transaction()
  @Query()
  @Allow(Permission.Owner)
  getShoppingCart(
    @Ctx() ctx: RequestContext,
    @Args('isRemoveMarkUp') isRemoveMarkUp?: boolean,
    @Args('isUseMember') isUseMember?: boolean,
    @Args('isUseShoppingCredit') isUseShoppingCredit?: boolean,
  ) {
    return this.orderCustomService.getShoppingCart(ctx, isRemoveMarkUp, isUseMember, isUseShoppingCredit);
  }

  @Query()
  @Allow(Permission.Owner)
  async eligibleShippingMethodsByOrderType(
    @Ctx() ctx: RequestContext,
    @Args('type') type: OrderPurchaseType,
  ): Promise<ShippingMethodQuote[]> {
    if (ctx.authorizedAsOwnerOnly) {
      const sessionOrder = await this.orderCustomService.getActiveOrderByType(ctx, type);
      if (sessionOrder) {
        let shippingMethodQuotes = await this.orderPromotionResultService.getEligibleShippingMethods(
          ctx,
          sessionOrder.id,
          sessionOrder,
        );
        if (shippingMethodQuotes.length > 1) {
          shippingMethodQuotes = shippingMethodQuotes.sort((a, b) => a.price - b.price);
        }
        return shippingMethodQuotes;
      }
    }
    return [];
  }

  @Transaction()
  @Mutation()
  @Allow(Permission.Owner)
  async setShippingMethodByOrderType(
    @Ctx() ctx: RequestContext,
    @Args('type') type: OrderPurchaseType,
    @Args('shippingMethodId') shippingMethodId: ID,
  ): Promise<ErrorResultUnion<SetOrderShippingMethodResult, Order>> {
    if (ctx.authorizedAsOwnerOnly) {
      const sessionOrder = await this.orderCustomService.getActiveOrderByType(ctx, type);
      if (sessionOrder) {
        const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${sessionOrder.id}`);
        try {
          const order = (await this.orderPromotionResultService.setShippingMethod(
            ctx,
            sessionOrder.id,
            [shippingMethodId],
            sessionOrder,
          )) as Order;
          const orderPromotionResult = await this.orderPromotionResultService.getResultByOrderId(ctx, sessionOrder.id);
          if (orderPromotionResult && order) {
            await this.orderPromotionResultService.upsertResult(
              ctx,
              {orderId: String(sessionOrder.id), promResult: orderPromotionResult.promResult},
              order,
            );
          }
          this.eventBus.publish(new ShoppingCartUpdateEvent(ctx, order.id, 'update'));
          return order;
        } catch (error) {
          Logger.error(`setShippingMethodByOrderType: ${error}`);
          throw error;
        } finally {
          await this.redLockService.unlockResource(lock);
        }
      }
    }
    return new NoActiveOrderError();
  }

  @Query()
  @Allow(Permission.Owner)
  async getPurchaseQuantity(
    @Ctx() ctx: RequestContext,
    @Args('productId') productId: ID,
    @Args('type') type: OrderPurchaseType,
    @Args('isIncludeCurrentOrder') isIncludeCurrentOrder: boolean,
  ) {
    if (!type) {
      type = OrderPurchaseType.RegularOrder;
    }
    const order = await this.orderCustomService.getActiveOrderByType(ctx, type);
    if (!order) {
      return 0;
    }
    return this.orderCustomCommonService.getPurchaseQuantity(ctx, order, productId, isIncludeCurrentOrder);
  }

  @Query()
  @Allow(Permission.Owner)
  // 获取商品可兑换的数量
  async getExchangeableQuantity(
    @Ctx() ctx: RequestContext,
    @Args('productId') productId: ID,
    @Args('type') type: OrderPurchaseType,
    @Args('isIncludeCurrentOrder') isIncludeCurrentOrder: boolean,
  ) {
    if (!type) {
      type = OrderPurchaseType.RegularOrder;
    }
    const order = await this.orderCustomService.getActiveOrderByType(ctx, type);
    if (!order) {
      return 0;
    }
    return this.orderCustomCommonService.getExchangeableQuantity(ctx, order, productId, isIncludeCurrentOrder);
  }

  @Transaction()
  @Mutation()
  @Allow(Permission.UpdateOrder, Permission.Owner)
  async addItemToOrderByOrderType(
    @Ctx() ctx: RequestContext,
    @Args('productVariantId') productVariantId: ID,
    @Args('quantity') quantity: number,
    @Args('type') type: OrderPurchaseType,
    @Args('buyType') buyType: OrderBuyType = OrderBuyType.Ordinary,
  ): Promise<ErrorResultUnion<UpdateOrderItemsResult, Order>> {
    // console.time('addItemToOrderByOrderType');
    const activeUserId = ctx.activeUserId;
    const lock = await this.redLockService.lockResource(`Order:AddItemToOrder:${activeUserId}_${productVariantId}`);
    try {
      if (buyType === OrderBuyType.PointsExchange && type !== OrderPurchaseType.RegularOrder) {
        throw new Error('积分兑换不能加入购物车');
      }
      // console.time('addItemToOrderByOrderType:getActiveOrderByType');
      let order = await this.orderCustomService.getActiveOrderByType(ctx, type);
      // console.timeEnd('addItemToOrderByOrderType:getActiveOrderByType');
      if (!order) {
        throw new NoActiveOrderError();
      }
      if (type === OrderPurchaseType.RegularOrder) {
        if (buyType === OrderBuyType.PointsExchange) {
          (order.customFields as OrderCustomFields).buyType = OrderBuyType.PointsExchange;
          await this.orderService.updateCustomFields(ctx, order.id, {
            buyType: OrderBuyType.PointsExchange,
          });
          await this.orderCustomCommonService.checkPointProduct(ctx, productVariantId);
        } else {
          if ((order.customFields as OrderCustomFields).buyType !== buyType) {
            (order.customFields as OrderCustomFields).buyType = buyType;
            await this.orderService.updateCustomFields(ctx, order.id, {
              buyType: buyType,
            });
          }
        }
      }
      if (type !== OrderPurchaseType.ShoppingTrolley) {
        // 预售商品检查
        // console.time('addItemToOrderByOrderType:checkProductPreSale');
        await this.orderCustomCommonService.checkProductPreSale(ctx, productVariantId);
        // console.timeEnd('addItemToOrderByOrderType:checkProductPreSale');
      }
      if (type !== OrderPurchaseType.RegularOrder) {
        // 检查商品是否是虚拟商品 虚拟商品不允许加入购物车 仅限于直接购买
        // console.time('addItemToOrderByOrderType:checkProductIsVirtual');
        await this.orderCustomCommonService.checkProductIsVirtual(ctx, productVariantId);
        // console.timeEnd('addItemToOrderByOrderType:checkProductIsVirtual');
      } else {
        // 检查商品是否是虚拟商品——会员卡 会员卡需要检查是否已经是会员
        // console.time('addItemToOrderByOrderType:checkWhetherTheUserIsAMember');
        await this.orderCustomCommonService.checkWhetherTheUserIsAMember(ctx, productVariantId);
        // console.timeEnd('addItemToOrderByOrderType:checkWhetherTheUserIsAMember');
      }
      // console.time('addItemToOrderByOrderType:checkProductPurchaseLimitation');
      await this.orderCustomCommonService.checkProductPurchaseLimitation(
        ctx,
        order,
        productVariantId,
        quantity,
        false,
        true,
        buyType,
      );
      // 修改到订单完成支付后再进行判断
      // if (buyType === OrderBuyType.PointsExchange) {
      //   // 如果是积分兑换商品, 判断是否需要隐藏
      //   const isHidden = await this.orderCustomCommonService.checkProductIsHidden(ctx, productVariantId);
      //   (order.customFields as OrderCustomFields).isHidden = isHidden;
      //   await this.orderService.updateCustomFields(ctx, order.id, {
      //     isHidden: isHidden,
      //   });
      // }
      // console.timeEnd('addItemToOrderByOrderType:checkProductPurchaseLimitation');
      // console.time('addItemToOrderByOrderType:checkProductVariantPurchasePermission');
      await this.customerProductService.checkProductVariantPurchasePermission(ctx, productVariantId);
      // console.timeEnd('addItemToOrderByOrderType:checkProductVariantPurchasePermission');
      // console.time('addItemToOrderByOrderType:addItemToOrder');
      order = (await this.orderPromotionResultService.addItemToOrder(
        ctx,
        order.id,
        productVariantId,
        quantity,
        undefined,
        order,
      )) as Order;
      // console.timeEnd('addItemToOrderByOrderType:addItemToOrder');
      // console.time('addItemToOrderByOrderType:applyPriceAdjustments');
      const result = await this.orderPromotionResultService.applyPriceAdjustments(ctx, order);
      // console.timeEnd('addItemToOrderByOrderType:applyPriceAdjustments');
      this.eventBus.publish(new ShoppingCartUpdateEvent(ctx, order.id, 'update'));
      // console.timeEnd('addItemToOrderByOrderType');
      return result;
    } catch (error) {
      Logger.error(`addItemToOrderByOrderType: ${error}`);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  @Transaction()
  @Mutation()
  @Allow(Permission.UpdateOrder, Permission.Owner)
  async addItemToOrderByWishBoxActivityRecord(
    @Ctx() ctx: RequestContext,
    @Args('wishBoxActivityRecordIds') wishBoxActivityRecordIds: ID[],
  ): Promise<Boolean> {
    const record = await this.wishBoxService.wishBoxActivityRecordPickUp(ctx, wishBoxActivityRecordIds);
    if (!record) {
      throw new Error('获取心愿盒子订单标记失败');
    }
    const removed = await this.removeAllOrderLinesByOrderType(ctx, OrderPurchaseType.RegularOrder);
    if (!removed) {
      throw new Error('清空购物车失败');
    }
    const lineIds = record
      .map(m => {
        return m.wishBoxActivityRecordItems.map(m2 => m2.productVariant.id);
      })
      .flat();
    try {
      for (const lId of lineIds) {
        await this.addItemToOrderByOrderType(ctx, lId, 1, OrderPurchaseType.RegularOrder);
      }
    } catch (error) {
      await this.removeAllOrderLinesByOrderType(ctx, OrderPurchaseType.RegularOrder);
      Logger.error(`添加商品规格失败: ${error}`);
      throw error;
    }
    return true;
  }

  @Transaction()
  @Mutation()
  @Allow(Permission.Owner)
  async addPaymentToOrderByOrderType(
    @Ctx() ctx: RequestContext,
    @Args('input') input: PaymentInput,
    @Args('type') type: OrderPurchaseType,
  ): Promise<Order> {
    if (process.env.APP_ENV !== 'dev') {
      throw new Error('Only available in dev environment');
    }
    const order = await this.orderCustomService.getActiveOrderByType(ctx, type);
    if (!order) {
      throw new NoActiveOrderError();
    }
    order.payments = await this.orderService.getOrderPayments(ctx, order.id);
    const amountToPay = order.totalWithTax - totalCoveredByPayments(order);
    const payment = await this.paymentService.createPayment(ctx, order, amountToPay, input.method, input.metadata);
    Logger.debug(`Created payment ${JSON.stringify(payment)} for Order ${order.code}`, 'OrderCustomResolver');
    this.eventBus.publish(new ShoppingCartUpdateEvent(ctx, order.id, 'update'));
    return (await this.orderService.findOne(ctx, order.id)) as Order;
  }

  @Transaction()
  @Mutation()
  @Allow(Permission.UpdateOrder, Permission.Owner)
  async adjustOrderLineByOrderType(
    @Ctx() ctx: RequestContext,
    @Args('orderLineId') orderLineId: ID,
    @Args('quantity') quantity: number,
    @Args('type') type: OrderPurchaseType,
  ): Promise<Order | undefined> {
    if (quantity === 0) {
      return this.removeOrderLineByOrderType(ctx, [orderLineId], type);
    }

    let order = await this.orderCustomService.getActiveOrderByType(ctx, type);
    if (!order) {
      throw new NoActiveOrderError();
    }
    const productVariantId = order.lines.find(l => l.id === orderLineId)?.productVariant.id as ID;
    // order.lines = order.lines.filter(l => l.id !== orderLineId);
    await this.orderCustomCommonService.checkProductPurchaseLimitation(
      ctx,
      order,
      productVariantId,
      quantity,
      false,
      false,
    );
    await this.customerProductService.checkProductVariantPurchasePermission(ctx, productVariantId);
    order = (await this.orderPromotionResultService.adjustOrderLine(
      ctx,
      order.id,
      orderLineId,
      quantity,
      undefined,
      order,
    )) as Order;
    const result = await this.orderPromotionResultService.applyPriceAdjustments(ctx, order);
    this.eventBus.publish(new ShoppingCartUpdateEvent(ctx, order.id, 'update'));
    return result;
  }

  @Transaction()
  @Mutation()
  @Allow(Permission.UpdateOrder, Permission.Owner)
  async removeOrderLineByOrderType(
    @Ctx() ctx: RequestContext,
    @Args('orderLineIds') orderLineIds: ID[],
    @Args('type') type: OrderPurchaseType,
  ): Promise<Order | undefined> {
    let order = await this.orderCustomService.getActiveOrderByType(ctx, type);
    if (!order) {
      throw new NoActiveOrderError();
    }
    const result = await this.orderCustomService.removeItemFromOrder(ctx, order.id, orderLineIds, order, false);
    if (result.order) {
      order = result.order;
    }
    if (result.recalculateOrNot) {
      order = await this.orderPromotionResultService.applyPriceAdjustments(ctx, order);
    }
    this.eventBus.publish(new ShoppingCartUpdateEvent(ctx, order.id, 'update'));
    return order;
  }

  @Transaction()
  @Mutation()
  @Allow(Permission.UpdateOrder, Permission.Owner)
  async removeAllOrderLinesByOrderType(
    @Ctx() ctx: RequestContext,
    @Args('type') type: OrderPurchaseType,
  ): Promise<Order | undefined> {
    const order = await this.orderCustomService.activeOrder(ctx, type);
    if (!order) {
      throw new NoActiveOrderError();
    }
    this.eventBus.publish(new ShoppingCartUpdateEvent(ctx, order.id, 'update'));
    return this.orderCustomService.removeAllItemsFromOrder(ctx, order) as Promise<Order>;
  }

  @Transaction()
  @Mutation()
  @Allow(Permission.Owner, Permission.UpdateOrder)
  shoppingCartSettlement(@Ctx() ctx: RequestContext, @Args('input') input: SettlementProduct[]) {
    return this.orderCustomService.shoppingCartSettlement(ctx, input);
  }

  @Transaction()
  @Mutation()
  @Allow(Permission.Owner, Permission.UpdateOrder)
  cancelledOrder(@Ctx() ctx: RequestContext, @Args('orderId') orderId: ID) {
    return this.orderCustomService.cancelledOrder(ctx, orderId);
  }

  @Transaction()
  @Mutation()
  @Allow(Permission.Owner, Permission.UpdateOrder)
  updateShoppingCartSku(
    @Ctx() ctx: RequestContext,
    @Args('orderLineId') orderLineId: ID,
    @Args('productVariantId') productVariantId: ID,
    @Args('quantity') quantity: number,
  ) {
    return this.orderCustomService.updateShoppingCartSku(ctx, orderLineId, productVariantId, quantity);
  }

  @Transaction()
  @Mutation()
  @Allow(Permission.Owner)
  async newSetOrderCustomFields(
    @Ctx() ctx: RequestContext,
    @Args('input') input: UpdateOrderCustomFieldsInput,
    @Args('orderId') orderId: ID,
  ): Promise<ErrorResultUnion<ActiveOrderResult, Order>> {
    this.eventBus.publish(new ShoppingCartUpdateEvent(ctx, orderId, 'update'));
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return this.orderService.updateCustomFields(ctx, orderId, (input as any).customFields);
  }

  @Query()
  @Allow(Permission.Owner)
  getNumber(@Ctx() ctx: RequestContext) {
    return 1;
  }

  @Query()
  @Allow(Permission.Owner)
  async getOrder(
    @Ctx() ctx: RequestContext,
    @Args('type') type: OrderPurchaseType,
    @Args('isRemoveMarkUp') isRemoveMarkUp?: boolean,
  ) {
    // const start = new Date().getTime();
    const result = await this.orderCustomService.getActiveOrderByType(ctx, type, isRemoveMarkUp);
    // const end = new Date().getTime();
    // this.total += end - start;
    // this.cnt++;
    // this.average = this.total / this.cnt;
    return result;
  }

  @Query()
  @Allow(Permission.Owner)
  getOrderNull(
    @Ctx() ctx: RequestContext,
    @Args('type') type: OrderPurchaseType,
    @Args('isRemoveMarkUp') isRemoveMarkUp?: boolean,
  ) {
    return {} as Order;
  }
}
