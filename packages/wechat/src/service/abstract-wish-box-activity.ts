import {Customer, ID, Order, RequestContext} from '@vendure/core';
import {WeChatPaymentService} from './wechat-payment.service';
import {PaymentInput} from '@scmally/member/dist/ui/generated-shop-types';

export type WishBoxActivityBuy = {
  id: ID;
  code: string;
  price: number;
  activityId: ID;
  activityOpenStrategyId: ID;
  customer: Customer;
};

export interface InterfaceWishBoxActivity {
  getWishBoxActivityBuyByOrderId(ctx: RequestContext, orderId: ID): Promise<WishBoxActivityBuy>;

  getWishBoxActivityBuyByCode(ctx: RequestContext, code: string): Promise<WishBoxActivityBuy>;

  addPaymentToActivityBuys(ctx: RequestContext, wishBoxActivityBuyId: ID, payment: PaymentInput): Promise<boolean>;

  verifyWishBoxActivityShoppingCartRecords(ctx: RequestContext, order: Order): Promise<boolean>;
}

export abstract class AbstractWishBoxActivity implements InterfaceWishBoxActivity {
  protected constructor(protected weChatPaymentService: WeChatPaymentService) {
    this.weChatPaymentService.registerWishBoxActivity(this);
  }
  abstract getWishBoxActivityBuyByCode(ctx: RequestContext, code: string): Promise<WishBoxActivityBuy>;
  abstract getWishBoxActivityBuyByOrderId(ctx: RequestContext, orderId: ID): Promise<WishBoxActivityBuy>;
  abstract addPaymentToActivityBuys(
    ctx: RequestContext,
    wishBoxActivityBuyId: ID,
    payment: PaymentInput,
  ): Promise<boolean>;
  abstract verifyWishBoxActivityShoppingCartRecords(ctx: RequestContext, order: Order): Promise<boolean>;
}
