import {MigrationInterface, QueryRunner} from "typeorm";

export class wishBoxImage1756456574539 implements MigrationInterface {

   public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query("ALTER TABLE `wish_box_activity` ADD `titleImage` varchar(255) NULL COMMENT '盲盒标题图片'", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity` ADD `shareCover` varchar(255) NULL COMMENT '盲盒分享封面图片'", undefined);
   }

   public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query("ALTER TABLE `wish_box_activity` DROP COLUMN `shareCover`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity` DROP COLUMN `titleImage`", undefined);
   }

}
