import {MigrationInterface, QueryRunner} from "typeorm";

export class wishBoxActivityBuy1756285951378 implements MigrationInterface {

   public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query("ALTER TABLE `wish_box_activity_buy` ADD `refundReason` varchar(255) NULL COMMENT '退款原因'", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_buy` CHANGE `payStatus` `payStatus` varchar(32) NOT NULL COMMENT '支付状态：pendingPay - 待支付 paid-已支付 refunded-已退款' DEFAULT 'pendingPay'", undefined);
   }

   public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query("ALTER TABLE `wish_box_activity_buy` CHANGE `payStatus` `payStatus` varchar(32) NOT NULL COMMENT '支付状态：pendingPay - 待支付 paid-已支付' DEFAULT 'pendingPay'", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_buy` DROP COLUMN `refundReason`", undefined);
   }

}
