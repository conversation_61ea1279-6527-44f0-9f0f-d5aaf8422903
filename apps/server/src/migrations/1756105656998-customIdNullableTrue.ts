import {MigrationInterface, QueryRunner} from "typeorm";

export class customIdNullableTrue1756105656998 implements MigrationInterface {

   public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query("ALTER TABLE `subscription_message_record` DROP FOREIGN KEY `FK_8a5521594f5393de5534456768c`", undefined);
        await queryRunner.query("DROP INDEX `UNIQUE_USER_PER_ORG` ON `subscription_message_record`", undefined);
        await queryRunner.query("ALTER TABLE `subscription_message_record` CHANGE `customerId` `customerId` int NULL COMMENT '用户id'", undefined);
        await queryRunner.query("CREATE UNIQUE INDEX `UNIQUE_USER_PER_ORG` ON `subscription_message_record` (`targetType`, `targetId`, `customerId`)", undefined);
        await queryRunner.query("CREATE INDEX `IDX_8a5521594f5393de5534456768` ON `subscription_message_record` (`customerId`)", undefined);
        await queryRunner.query("ALTER TABLE `subscription_message_record` ADD CONSTRAINT `FK_8a5521594f5393de5534456768c` FOREIGN KEY (`customerId`) REFERENCES `customer`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
   }

   public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query("DROP INDEX `IDX_8a5521594f5393de5534456768` ON `subscription_message_record`", undefined);
        await queryRunner.query("ALTER TABLE `subscription_message_record` DROP FOREIGN KEY `FK_8a5521594f5393de5534456768c`", undefined);
        await queryRunner.query("DROP INDEX `UNIQUE_USER_PER_ORG` ON `subscription_message_record`", undefined);
        await queryRunner.query("ALTER TABLE `subscription_message_record` CHANGE `customerId` `customerId` int NOT NULL COMMENT '用户id'", undefined);
        await queryRunner.query("CREATE UNIQUE INDEX `UNIQUE_USER_PER_ORG` ON `subscription_message_record` (`targetType`, `targetId`, `customerId`)", undefined);
        await queryRunner.query("ALTER TABLE `subscription_message_record` ADD CONSTRAINT `FK_8a5521594f5393de5534456768c` FOREIGN KEY (`customerId`) REFERENCES `customer`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
   }

}
