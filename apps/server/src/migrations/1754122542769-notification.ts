import {MigrationInterface, QueryRunner} from "typeorm";

export class notification1754122542769 implements MigrationInterface {

   public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query("CREATE TABLE `forum_audit_notification` (`createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), `forumCustomerId` int NOT NULL COMMENT '回复的用户id', `auditReviewRefuse` varchar(255) NULL COMMENT '评论审核不通过', `id` int NOT NULL AUTO_INCREMENT, `forumReviewId` int NULL, `forumPostId` int NULL, `channelId` int NULL, PRIMARY KEY (`id`)) ENGINE=InnoDB", undefined);
        await queryRunner.query("ALTER TABLE `forum_audit_notification` ADD CONSTRAINT `FK_c6a8cd4908d6ca84cecae52607b` FOREIGN KEY (`forumReviewId`) REFERENCES `forum_review`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `forum_audit_notification` ADD CONSTRAINT `FK_e8ebfa3ef58644f1c1864e5b413` FOREIGN KEY (`forumPostId`) REFERENCES `forum_post`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `forum_audit_notification` ADD CONSTRAINT `FK_0e3f0151f685eb914a164dade90` FOREIGN KEY (`channelId`) REFERENCES `channel`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
   }

   public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query("ALTER TABLE `forum_audit_notification` DROP FOREIGN KEY `FK_0e3f0151f685eb914a164dade90`", undefined);
        await queryRunner.query("ALTER TABLE `forum_audit_notification` DROP FOREIGN KEY `FK_e8ebfa3ef58644f1c1864e5b413`", undefined);
        await queryRunner.query("ALTER TABLE `forum_audit_notification` DROP FOREIGN KEY `FK_c6a8cd4908d6ca84cecae52607b`", undefined);
        await queryRunner.query("DROP TABLE `forum_audit_notification`", undefined);
   }

}
