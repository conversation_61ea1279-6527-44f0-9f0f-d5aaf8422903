import {MigrationInterface, QueryRunner} from "typeorm";

export class wishBox1755486589673 implements MigrationInterface {

   public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query("CREATE TABLE `wish_box_activity_open_strategy` (`createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), `activityId` int NOT NULL COMMENT '心愿盒子活动id', `count` int NOT NULL COMMENT '抽取次数', `price` int NOT NULL COMMENT '价格，分', `wishProbability` float NOT NULL COMMENT '心愿商品概率（总概率需为100%）', `baseProbability` float NOT NULL COMMENT '基础商品概率（总概率需为100%）', `status` varchar(16) NOT NULL COMMENT '状态：normal-正常' DEFAULT 'normal', `sort` int NOT NULL COMMENT '排序号', `channelId` int NOT NULL COMMENT '渠道id', `id` int NOT NULL AUTO_INCREMENT, INDEX `IDX_6dc774c7d8e5071d0189e3b938` (`activityId`), PRIMARY KEY (`id`)) ENGINE=InnoDB", undefined);
        await queryRunner.query("CREATE TABLE `wish_box_activity_prize` (`createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), `activityId` int NOT NULL COMMENT '心愿盒子活动id', `boxType` varchar(16) NOT NULL COMMENT '盒子类型：free-免费 normal-普通', `probability` float NULL COMMENT '抽中概率：同类型盒子叠加，空时均分剩余概率，不满100%会出空奖，超出100按比例计算', `type` varchar(16) NOT NULL COMMENT '奖品类型：product-产品' DEFAULT 'product', `targetId` int NULL COMMENT '奖品id', `status` varchar(16) NOT NULL DEFAULT 'normal', `channelId` int NOT NULL COMMENT '渠道id', `id` int NOT NULL AUTO_INCREMENT, INDEX `IDX_ff8f3faded56a43786057e05f9` (`activityId`), PRIMARY KEY (`id`)) ENGINE=InnoDB", undefined);
        await queryRunner.query("CREATE TABLE `wish_box_activity` (`createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), `name` varchar(255) NOT NULL COMMENT '名称', `remarks` varchar(255) NOT NULL COMMENT '备注', `startAt` datetime NOT NULL COMMENT '开始时间', `endAt` datetime NOT NULL COMMENT '结束时间', `period` int NOT NULL COMMENT '普通盒子循环周期' DEFAULT '0', `periodUnit` varchar(255) NOT NULL COMMENT '普通盒子循环周期单位' DEFAULT 'day', `periodPerLimit` int NOT NULL COMMENT '普通盒子周期内用户次数限制' DEFAULT '0', `freeBoxOpen` tinyint NOT NULL COMMENT '启动免费盒子' DEFAULT 0, `freeBoxPeriod` int NOT NULL COMMENT '免费盒子循环周期' DEFAULT '0', `freeBoxPeriodUnit` varchar(255) NOT NULL COMMENT '免费盒子循环周期单位' DEFAULT 'day', `freeBoxPeriodPerLimit` int NOT NULL COMMENT '免费盒子周期内用户抽取次数限制' DEFAULT '0', `freeBoxPerWinLimit` int NOT NULL COMMENT '免费盒子用户中奖次数限制，-1为不限制' DEFAULT '0', `freeBoxPrizeValidityMode` varchar(255) NOT NULL COMMENT '免费盒子奖品有效模式' DEFAULT 'relative', `freeBoxPrizeValidityDay` int NOT NULL COMMENT '免费盒子奖品有效天数' DEFAULT '0', `freeBoxPrizeValidityStartAt` datetime NULL COMMENT '免费盒子奖品有效开始时间', `freeBoxPrizeValidityExpireAt` datetime NULL COMMENT '免费盒子奖品有效结束时间', `rules` text NOT NULL COMMENT '规则', `status` varchar(255) NOT NULL COMMENT '活动状态' DEFAULT 'draft', `channelId` int NOT NULL COMMENT '渠道id', `id` int NOT NULL AUTO_INCREMENT, INDEX `IDX_a54c6e750f5bfd8fde93c1e2ed` (`startAt`), INDEX `IDX_21a91298e9d71f7655ee2924d9` (`endAt`), PRIMARY KEY (`id`)) ENGINE=InnoDB", undefined);
        await queryRunner.query("CREATE TABLE `wish_box_activity_pick` (`createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), `activityId` int NOT NULL COMMENT '心愿盒子活动id', `customerId` int NOT NULL COMMENT '用户id', `activityPrizeId` int NOT NULL COMMENT '心愿盒子活动奖品id', `channelId` int NOT NULL COMMENT '渠道id', `id` int NOT NULL AUTO_INCREMENT, INDEX `IDX_caaf6370264df68ab342bb8184` (`activityId`), INDEX `IDX_302298e7f34dda87f51b5d6556` (`customerId`), INDEX `IDX_f21199e417c57e1133326836ca` (`activityPrizeId`), PRIMARY KEY (`id`)) ENGINE=InnoDB", undefined);
        await queryRunner.query("CREATE TABLE `wish_box_activity_record_item` (`createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), `recordId` int NOT NULL COMMENT '心愿盒子活动记录id', `activityPrizeId` int NOT NULL COMMENT '心愿盒子活动奖品id', `type` varchar(32) NOT NULL COMMENT '奖品类型：product-产品', `targetId` int NULL COMMENT '奖品id', `channelId` int NOT NULL COMMENT '渠道id', `id` int NOT NULL AUTO_INCREMENT, INDEX `IDX_da503a34a5e37eed055be98518` (`recordId`), INDEX `IDX_a569ceba44ac5055cf981dc40f` (`activityPrizeId`), PRIMARY KEY (`id`)) ENGINE=InnoDB", undefined);
        await queryRunner.query("CREATE TABLE `wish_box_activity_record` (`createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), `activityId` int NOT NULL COMMENT '心愿盒子活动id', `customerId` int NOT NULL COMMENT '用户id', `activityBuyId` int NULL COMMENT '购买记录关联id', `orderId` int NULL COMMENT '订单id', `boxType` varchar(16) NOT NULL COMMENT '盒子类型：free-免费 normal-普通', `isEmpty` tinyint NOT NULL COMMENT '是否空奖', `code` varchar(64) NOT NULL COMMENT '开盒子编号', `startAt` datetime NULL COMMENT '开始时间，空为不限制', `expireAt` datetime NULL COMMENT '过期时间，空为不限制', `status` varchar(32) NOT NULL COMMENT '状态：pendingDelivery - 待提货  delivered-已提货' DEFAULT 'pendingDelivery', `inShoppingCart` tinyint NOT NULL COMMENT '是否在购物车中' DEFAULT 0, `channelId` int NOT NULL COMMENT '渠道id', `id` int NOT NULL AUTO_INCREMENT, INDEX `IDX_0fd4714e91fa754d778e2b58cc` (`activityId`), INDEX `IDX_f53e79b83af84fdcb6799e667b` (`customerId`), INDEX `IDX_ac02ab3c017cb827bffb63a343` (`activityBuyId`), INDEX `IDX_6efac08b9219bc2afbb1e7d3f0` (`code`), INDEX `IDX_242394a737a8bd111a08f48211` (`startAt`), INDEX `IDX_6405fd48c6a5e2124d09a08903` (`expireAt`), PRIMARY KEY (`id`)) ENGINE=InnoDB", undefined);
        await queryRunner.query("CREATE TABLE `wish_box_activity_buy` (`createdAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), `updatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), `activityId` int NOT NULL COMMENT '心愿盒子活动id', `customerId` int NOT NULL COMMENT '用户id', `activityOpenStrategyId` int NOT NULL COMMENT '开启策略id', `price` int NOT NULL COMMENT '价格，单位分', `code` varchar(64) NOT NULL COMMENT '订单号', `paymentMetadata` text NULL COMMENT '支付数据', `paymentMethod` varchar(255) NULL COMMENT '支付方式', `paymentAt` datetime NULL COMMENT '支付时间', `status` varchar(32) NOT NULL COMMENT '状态： pendingOpen-等待开启 opened-已开启' DEFAULT 'pendingOpen', `payStatus` varchar(32) NOT NULL COMMENT '支付状态：pendingPay - 待支付 paid-已支付' DEFAULT 'pendingPay', `channelId` int NOT NULL COMMENT '渠道id', `id` int NOT NULL AUTO_INCREMENT, INDEX `IDX_8d2cc8223ce632c5708c4cfcd3` (`activityId`), INDEX `IDX_5191460b21cc1c7e3ac1a4418f` (`customerId`), INDEX `IDX_ce25ea83e373110e50ecef813e` (`activityOpenStrategyId`), INDEX `IDX_3689d35846d8272dfd477d4212` (`code`), INDEX `IDX_162a0798ade441756967cce176` (`paymentAt`), PRIMARY KEY (`id`)) ENGINE=InnoDB", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_open_strategy` ADD CONSTRAINT `FK_6dc774c7d8e5071d0189e3b9384` FOREIGN KEY (`activityId`) REFERENCES `wish_box_activity`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_prize` ADD CONSTRAINT `FK_ff8f3faded56a43786057e05f9d` FOREIGN KEY (`activityId`) REFERENCES `wish_box_activity`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_prize` ADD CONSTRAINT `FK_ff509e0d2f3b49ad1b9d9ca2911` FOREIGN KEY (`targetId`) REFERENCES `product_variant`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_pick` ADD CONSTRAINT `FK_caaf6370264df68ab342bb81846` FOREIGN KEY (`activityId`) REFERENCES `wish_box_activity`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_pick` ADD CONSTRAINT `FK_302298e7f34dda87f51b5d6556c` FOREIGN KEY (`customerId`) REFERENCES `customer`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_pick` ADD CONSTRAINT `FK_f21199e417c57e1133326836ca3` FOREIGN KEY (`activityPrizeId`) REFERENCES `wish_box_activity_prize`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_record_item` ADD CONSTRAINT `FK_da503a34a5e37eed055be98518e` FOREIGN KEY (`recordId`) REFERENCES `wish_box_activity_record`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_record_item` ADD CONSTRAINT `FK_a569ceba44ac5055cf981dc40f5` FOREIGN KEY (`activityPrizeId`) REFERENCES `wish_box_activity_prize`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_record_item` ADD CONSTRAINT `FK_cb825785bb6d9ce9d9b55e62160` FOREIGN KEY (`targetId`) REFERENCES `product_variant`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_record` ADD CONSTRAINT `FK_0fd4714e91fa754d778e2b58cc1` FOREIGN KEY (`activityId`) REFERENCES `wish_box_activity`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_record` ADD CONSTRAINT `FK_f53e79b83af84fdcb6799e667b0` FOREIGN KEY (`customerId`) REFERENCES `customer`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_record` ADD CONSTRAINT `FK_ac02ab3c017cb827bffb63a343b` FOREIGN KEY (`activityBuyId`) REFERENCES `wish_box_activity_buy`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_record` ADD CONSTRAINT `FK_8c166f009e48d06a880d91150a4` FOREIGN KEY (`orderId`) REFERENCES `order`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_buy` ADD CONSTRAINT `FK_8d2cc8223ce632c5708c4cfcd36` FOREIGN KEY (`activityId`) REFERENCES `wish_box_activity`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_buy` ADD CONSTRAINT `FK_5191460b21cc1c7e3ac1a4418f5` FOREIGN KEY (`customerId`) REFERENCES `customer`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_buy` ADD CONSTRAINT `FK_ce25ea83e373110e50ecef813ef` FOREIGN KEY (`activityOpenStrategyId`) REFERENCES `wish_box_activity_open_strategy`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION", undefined);
   }

   public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query("ALTER TABLE `wish_box_activity_buy` DROP FOREIGN KEY `FK_ce25ea83e373110e50ecef813ef`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_buy` DROP FOREIGN KEY `FK_5191460b21cc1c7e3ac1a4418f5`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_buy` DROP FOREIGN KEY `FK_8d2cc8223ce632c5708c4cfcd36`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_record` DROP FOREIGN KEY `FK_8c166f009e48d06a880d91150a4`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_record` DROP FOREIGN KEY `FK_ac02ab3c017cb827bffb63a343b`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_record` DROP FOREIGN KEY `FK_f53e79b83af84fdcb6799e667b0`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_record` DROP FOREIGN KEY `FK_0fd4714e91fa754d778e2b58cc1`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_record_item` DROP FOREIGN KEY `FK_cb825785bb6d9ce9d9b55e62160`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_record_item` DROP FOREIGN KEY `FK_a569ceba44ac5055cf981dc40f5`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_record_item` DROP FOREIGN KEY `FK_da503a34a5e37eed055be98518e`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_pick` DROP FOREIGN KEY `FK_f21199e417c57e1133326836ca3`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_pick` DROP FOREIGN KEY `FK_302298e7f34dda87f51b5d6556c`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_pick` DROP FOREIGN KEY `FK_caaf6370264df68ab342bb81846`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_prize` DROP FOREIGN KEY `FK_ff509e0d2f3b49ad1b9d9ca2911`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_prize` DROP FOREIGN KEY `FK_ff8f3faded56a43786057e05f9d`", undefined);
        await queryRunner.query("ALTER TABLE `wish_box_activity_open_strategy` DROP FOREIGN KEY `FK_6dc774c7d8e5071d0189e3b9384`", undefined);
        await queryRunner.query("DROP INDEX `IDX_162a0798ade441756967cce176` ON `wish_box_activity_buy`", undefined);
        await queryRunner.query("DROP INDEX `IDX_3689d35846d8272dfd477d4212` ON `wish_box_activity_buy`", undefined);
        await queryRunner.query("DROP INDEX `IDX_ce25ea83e373110e50ecef813e` ON `wish_box_activity_buy`", undefined);
        await queryRunner.query("DROP INDEX `IDX_5191460b21cc1c7e3ac1a4418f` ON `wish_box_activity_buy`", undefined);
        await queryRunner.query("DROP INDEX `IDX_8d2cc8223ce632c5708c4cfcd3` ON `wish_box_activity_buy`", undefined);
        await queryRunner.query("DROP TABLE `wish_box_activity_buy`", undefined);
        await queryRunner.query("DROP INDEX `IDX_6405fd48c6a5e2124d09a08903` ON `wish_box_activity_record`", undefined);
        await queryRunner.query("DROP INDEX `IDX_242394a737a8bd111a08f48211` ON `wish_box_activity_record`", undefined);
        await queryRunner.query("DROP INDEX `IDX_6efac08b9219bc2afbb1e7d3f0` ON `wish_box_activity_record`", undefined);
        await queryRunner.query("DROP INDEX `IDX_ac02ab3c017cb827bffb63a343` ON `wish_box_activity_record`", undefined);
        await queryRunner.query("DROP INDEX `IDX_f53e79b83af84fdcb6799e667b` ON `wish_box_activity_record`", undefined);
        await queryRunner.query("DROP INDEX `IDX_0fd4714e91fa754d778e2b58cc` ON `wish_box_activity_record`", undefined);
        await queryRunner.query("DROP TABLE `wish_box_activity_record`", undefined);
        await queryRunner.query("DROP INDEX `IDX_a569ceba44ac5055cf981dc40f` ON `wish_box_activity_record_item`", undefined);
        await queryRunner.query("DROP INDEX `IDX_da503a34a5e37eed055be98518` ON `wish_box_activity_record_item`", undefined);
        await queryRunner.query("DROP TABLE `wish_box_activity_record_item`", undefined);
        await queryRunner.query("DROP INDEX `IDX_f21199e417c57e1133326836ca` ON `wish_box_activity_pick`", undefined);
        await queryRunner.query("DROP INDEX `IDX_302298e7f34dda87f51b5d6556` ON `wish_box_activity_pick`", undefined);
        await queryRunner.query("DROP INDEX `IDX_caaf6370264df68ab342bb8184` ON `wish_box_activity_pick`", undefined);
        await queryRunner.query("DROP TABLE `wish_box_activity_pick`", undefined);
        await queryRunner.query("DROP INDEX `IDX_21a91298e9d71f7655ee2924d9` ON `wish_box_activity`", undefined);
        await queryRunner.query("DROP INDEX `IDX_a54c6e750f5bfd8fde93c1e2ed` ON `wish_box_activity`", undefined);
        await queryRunner.query("DROP TABLE `wish_box_activity`", undefined);
        await queryRunner.query("DROP INDEX `IDX_ff8f3faded56a43786057e05f9` ON `wish_box_activity_prize`", undefined);
        await queryRunner.query("DROP TABLE `wish_box_activity_prize`", undefined);
        await queryRunner.query("DROP INDEX `IDX_6dc774c7d8e5071d0189e3b938` ON `wish_box_activity_open_strategy`", undefined);
        await queryRunner.query("DROP TABLE `wish_box_activity_open_strategy`", undefined);
   }

}
